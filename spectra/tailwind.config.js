module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html",
    "./.storybook/**/*.{js,jsx,ts,tsx}",
  ],
  darkMode: false, // or 'media' or 'class'
  theme: {
    extend: {
      margin: {
        320: "320px",
      },
      colors: {
        blue10: "#0A121F",
        blue20: "#15253E",
        blue30: "#1F375D",
        blue40: "#2A447C",
        blue50: "#345C9B",
        blue60: "#5D7DAF",
        blue70: "#859DC3",
        blue80: "#AEBED7",
        blue90: "#D6DEEB",
        blue95: "#ECF0F5",
        blue97: "#F5F7FA",

        space10: "#06090D",
        space20: "#0B131A",
        space30: "#111C26",
        space40: "#162633",
        space50: "#1C2F40",
        space60: "#495966",
        space70: "#77828C",
        space80: "#A4ACB3",
        space90: "#E8EAEC",

        green10: "#162014",
        green20: "#2C4029",
        green30: "#425F3D",
        green40: "#587F52",
        green50: "#6E9F66",
        green60: "#8BB285",
        green70: "#A8C5A3",
        green80: "#C5D9C2",
        green90: "#F0F5EF",

        yellow10: "#2F230F",
        yellow20: "#5D461D",
        yellow30: "#8C6A2C",
        yellow40: "#BA8D3A",
        yellow50: "#E9B049",
        yellow60: "#EDC06D",
        yellow70: "#F2D092",
        yellow80: "#F6DFB6",
        yellow90: "#FDF7EC",

        red10: "#271211",
        red20: "#4F2423",
        red30: "#763734",
        red40: "#9E4946",
        red50: "#C55B57",
        red60: "#D17C79",
        red70: "#DC9D9A",
        red80: "#E8BDBC",
        red90: "#F9EEEE",

        gray10: "#08090A",
        gray20: "#111214",
        gray30: "#191C1E",
        gray40: "#222528",
        gray50: "#2A2E32",
        gray60: "#55585B",
        gray70: "#7F8284",
        gray80: "#AAABAD",
        gray90: "#D4D5D6",
        gray95: "#EAEAEB",
        gray97: "#F5F5F5",

        db: "#2A2F3A",
        dlb: "#3a4d66",
        lb: "#005BAB",
        blueg: "#00876a",
        chDisp: "#5EA15F",
        chRem: "#ADCFAD",
        chPos: "#F1F4F6",
        chSite: "#8884D8",
        batSite: "#82CA9D",
      },
      width: {
        25: "25px",
        30: "30px",
        45: "45px",
        60: "60px",
        100: "100px",
        120: "120px",
        150: "150px",
        190: "190px",
        250: "250px",
        275: "275px",
        300: "300px",
        340: "340px",
        350: "350px",
        600: "600px",
        656: "656px",
        800: "800px",
        880: "880px",
        508: "508px",
      },
      height: {
        30: "30px",
        80: "80px",
        100: "100px",
        150: "150px",
        190: "190px",
        250: "250px",
        340: "340px",
        370: "370px",
        420: "420px",
        510: "510px",
        600: "600px",
        685: "685px",
        800: "800px",
        "90vh": "90vh",
      },
      flex: {
        0.7: "0.7 1 0%",
      },
      maxWidth: {
        100: "100px",
        150: "150px",
        175: "175px",
        225: "225px",
        300: "300px",
        370: "370px",
        600: "600px",
      },
      maxHeight: {
        100: "100px",
        150: "150px",
        175: "175px",
        225: "225px",
        275: "275px",
        350: "350px",
        370: "370px",
      },
      minWidth: {
        100: "100px",
        70: "70px",
        200: "200px",
        210: "210px",
        225: "225px",
        180: "180px",
        190: "190px",
        250: "250px",
        300: "300px",
        350: "350px",
        500: "500px",
        620: "620px",
      },
      minHeight: {
        75: "75px",
        150: "150px",
        175: "175px",
        200: "200px",
        275: "275px",
        370: "370px",
      },
      textColor: {
        lightGray: "#F1EFEE",
        primary: "#FAFAFA",
        secColor: "#efefef",
        navColor: "#BEBEBE",
      },
      backgroundColor: {
        mainColor: "#FBF8F9",
        secondaryColor: "#F0F0F0",
        blackOverlay: "rgba(0, 0 ,0 ,0.7)",
      },
      keyframes: {
        "slide-in": {
          "0%": {
            "-webkit-transform": "translateX(-200px)",
            transform: "translateX(-200px)",
          },
          "100%": {
            "-webkit-transform": "translateX(0px)",
            transform: "translateX(0px)",
          },
        },

        "slide-fwd": {
          "0%": {
            "-webkit-transform": "translateZ(0px)",
            transform: "translateZ(0px)",
          },
          "100%": {
            "-webkit-transform": "translateZ(160px)",
            transform: "translateZ(160px)",
          },
        },
      },
      animation: {
        "slide-in": "slide-in 0.5s ease-out",
        "slide-fwd":
          " slide-fwd 0.45s cubic-bezier(0.250, 0.460, 0.450, 0.940) both",
      },
      transitionProperty: {
        height: "height",
      },
    },
    cursor: {
      "zoom-in": "zoom-in",
      pointer: "pointer",
    },
  },
  variants: {
    // backgroundColor: ['active'],
    extend: {},
  },
  plugins: [require("@tailwindcss/container-queries")],
};
