/**
 * Demo component showing DeviceTile error integration
 * This demonstrates the before/after transformation
 */

import { useEffect, useState } from "react";
import { ErrorProvider } from "../components/uikit/ErrorProvider/ErrorProvider";
import { ErrorPatterns } from "../errorManagement";

// Mock API function that can succeed or fail
const mockGetSummaryForThing = async (shouldFail = false) => {
  await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate network delay

  if (shouldFail) {
    throw new Error("Network error: Unable to fetch device statistics");
  }

  return [
    { type: "power", value: 100, unit: "kW" },
    { type: "energy", value: 250, unit: "kWh" },
    { type: "efficiency", value: 85, unit: "%" },
  ];
};

// Component using OLD error handling approach
const DeviceTileOldApproach: React.FC<{ shouldFail: boolean }> = ({
  shouldFail,
}) => {
  type Stat = { type: string; value: number; unit: string };
  const [stats, setStats] = useState<Stat[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await mockGetSummaryForThing(shouldFail);
        setStats(response);
      } catch (error) {
        console.error("Unable to fetch stats for device", error);
        setError("Failed to load data");
        setStats([]);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [shouldFail]);

  return (
    <div className="p-4 border border-gray-300 rounded-lg">
      <h3 className="font-semibold text-red-600 mb-2">
        ❌ OLD Approach (Manual Error Handling)
      </h3>
      <div className="text-sm text-gray-600 mb-3">
        Device: Test Charger #123
      </div>

      {loading && <div className="text-blue-600">Loading...</div>}

      {error && (
        <div className="text-red-600 text-sm mb-2">
          ⚠️ {error} (Only visible in console)
        </div>
      )}

      {!loading && stats.length > 0 && (
        <div className="space-y-1">
          {stats.map((stat) => (
            <div key={stat.type} className="text-sm">
              {stat.type}: {stat.value} {stat.unit}
            </div>
          ))}
        </div>
      )}

      {!loading && stats.length === 0 && !error && (
        <div className="text-gray-500 text-sm">No data available</div>
      )}

      <div className="mt-3 text-xs text-gray-500">
        Problems: Silent failures, poor UX, inconsistent error handling
      </div>
    </div>
  );
};

// Component using NEW centralized error handling
const DeviceTileNewApproach: React.FC<{ shouldFail: boolean }> = ({
  shouldFail,
}) => {
  type Stat = { type: string; value: number; unit: string };
  const [stats, setStats] = useState<Stat[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);

      // Use centralized error handling
      const result = await ErrorPatterns.withErrorHandling(async () => {
        const response = await mockGetSummaryForThing(shouldFail);
        return response;
      }, "Failed to load device statistics for Test Charger #123. Please try refreshing the page or check your connection.");

      // Handle the response - result will be null if there was an error
      if (result) {
        setStats(result);
      } else {
        // Error was already handled by the centralized system
        setStats([]);
      }

      setLoading(false);
    };

    fetchStats();
  }, [shouldFail]);

  return (
    <div className="p-4 border border-green-300 rounded-lg">
      <h3 className="font-semibold text-green-600 mb-2">
        ✅ NEW Approach (Centralized Error Handling)
      </h3>
      <div className="text-sm text-gray-600 mb-3">
        Device: Test Charger #123
      </div>

      {loading && <div className="text-blue-600">Loading...</div>}

      {!loading && stats.length > 0 && (
        <div className="space-y-1">
          {stats.map((stat) => (
            <div key={stat.type} className="text-sm">
              {stat.type}: {stat.value} {stat.unit}
            </div>
          ))}
        </div>
      )}

      {!loading && stats.length === 0 && (
        <div className="text-gray-500 text-sm">No data available</div>
      )}

      <div className="mt-3 text-xs text-gray-500">
        Benefits: User-friendly notifications, consistent handling, graceful
        degradation
      </div>
    </div>
  );
};

// Main demo component
export const DeviceTileErrorIntegrationDemo: React.FC = () => {
  const [shouldFail, setShouldFail] = useState(false);

  return (
    <ErrorProvider toastPosition="top-right" maxToasts={5}>
      <div className="p-6 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4">
          DeviceTile Error Integration Demo
        </h1>

        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h2 className="font-semibold mb-2">Demo Controls</h2>
          <p className="text-sm text-gray-600 mb-3">
            Toggle between success and failure scenarios to see how each
            approach handles errors.
          </p>

          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => setShouldFail(false)}
              className={`px-4 py-2 rounded ${
                !shouldFail
                  ? "bg-green-600 text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              ✅ Success Scenario
            </button>
            <button
              type="button"
              onClick={() => setShouldFail(true)}
              className={`px-4 py-2 rounded ${
                shouldFail
                  ? "bg-red-600 text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              ❌ Failure Scenario
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <DeviceTileOldApproach shouldFail={shouldFail} />
          <DeviceTileNewApproach shouldFail={shouldFail} />
        </div>

        <div className="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h2 className="font-semibold mb-3">Key Differences</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-medium text-red-600 mb-2">
                ❌ Old Approach Problems:
              </h3>
              <ul className="space-y-1 text-gray-600">
                <li>• Errors only in console (users don't see them)</li>
                <li>• Inconsistent error handling across components</li>
                <li>• Manual try-catch boilerplate everywhere</li>
                <li>• Poor user experience during failures</li>
                <li>• Silent failures confuse users</li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium text-green-600 mb-2">
                ✅ New Approach Benefits:
              </h3>
              <ul className="space-y-1 text-gray-600">
                <li>• User-friendly error notifications</li>
                <li>• Consistent error handling pattern</li>
                <li>• Centralized error tracking</li>
                <li>• Graceful degradation</li>
                <li>• Professional user experience</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h2 className="font-semibold mb-2">Try It Out</h2>
          <p className="text-sm text-gray-600">
            1. Click "Failure Scenario" to see how errors are handled
            <br />
            2. Notice the ErrorToast notification that appears (top-right)
            <br />
            3. Compare the user experience between old and new approaches
            <br />
            4. Switch back to "Success Scenario" to see normal operation
          </p>
        </div>
      </div>
    </ErrorProvider>
  );
};
