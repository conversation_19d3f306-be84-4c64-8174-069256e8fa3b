/**
 * Storybook stories for DeviceTile Error Integration Demo
 */

import type { <PERSON>a, StoryObj } from "@storybook/react";
import { DeviceTileErrorIntegrationDemo } from "./DeviceTileErrorIntegrationDemo";

const meta: Meta<typeof DeviceTileErrorIntegrationDemo> = {
  title: "Examples/DeviceTile Error Integration",
  component: DeviceTileErrorIntegrationDemo,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
# DeviceTile Error Integration Demo

This demo shows the transformation of API error handling from manual try-catch patterns to centralized error management.

## What This Demonstrates

### Before (Old Approach)
- Manual try-catch in every component
- Console-only error logging
- Poor user experience during failures
- Inconsistent error handling patterns

### After (New Approach)
- Centralized error handling with \`ErrorPatterns.withErrorHandling\`
- User-friendly error notifications via ErrorToast
- Consistent error handling across the application
- Graceful degradation with fallback UI

## How to Use

1. **Success Scenario**: Click to see normal operation
2. **Failure Scenario**: Click to trigger API errors and see how they're handled
3. **Compare**: Notice the difference in user experience between approaches

## Key Integration Points

### Import Error Management
\`\`\`typescript
import { ErrorPatterns } from "../../errorManagement";
\`\`\`

### Wrap API Calls
\`\`\`typescript
const result = await ErrorPatterns.withErrorHandling(
  async () => {
    const response = await getSummaryForThing(/* params */);
    return datapointsToMap(response);
  },
  "Failed to load device statistics for {deviceName}. Please try refreshing the page."
);
\`\`\`

### Handle Results
\`\`\`typescript
if (result) {
  setStats(result); // Success
} else {
  setStats({}); // Error already shown to user
}
\`\`\`

This pattern can be applied to any component with API calls for consistent, user-friendly error handling.
        `,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof DeviceTileErrorIntegrationDemo>;

export const Default: Story = {
  render: () => <DeviceTileErrorIntegrationDemo />,
};

export const InteractiveDemo: Story = {
  render: () => <DeviceTileErrorIntegrationDemo />,
  parameters: {
    docs: {
      description: {
        story: `
### Interactive Demo

Use the controls to switch between success and failure scenarios:

- **Success Scenario**: Shows normal API operation with data loading
- **Failure Scenario**: Triggers API errors to demonstrate error handling

Watch for ErrorToast notifications in the top-right corner when failures occur.
        `,
      },
    },
  },
};
