/**
 * Examples demonstrating how to use the centralized error management system
 */

import { useState } from "react";
import { useErrorActions, useErrors } from "../stores/errorStore";
import { useApiErrorHandler } from "../utils/apiErrorHandler";
import {
  createError,
  createNetworkError,
  createValidationError,
} from "../utils/errorUtils";

/**
 * Example 1: Basic error handling in a component
 */
export const BasicErrorExample: React.FC = () => {
  const { showError, showNetworkError, showValidationError } =
    useErrorActions();
  const [loading, setLoading] = useState(false);

  const handleShowError = () => {
    showError("Example Error", "This is an example error message.", "client", {
      severity: "medium",
    });
  };

  const handleNetworkError = () => {
    const mockResponse = new Response(null, {
      status: 500,
      statusText: "Internal Server Error",
    });
    showNetworkError(mockResponse, "/api/example", "GET");
  };

  const handleValidationError = () => {
    showValidationError("Form validation failed", "email", {
      email: ["Email is required", "Email format is invalid"],
      password: ["Password must be at least 8 characters"],
    });
  };

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Basic Error Examples</h3>
      <div className="space-y-2">
        <button
          type="button"
          onClick={handleShowError}
          className="block w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Show Generic Error
        </button>
        <button
          type="button"
          onClick={handleNetworkError}
          className="block w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Show Network Error
        </button>
        <button
          type="button"
          onClick={handleValidationError}
          className="block w-full px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
        >
          Show Validation Error
        </button>
      </div>
    </div>
  );
};

/**
 * Example 2: API call with error handling
 */
export const ApiErrorExample: React.FC = () => {
  const { withErrorHandling } = useApiErrorHandler();
  const [data, setData] = useState<unknown>(null);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);

    const result = await withErrorHandling(
      async () => {
        // Simulate API call that might fail
        const response = await fetch("/api/data");
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      },
      {
        operationName: "Fetch data",
        customErrorMessage: "Failed to load data. Please try again.",
      },
    );

    if (result) {
      setData(result);
    }

    setLoading(false);
  };

  const simulateError = async () => {
    setLoading(true);

    await withErrorHandling(
      async () => {
        // Simulate a failing API call
        throw new Error("Simulated API error");
      },
      {
        operationName: "Simulate error",
        customErrorMessage: "This is a simulated error for demonstration.",
      },
    );

    setLoading(false);
  };

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">API Error Handling Example</h3>
      <div className="space-y-2">
        <button
          type="button"
          onClick={fetchData}
          disabled={loading}
          className="block w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? "Loading..." : "Fetch Data (Success)"}
        </button>
        <button
          type="button"
          onClick={simulateError}
          disabled={loading}
          className="block w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
        >
          {loading ? "Loading..." : "Simulate API Error"}
        </button>
      </div>
      {data && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
          <p className="text-green-800">Data loaded successfully!</p>
        </div>
      )}
    </div>
  );
};

/**
 * Example 3: Form with validation error handling
 */
export const FormErrorExample: React.FC = () => {
  const { showValidationError, clearErrorsByCategory } = useErrorActions();
  const [formData, setFormData] = useState({ email: "", password: "" });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous validation errors
    clearErrorsByCategory("validation");

    const errors: Record<string, string[]> = {};

    if (!formData.email) {
      errors.email = ["Email is required"];
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = ["Email format is invalid"];
    }

    if (!formData.password) {
      errors.password = ["Password is required"];
    } else if (formData.password.length < 8) {
      errors.password = ["Password must be at least 8 characters"];
    }

    if (Object.keys(errors).length > 0) {
      showValidationError(
        "Please fix the following errors:",
        undefined,
        errors,
      );
      return;
    }

    // Form is valid, proceed with submission
    alert("Form submitted successfully!");
  };

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Form Validation Example</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Email
          </label>
          <input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your email"
          />
        </div>
        <div>
          <label
            htmlFor="password"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Password
          </label>
          <input
            id="password"
            type="password"
            value={formData.password}
            onChange={(e) =>
              setFormData({ ...formData, password: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your password"
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Submit Form
        </button>
      </form>
    </div>
  );
};

/**
 * Example 4: Error management dashboard
 */
export const ErrorDashboard: React.FC = () => {
  const errors = useErrors();
  const { clearErrors, clearErrorsByCategory, removeError } = useErrorActions();

  const errorsByCategory = errors.reduce(
    (acc, error) => {
      acc[error.category] = (acc[error.category] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Error Management Dashboard</h3>

      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-2">
          Total Errors: {errors.length}
        </p>

        {Object.entries(errorsByCategory).map(([category, count]) => (
          <div
            key={category}
            className="flex justify-between items-center py-1"
          >
            <span className="text-sm capitalize">{category}:</span>
            <span className="text-sm font-medium">{count}</span>
          </div>
        ))}
      </div>

      <div className="space-y-2">
        <button
          type="button"
          onClick={clearErrors}
          className="block w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          Clear All Errors
        </button>
        <button
          type="button"
          onClick={() => clearErrorsByCategory("validation")}
          className="block w-full px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
        >
          Clear Validation Errors
        </button>
        <button
          type="button"
          onClick={() => clearErrorsByCategory("network")}
          className="block w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Clear Network Errors
        </button>
      </div>

      {errors.length > 0 && (
        <div className="mt-4">
          <h4 className="text-md font-medium mb-2">Recent Errors:</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {errors.slice(0, 5).map((error) => (
              <div
                key={error.id}
                className="flex justify-between items-start p-2 bg-gray-50 rounded text-sm"
              >
                <div className="flex-1">
                  <div className="font-medium">{error.title}</div>
                  <div className="text-gray-600">{error.message}</div>
                  <div className="text-xs text-gray-500">
                    {error.category} • {error.severity}
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => removeError(error.id)}
                  className="ml-2 text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Main examples component
 */
export const ErrorManagementExamples: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        Error Management System Examples
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <BasicErrorExample />
        <ApiErrorExample />
        <FormErrorExample />
        <ErrorDashboard />
      </div>
    </div>
  );
};
