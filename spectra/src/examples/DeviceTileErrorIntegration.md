# DeviceTile API Error Integration Example

This document demonstrates how to integrate API calls with our centralized error management system using the `DeviceTile.tsx` component as an example.

## 📋 **Before vs After Comparison**

### **❌ BEFORE: Manual Error Handling**

```typescript
// Old approach - manual try-catch with console.error
const fetchStats = async () => {
  setStatsLoading(true);
  try {
    const response = await getSummaryForThing(
      device.placeType ?? "site",
      device.siteId,
      device.thingId,
      start,
      end,
      simulationId,
    );
    const mappedData = datapointsToMap(response);
    setStats(mappedData);
  } catch (error) {
    console.error(
      `Unable to fetch stats for device ${device.thingId}`,
      error,
    );
    setStats({});
  } finally {
    setStatsLoading(false);
  }
};
```

**Problems with this approach:**
- ❌ Errors only logged to console (users don't see them)
- ❌ No user-friendly error messages
- ❌ Inconsistent error handling across components
- ❌ No centralized error tracking
- ❌ Silent failures - users don't know what went wrong

### **✅ AFTER: Centralized Error Management**

```typescript
// New approach - centralized error handling with user feedback
import { ErrorPatterns } from "../../errorManagement";

const fetchStats = async () => {
  setStatsLoading(true);

  // Use centralized error handling for the API call
  const result = await ErrorPatterns.withErrorHandling(
    async () => {
      const response = await getSummaryForThing(
        device.placeType ?? "site",
        device.siteId,
        device.thingId,
        start,
        end,
        simulationId,
      );
      return datapointsToMap(response);
    },
    `Failed to load device statistics for ${device.thingName || device.thingId}. Please try refreshing the page or check your connection.`
  );

  // Handle the response - result will be null if there was an error
  if (result) {
    setStats(result);
  } else {
    // Error was already handled by the centralized system
    // Set empty stats to show "No data" state
    setStats({});
  }

  setStatsLoading(false);
};
```

**Benefits of this approach:**
- ✅ User-friendly error notifications via ErrorToast
- ✅ Consistent error handling across the application
- ✅ Centralized error tracking and logging
- ✅ Graceful degradation - app continues working
- ✅ Customizable error messages per context
- ✅ No more silent failures

## 🔧 **Integration Steps**

### **Step 1: Import Error Management**

```typescript
import { ErrorPatterns } from "../../errorManagement";
```

### **Step 2: Wrap API Calls**

```typescript
// Replace this pattern:
try {
  const response = await apiCall();
  handleSuccess(response);
} catch (error) {
  console.error('Error:', error);
  handleError();
}

// With this pattern:
const result = await ErrorPatterns.withErrorHandling(
  async () => {
    const response = await apiCall();
    return processResponse(response);
  },
  "User-friendly error message describing what failed"
);

if (result) {
  handleSuccess(result);
} else {
  handleError(); // Error already shown to user
}
```

### **Step 3: Customize Error Messages**

Make error messages:
- **User-friendly** - avoid technical jargon
- **Contextual** - mention what the user was trying to do
- **Actionable** - suggest what the user can do next

```typescript
// ❌ Bad error message
"API call failed"

// ✅ Good error message
`Failed to load device statistics for ${device.thingName}. Please try refreshing the page or check your connection.`
```

## 📊 **Real-World Results**

### **User Experience Improvements:**

**Before:**
- No indication of what went wrong
- User doesn't know if they should wait or take action

**After:**
- User sees helpful error notification
- Clear explanation of what failed
- Suggested actions to resolve the issue

### **Developer Experience Improvements:**

**Before:**
- Inconsistent error handling patterns
- Manual error logging in every component
- Difficult to track errors across the app
- Repetitive try-catch boilerplate

**After:**
- Consistent error handling pattern
- Centralized error tracking
- Automatic user notifications

## 🎯 **Best Practices**

### **1. Error Message Guidelines**

```typescript
// ✅ Good: Specific, actionable, user-friendly
`Failed to load device statistics for ${deviceName}. Please try refreshing the page or check your connection.`

// ✅ Good: Context-aware
`Unable to check connection status for ${deviceName}. Connection indicator may not be accurate.`

// ❌ Bad: Technical, not actionable
"HTTP 500 Internal Server Error"

// ❌ Bad: Too generic
"Something went wrong"
```

### **2. Response Handling Pattern**

```typescript
const result = await ErrorPatterns.withErrorHandling(
  async () => {
    // API call and data processing
    const response = await apiCall();
    return processData(response);
  },
  "User-friendly error message"
);

// Always check if result exists
if (result) {
  // Success case
  setState(result);
} else {
  // Error case - error already shown to user
  setState(fallbackValue);
}
```

### **3. Maintain Existing Functionality**

```typescript
// Ensure loading states work correctly
setLoading(true);
const result = await ErrorPatterns.withErrorHandling(/* ... */);
// Always set loading to false, regardless of success/failure
setLoading(false);

// Ensure fallback states are handled
if (result) {
  setData(result);
} else {
  setData([]); // or appropriate fallback
}
```

## 🚀 **Migration Checklist**

When integrating existing API calls:

- [ ] ✅ Import `ErrorPatterns` from error management
- [ ] ✅ Replace try-catch with `withErrorHandling`
- [ ] ✅ Move data processing inside the async function
- [ ] ✅ Provide user-friendly error message
- [ ] ✅ Handle null result appropriately
- [ ] ✅ Maintain loading states
- [ ] ✅ Ensure fallback UI works
- [ ] ✅ Test both success and failure scenarios
- [ ] ✅ Remove old console.error statements
- [ ] ✅ Verify error notifications appear correctly

## 🧪 **Testing the Integration**

### **Test Success Case:**
1. Component loads normally
2. Data displays correctly
3. No error notifications appear

### **Test Failure Case:**
1. Simulate API failure (network disconnect, server error)
2. Verify ErrorToast notification appears
3. Verify error message is user-friendly
4. Verify component shows fallback state (e.g., "No data")
5. Verify app continues working normally

### **Test Error Message:**
1. Check that device name appears in error message
2. Verify message suggests actionable steps
3. Ensure message is not technical

The same pattern can be applied to any component with API calls for consistent, user-friendly error handling across the entire application.
