# Centralized Error Management System

A comprehensive error handling solution that provides graceful error management, user-friendly notifications, and centralized error state management.

## Features

- **Centralized Error Store**: Zustand-based store for managing all application errors
- **Error Categories**: Different handling strategies for network, authentication, validation, server, and runtime errors
- **User-Friendly UI**: Non-blocking toast notifications and error boundaries
- **Auto-Recovery**: Retry mechanisms and automatic error dismissal
- **Type Safety**: Full TypeScript support with comprehensive error types
- **Storybook Integration**: Interactive component documentation and testing
- **No External Dependencies**: Uses custom ID generation for better compatibility

## Quick Start

### 1. Installation

The error management system is already integrated into the application. No additional installation is required.

### 2. Basic Usage

```typescript
import { useErrorActions } from '../stores/errorStore';

function MyComponent() {
  const { showError, showNetworkError } = useErrorActions();

  const handleError = () => {
    showError('Something went wrong', 'Please try again later', 'client');
  };

  const handleApiError = async () => {
    try {
      const response = await fetch('/api/data');
      if (!response.ok) {
        showNetworkError(response, '/api/data', 'GET');
      }
    } catch (error) {
      showError('Network Error', 'Failed to fetch data', 'network');
    }
  };

  return (
    <div>
      <button onClick={handleError}>Show Error</button>
      <button onClick={handleApiError}>Test API Error</button>
    </div>
  );
}
```

### 3. API Integration

```typescript
import { useApiErrorHandler } from '../utils/apiErrorHandler';

function DataComponent() {
  const { withErrorHandling } = useApiErrorHandler();

  const fetchData = async () => {
    const result = await withErrorHandling(
      async () => {
        const response = await fetch('/api/data');
        if (!response.ok) throw new Error('Failed to fetch');
        return response.json();
      },
      {
        operationName: 'Fetch data',
        customErrorMessage: 'Unable to load data. Please try again.',
      }
    );

    if (result) {
      // Handle successful result
      console.log('Data loaded:', result);
    }
  };

  return <button onClick={fetchData}>Load Data</button>;
}
```

## Error Categories

| Category | Description | Default Behavior |
|----------|-------------|------------------|
| `network` | HTTP errors, timeouts, connectivity issues | Auto-hide after 5s, retryable |
| `authentication` | Login required, session expired | No auto-hide, may redirect |
| `authorization` | Permission denied, access forbidden | Auto-hide after 8s |
| `validation` | Form validation, input errors | No auto-hide, field-specific |
| `server` | 5xx errors, backend issues | Auto-hide after 8s, retryable |
| `client` | 4xx errors, client-side issues | Auto-hide after 5s |
| `runtime` | JavaScript errors, component crashes | No auto-hide, critical |
| `unknown` | Unclassified errors | Auto-hide after 5s |

## Components

### ErrorProvider

Wrap your app with `ErrorProvider` to enable global error management:

```typescript
import { ErrorProvider } from '../components/uikit/ErrorProvider/ErrorProvider';

function App() {
  return (
    <ErrorProvider toastPosition="top-right" maxToasts={5}>
      {/* Your app content */}
    </ErrorProvider>
  );
}
```

### ErrorBoundary

Catch React component errors:

```typescript
import { ErrorBoundary } from '../components/uikit/ErrorBoundary/ErrorBoundary';

function MyComponent() {
  return (
    <ErrorBoundary>
      <SomeComponentThatMightFail />
    </ErrorBoundary>
  );
}
```

### ErrorToast

Display individual error notifications:

```typescript
import { ErrorToast } from '../components/uikit/ErrorToast/ErrorToast';
import { createError } from '../utils/errorUtils';

const error = createError('Title', 'Message', 'network');

<ErrorToast error={error} position="top-right" />
```

## Store API

### Actions

```typescript
const {
  addError,           // Add custom error
  removeError,        // Remove specific error
  clearErrors,        // Clear all errors
  clearErrorsByCategory, // Clear errors by category
  showError,          // Show generic error
  showNetworkError,   // Show network error
  showValidationError, // Show validation error
  showAuthError,      // Show auth error
  showServerError,    // Show server error
} = useErrorActions();
```

### State

```typescript
const errors = useErrors(); // Get all current errors

const {
  hasErrors,
  hasErrorsOfCategory,
  getErrorsByCategory,
  getErrorsBySeverity,
} = useErrorUtils();
```

## Examples

See `src/examples/ErrorManagementExamples.tsx` for comprehensive usage examples including:

- Basic error handling
- API error management
- Form validation errors
- Error dashboard

## Storybook

View interactive component documentation:

```bash
npm run storybook
```

Navigate to:
- UIKit/ErrorToast
- UIKit/ErrorBoundary
- UIKit/ErrorProvider

## Configuration

### Global Setup

```typescript
import { setupErrorManagement } from '../errorManagement';

// Call during app initialization
setupErrorManagement();
```

### Error Provider Configuration

```typescript
<ErrorProvider
  toastPosition="top-right"    // Toast position
  maxToasts={5}               // Max simultaneous toasts
  enableErrorBoundary={true}  // Enable error boundary
>
  {children}
</ErrorProvider>
```

## Best Practices

1. **Use appropriate error categories** for proper handling
2. **Provide user-friendly messages** instead of technical details
3. **Make errors retryable** when appropriate
4. **Clear errors** when navigating or after successful operations
5. **Use error boundaries** around components that might fail
6. **Test error scenarios** in development and staging

## Troubleshooting

### Storybook Compatibility

The error management system is designed to work seamlessly with Storybook. If you encounter any issues:

1. **UUID Errors**: The system uses custom ID generation instead of the `uuid` package to avoid Storybook compatibility issues
2. **Module Resolution**: All imports use relative paths for better compatibility

### Common Issues

- **Error IDs not unique**: The custom ID generator uses timestamp + random string for uniqueness
- **TypeScript errors**: Ensure all error types are properly imported from `../types/errors`
- **Storybook stories not loading**: Check that all dependencies are properly resolved

## Migration Guide

To integrate with existing error handling:

1. Replace `console.error()` calls with `showError()`
2. Update API error handling to use `withErrorHandling()`
3. Add `ErrorBoundary` around components
4. Replace custom error modals with the centralized system
5. Remove any existing UUID dependencies if they cause conflicts
