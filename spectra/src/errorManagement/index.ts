/**
 * Centralized Error Management System
 *
 * This module provides a comprehensive error handling solution for the application.
 * It includes error types, utilities, stores, and UI components for managing errors gracefully.
 */

import { setupGlobalErrorHandlers } from "../utils/apiErrorHandler";

// Types
export type {
  AnyError,
  AppError,
  AuthenticationError,
  ErrorAction,
  ErrorCategory,
  ErrorSeverity,
  NetworkError,
  RuntimeError,
  ServerError,
  ValidationError,
} from "../types/errors";

export {
  ERROR_CONFIGS,
  ERROR_MESSAGES,
  HTTP_STATUS_TO_CATEGORY,
} from "../types/errors";

// Utilities
export {
  createAuthenticationError,
  createError,
  createNetworkError,
  createOfflineError,
  createRuntimeError,
  createServerError,
  createValidationError,
  getUserFriendlyMessage,
  isOffline,
  isRetryableError,
  shouldAutoHide,
} from "../utils/errorUtils";

export {
  createApiErrorHandler,
  createRetry<PERSON>and<PERSON>,
  useApiErrorHandler,
} from "../utils/apiErrorHandler";

// Store
export {
  useErrorActions,
  useErrors,
  useErrorStore,
  useErrorUtils,
} from "../stores/errorStore";

// UI Components
export {
  DefaultErrorFallback,
  ErrorBoundary,
  useErrorBoundary,
} from "../components/uikit/ErrorBoundary/ErrorBoundary";
export {
  ErrorProvider,
  useErrorProvider,
} from "../components/uikit/ErrorProvider/ErrorProvider";
export { ErrorToast } from "../components/uikit/ErrorToast/ErrorToast";

// Examples
export {
  ApiErrorExample,
  BasicErrorExample,
  ErrorDashboard,
  ErrorManagementExamples,
  FormErrorExample,
} from "../examples/ErrorManagementExamples";

// Testing Utilities
export {
  consoleCommands,
  errorTestUtils,
  testGlobalJavaScriptErrors,
  testUnhandledPromiseRejections,
} from "../utils/globalErrorTester";

/**
 * Quick setup function for basic error management
 * Call this in your app's initialization to set up global error handlers
 */
export const setupErrorManagement = () => {
  setupGlobalErrorHandlers();

  // You can add additional setup logic here
  console.log("Error management system initialized");
};

/**
 * Common error handling patterns
 */
export const ErrorPatterns = {
  /**
   * Handle API response errors
   */
  handleApiResponse: async (
    response: Response,
    url?: string,
    method?: string,
  ) => {
    if (!response.ok) {
      const { useErrorStore } = await import("../stores/errorStore");

      // Handle 403 errors gracefully - don't block the entire page
      if (response.status === 403) {
        const { showNetworkError } = useErrorStore.getState();
        showNetworkError(response, url, method);
      } else {
        const { showNetworkError } = useErrorStore.getState();
        showNetworkError(response, url, method);
      }

      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response;
  },

  /**
   * Handle async operations with error management
   */
  withErrorHandling: async <T>(
    operation: () => Promise<T>,
    errorMessage = "Operation failed",
  ): Promise<T | null> => {
    try {
      return await operation();
    } catch (error) {
      const { useErrorStore } = await import("../stores/errorStore");
      const { showError } = useErrorStore.getState();
      showError("Error", errorMessage, "unknown");
      return null;
    }
  },

  /**
   * Handle form validation errors
   */
  handleValidationErrors: (errors: Record<string, string[]>) => {
    const { useErrorActions } = require("../stores/errorStore");
    const { showValidationError } = useErrorActions.getState();

    const errorCount = Object.keys(errors).length;
    const message = `Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""}`;

    showValidationError(message, undefined, errors);
  },
};
