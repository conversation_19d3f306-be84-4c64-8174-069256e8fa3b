# Global Error Handler Testing Guide

This guide explains when the global error event listeners are triggered and how to test them in your development environment.

## 📋 **Event Listener Overview**

### 1. **Unhandled Promise Rejection Listener**
```typescript
window.addEventListener('unhandledrejection', (event) => {
  // Triggered when a Promise rejects without a .catch() handler
});
```

**When triggered:**
- `Promise.reject()` without `.catch()`
- `async function` throws without `try-catch` or `.catch()`
- `fetch()` requests that fail without error handling
- Promise chains that reject without final `.catch()`
- JSON parsing errors in promises without error handling

### 2. **Global JavaScript Error Listener**
```typescript
window.addEventListener('error', (event) => {
  // Triggered when JavaScript runtime errors occur
});
```

**When triggered:**
- Accessing properties on `null` or `undefined`
- Calling functions that don't exist
- Type errors (calling string methods on non-strings)
- Reference errors (accessing undefined variables)
- Syntax errors in `eval()`
- Array method calls on non-arrays

## 🧪 **Testing Methods**

### **Console Commands (Quick Testing)**

Copy and paste these commands into your browser console:

#### **Unhandled Promise Rejection Tests:**

```javascript
// Basic promise rejection
Promise.reject(new Error('Test unhandled promise rejection'));

// Async function error
(async () => {
  throw new Error('Async function error test');
})();

// Fetch error
fetch('/non-existent-endpoint-test');

// Promise chain error
Promise.resolve().then(() => {
  throw new Error('Promise chain error');
});

// Delayed rejection
setTimeout(() => {
  Promise.reject(new Error('Delayed rejection test'));
}, 1000);
```

#### **Global JavaScript Error Tests:**

```javascript
// Undefined property access
setTimeout(() => (null).someProperty, 100);

// Undefined function call
setTimeout(() => window.nonExistentFunction(), 100);

// Array method on null
setTimeout(() => (null).map(() => {}), 100);

// Reference error
setTimeout(() => console.log(undefinedVariable), 100);

// String method error
setTimeout(() => (null).toUpperCase(), 100);

// Eval syntax error
setTimeout(() => eval('invalid syntax {{{'), 100);
```

### **Utility Functions**

Import and use the testing utilities:

```typescript
import {
  testUnhandledPromiseRejections,
  testGlobalJavaScriptErrors,
  errorTestUtils
} from './utils/globalErrorTester';

// Test individual scenarios
testUnhandledPromiseRejections.basicRejection();
testGlobalJavaScriptErrors.undefinedPropertyAccess();

// Run all tests
errorTestUtils.runAllPromiseTests();
errorTestUtils.runAllJavaScriptTests();
```

## 🔧 **Setup Requirements**

### **1. Initialize Global Error Handlers**

Make sure to call `setupGlobalErrorHandlers()` in your app initialization:

```typescript
import { setupGlobalErrorHandlers } from './utils/apiErrorHandler';

// In your main App component or index file
useEffect(() => {
  setupGlobalErrorHandlers();
}, []);
```

### **2. Wrap App with ErrorProvider**

Ensure your app is wrapped with the ErrorProvider:

```typescript
import { ErrorProvider } from './components/uikit/ErrorProvider/ErrorProvider';

<ErrorProvider toastPosition="top-right" maxToasts={5}>
  <YourApp />
</ErrorProvider>
```

## 📊 **Expected Results**

When you trigger the tests, you should see:

### **Console Output:**
```
🧪 Testing: Basic unhandled promise rejection
✅ Triggered: Promise.reject() without .catch()
Unhandled promise rejection: Error: Test unhandled promise rejection
```

### **Visual Results:**
- **ErrorToast notifications** appear in the configured position
- **Error details** show in the toast (title, message, severity)
- **Auto-hide behavior** based on error type
- **Dismiss buttons** for user interaction

### **Error Store Updates:**
- Errors are added to the centralized error store
- Error categories are correctly assigned
- Severity levels are appropriate
- Auto-hide timers work as expected

## 📝 **Adding Custom Tests**

To add your own error scenarios:

```typescript
// Add to globalErrorTester.ts
export const customErrorTests = {
  myCustomTest: () => {
    console.log('🧪 Testing: My custom error');

    // Your error-triggering code here
    setTimeout(() => {
      throw new Error('My custom error');
    }, 100);

    console.log('✅ Triggered: My custom error');
  }
};
```

## 🔍 **Debugging Tips**

1. **Open browser DevTools** before running tests
2. **Check the Console tab** for error logs
3. **Monitor the Network tab** for failed requests
4. **Use React DevTools** to inspect error store state
5. **Check ErrorProvider props** for correct configuration

## 📚 **Real-World Scenarios**

These global error handlers catch errors that occur in:

- **Third-party libraries** that don't handle errors properly
- **Async operations** without proper error handling
- **Event handlers** that throw uncaught errors
- **Dynamic imports** that fail
- **Web API calls** that aren't wrapped in try-catch
- **User interactions** that trigger unexpected errors

The centralized error management system ensures these errors are handled gracefully and presented to users in a consistent, user-friendly way.
