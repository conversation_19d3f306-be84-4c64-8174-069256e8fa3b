const { createProxyMiddleware } = require("http-proxy-middleware");

const urls = {
  local: {
    ingestion: "http://localhost:5216",
    data: "http://localhost:5190",
  },
  development: {
    ingestion: "https://ingestion.spectra.api.dev.aerovy.com",
    data: "https://data.spectra.api.dev.aerovy.com",
    enterprise: "https://spectra.api.dev.aerovy.com",
  },
  staging: {
    ingestion: "https://ingestion.spectra.api.staging.aerovy.com",
    data: "https://data.spectra.api.staging.aerovy.com",
    enterprise: "https://spectra.api.staging.aerovy.com",
  },
  production: {
    ingestion: "https://ingestion.spectra.api.aerovy.com",
    data: "https://data.spectra.api.aerovy.com",
    enterprise: "https://spectra.api.aerovy.com",
  },
};

module.exports = (app) => {
  const proxyEnv = process.env.PROXY_ENV || "local";
  const { ingestion, data, enterprise } = urls[proxyEnv];

  app.use(
    "/ingestion",
    createProxyMiddleware({
      logger: console,
      logLevel: "debug",
      target: ingestion,
      changeOrigin: true,
      pathRewrite: {
        "^/ingestion": "",
      },
    }),
  );

  app.use(
    "/data",
    createProxyMiddleware({
      logger: console,
      logLevel: "debug",
      target: data,
      changeOrigin: true,
      pathRewrite: {
        "^/data": "",
      },
    }),
  );

  app.use(
    "/enterprise",
    createProxyMiddleware({
      logger: console,
      logLevel: "debug",
      target: enterprise,
      changeOrigin: true,
      pathRewrite: {
        "^/enterprise": "",
      },
    }),
  );

  app.use(
    "/consumerswap",
    createProxyMiddleware({
      logger: console,
      logLevel: "debug",
      target: "https://consumerswap.api.dev.id.aerovy.com",
      changeOrigin: true,
      pathRewrite: {
        "^/consumerswap": "",
      },
    }),
  );
};
