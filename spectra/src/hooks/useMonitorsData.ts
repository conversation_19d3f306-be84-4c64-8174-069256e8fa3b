import type { Monitor } from "api/enterprise/monitors";
import { useMonitorsApi } from "api/enterprise/monitors";
import { useAuth } from "context/AuthContext";
import { useMonitorsStore } from "stores/monitorsStore";

export const useMonitorsData = () => {
  const {
    monitors,
    isLoading,
    isAdding,
    isUpdating,
    setMonitors,
    setLoading,
    setAdding,
    setUpdating,
  } = useMonitorsStore();

  const { getMonitors } = useMonitorsApi();
  const { user } = useAuth();

  const fetchMonitorsIfNeeded = async () => {
    if (monitors.length > 0) {
      return monitors;
    }

    if (!user?.partnerId) {
      return [];
    }

    setLoading(true);
    try {
      const tempMonitors: Monitor[] = [];
      let hasMore = true;
      let continuationToken: string | undefined = undefined;

      while (hasMore) {
        const response = await getMonitors(user.partnerId, continuationToken);
        if (Array.isArray(response.items)) {
          tempMonitors.push(...response.items);
        }
        hasMore = response.hasMore;
        continuationToken = response.continuationToken ?? undefined;
      }

      setMonitors(tempMonitors);
      return tempMonitors;
    } catch (error) {
      console.error("Error fetching monitors:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshMonitors = async () => {
    if (!user?.partnerId) {
      return [];
    }

    setLoading(true);
    try {
      const tempMonitors: Monitor[] = [];
      let hasMore = true;
      let continuationToken: string | undefined = undefined;

      while (hasMore) {
        const response = await getMonitors(user.partnerId, continuationToken);
        if (Array.isArray(response.items)) {
          tempMonitors.push(...response.items);
        }
        hasMore = response.hasMore;
        continuationToken = response.continuationToken ?? undefined;
      }

      setMonitors(tempMonitors);
      return tempMonitors;
    } catch (error) {
      console.error("Error refreshing monitors:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    // Data
    monitors,
    // Loading states
    isLoading,
    isAdding,
    isUpdating,
    // Methods
    fetchMonitorsIfNeeded,
    refreshMonitors,
  };
};
