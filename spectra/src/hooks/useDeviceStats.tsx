import { type DatapointMap, datapointsToMap } from "api/data";
import type { Thing } from "api/ingestion/things";
import { useAuth } from "context/AuthContext";
import { useSelectedSimulation } from "context/SelectedSimulationContext";
import { useSelectedTimeRange } from "context/SelectedTimeRangeContext";
import { useEffect, useMemo, useState } from "react";
import { useLastPolledTimeStore } from "stores/lastPolledTimeStore";
import { useDataApi } from "../api/data";
import { ErrorPatterns } from "../errorManagement";

export const useDeviceStats = (device: Thing) => {
  // state
  const [stats, setStats] = useState<DatapointMap>({});
  const [localLastEventTime, setLocalLastEventTime] = useState<string | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const lastEventTime = useLastPolledTimeStore(
    (state) => state.lastEventTime[device.thingId],
  );

  // contexts
  const { start, end } = useSelectedTimeRange();
  const { simulationId } = useSelectedSimulation();
  const { user } = useAuth();

  // api
  const { getSummaryForThing, getThingLastEventTime } = useDataApi();

  // derived
  const reportingInterval = Number(device.properties?.reportingInterval) ?? 60;

  const isDisconnected = useMemo(() => {
    const time = localLastEventTime || lastEventTime;
    if (!time || time === "") return true;
    try {
      const lastEvent = new Date(time).getTime();
      if (Number.isNaN(lastEvent)) return true;

      const now = new Date().getTime();
      const timeElapsed = now - lastEvent;
      return timeElapsed > reportingInterval * 60 * 1000;
    } catch (e) {
      console.error("Error parsing date:", lastEventTime, e);
      return true;
    }
  }, [lastEventTime, localLastEventTime, reportingInterval]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch stats on mount; getSummaryForThing and user are not a dependency
  useEffect(() => {
    if (!user) return;

    const fetchStats = async () => {
      setIsLoading(true);

      const result = await ErrorPatterns.withErrorHandling(async () => {
        const response = await getSummaryForThing(
          device.placeType ?? "site",
          device.siteId,
          device.thingId,
          start,
          end,
          simulationId,
        );
        return datapointsToMap(response);
      }, `Failed to load device statistics for ${device.thingName} (${device.thingId}). Device may be offline.`);

      if (result) {
        // filter out stats that have unit string
        const filteredStats = Object.fromEntries(
          Object.entries(result).filter(
            ([_, value]) => value.unit !== "string",
          ),
        );
        setStats(filteredStats);
      } else {
        // Error was already handled by the centralized system
        // Set empty stats to show "No data" state
        setStats({});
      }

      setIsLoading(false);
    };

    fetchStats();
  }, [device, start, end, user?.partnerId, simulationId]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to refetch stats when last event time changes
  useEffect(() => {
    if (!lastEventTime || isLoading || !user) return;

    const fetchStats = async () => {
      await getSummaryForThing(
        device.placeType ?? "site",
        device.siteId,
        device.thingId,
        lastEventTime, // note: the last event time is the start time so we can ensure we get the latest stats
        lastEventTime.add(1, "minutes"),
        simulationId,
      )
        .then(datapointsToMap)
        .then((response) => {
          // filter out stats that have unit string
          return Object.fromEntries(
            Object.entries(response).filter(
              ([_, value]) => value.unit !== "string",
            ),
          );
        })
        .then(setStats)
        .catch((e) => console.log("refetching failed", e));
    };

    fetchStats();
  }, [lastEventTime?.toString()]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch last event time on mount; getThingLastEventTime is not a dependency
  useEffect(() => {
    if (!user) return;

    const fetchLastEventTime = async () => {
      const result = await ErrorPatterns.withErrorHandling(async () => {
        return await getThingLastEventTime(
          device.placeType ?? "site",
          device.siteId,
          device.thingId,
        );
      }, `Failed to check connection status for ${device.thingName} (${device.thingId}). Connection indicator may not be accurate.`);

      // Handle the response - result will be null if there was an error
      setLocalLastEventTime(result || null);
    };

    fetchLastEventTime();
  }, [device, user]);

  return {
    stats,
    isLoading,
    lastEventTime: localLastEventTime,
    isDisconnected,
    reportingInterval,
  };
};
