import { useCallback, useEffect, useState } from "react";
import { type KeyType, useDataApi } from "../api/data";

export const useThingKeys = () => {
  const [thingPropertyKeys, setThingPropertyKeys] = useState<
    Record<string, KeyType[]>
  >({});
  const [thingMetricKeys, setThingMetricKeys] = useState<
    Record<string, KeyType[]>
  >({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const { getThingPropertyKeys, getThingMetricKeys } = useDataApi();

  // biome-ignore lint/correctness/useExhaustiveDependencies: getThingPropertyKeys, getThingMetricKeys are not dependent on anything
  const fetchKeys = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const [propertyKeys, metricKeys] = await Promise.all([
        getThingPropertyKeys(),
        getThingMetricKeys(),
      ]);

      setThingPropertyKeys(propertyKeys);
      setThingMetricKeys(metricKeys);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchKeys();
  }, [fetchKeys]);

  return {
    thingPropertyKeys,
    thingMetricKeys,
    isLoading,
    error,
    refetch: fetchKeys,
  };
};
