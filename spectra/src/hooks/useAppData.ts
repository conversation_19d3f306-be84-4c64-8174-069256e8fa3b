import { useDevicesStore } from "stores/devicesStore";
import { useFleetsStore } from "stores/fleetsStore";
import { useIntegrationsStore } from "stores/integrationsStore";
import { useMonitorsStore } from "stores/monitorsStore";
import { useSitesStore } from "stores/sitesStore";
import { useDevicesData } from "./useDevicesData";
import { useFleetsData } from "./useFleetsData";
import { useIntegrationsData } from "./useIntegrationsData";
import { useMonitorsData } from "./useMonitorsData";
import { useSitesData } from "./useSitesData";

export const useAppData = () => {
  // Get data from individual stores
  const { sites } = useSitesStore();
  const { devices } = useDevicesStore();
  const { fleets } = useFleetsStore();
  const { integrations } = useIntegrationsStore();
  const { monitors } = useMonitorsStore();

  // Get loading states from individual stores
  const sitesLoading = useSitesStore((state) => state.isLoading);
  const devicesLoading = useDevicesStore((state) => state.isLoading);
  const fleetsLoading = useFleetsStore((state) => state.isLoading);
  const integrationsLoading = useIntegrationsStore((state) => state.isLoading);
  const monitorsLoading = useMonitorsStore((state) => state.isLoading);

  // Get adding states from individual stores
  const sitesAdding = useSitesStore((state) => state.isAdding);
  const devicesAdding = useDevicesStore((state) => state.isAdding);
  const fleetsAdding = useFleetsStore((state) => state.isAdding);
  const integrationsAdding = useIntegrationsStore((state) => state.isAdding);
  const monitorsAdding = useMonitorsStore((state) => state.isAdding);

  // Get updating states from individual stores
  const sitesUpdating = useSitesStore((state) => state.isUpdating);
  const devicesUpdating = useDevicesStore((state) => state.isUpdating);
  const fleetsUpdating = useFleetsStore((state) => state.isUpdating);
  const integrationsUpdating = useIntegrationsStore(
    (state) => state.isUpdating,
  );
  const monitorsUpdating = useMonitorsStore((state) => state.isUpdating);

  // Use all individual data hooks
  const { fetchSitesIfNeeded, refreshSites, addNewSite, editSite, deleteSite } =
    useSitesData();

  const {
    fetchDevicesIfNeeded,
    refreshDevices,
    addDevice,
    editThing,
    deleteThing,
  } = useDevicesData();

  const {
    fetchFleetsIfNeeded,
    refreshFleets,
    addNewFleet,
    editFleet,
    deleteFleet,
  } = useFleetsData();

  const {
    fetchIntegrationsIfNeeded,
    refreshIntegrations,
    addNewIntegration,
    removeIntegration,
  } = useIntegrationsData();

  const { fetchMonitorsIfNeeded, refreshMonitors } = useMonitorsData();

  const fetchAllIfNeeded = async () => {
    try {
      const [
        sitesData,
        devicesData,
        fleetsData,
        integrationsData,
        monitorsData,
      ] = await Promise.all([
        fetchSitesIfNeeded(),
        fetchDevicesIfNeeded(),
        fetchFleetsIfNeeded(),
        fetchIntegrationsIfNeeded(),
        fetchMonitorsIfNeeded(),
      ]);
      return {
        sites: sitesData,
        devices: devicesData,
        fleets: fleetsData,
        integrations: integrationsData,
        monitors: monitorsData,
      };
    } catch (error) {
      console.error("Error fetching data:", error);
      throw error;
    }
  };

  const refreshAll = async () => {
    try {
      const [
        sitesData,
        devicesData,
        fleetsData,
        integrationsData,
        monitorsData,
      ] = await Promise.all([
        refreshSites(),
        refreshDevices(),
        refreshFleets(),
        refreshIntegrations(),
        refreshMonitors(),
      ]);
      return {
        sites: sitesData,
        devices: devicesData,
        fleets: fleetsData,
        integrations: integrationsData,
        monitors: monitorsData,
      };
    } catch (error) {
      console.error("Error refreshing data:", error);
      throw error;
    }
  };

  return {
    // Data
    sites,
    devices,
    fleets,
    integrations,
    monitors,

    // Fetch methods
    fetchSitesIfNeeded,
    fetchDevicesIfNeeded,
    fetchFleetsIfNeeded,
    fetchIntegrationsIfNeeded,
    fetchMonitorsIfNeeded,
    fetchAllIfNeeded,

    // Refresh methods
    refreshSites,
    refreshDevices,
    refreshFleets,
    refreshIntegrations,
    refreshMonitors,
    refreshAll,

    // Add methods
    addNewSite,
    addDevice,
    addNewFleet,
    addNewIntegration,

    // Edit methods
    editSite,
    deleteSite,
    editThing,
    deleteThing,
    editFleet,
    deleteFleet,
    removeIntegration,

    // Loading states
    isLoading: {
      sites: sitesLoading,
      devices: devicesLoading,
      fleets: fleetsLoading,
      integrations: integrationsLoading,
      monitors: monitorsLoading,
    },
    isAdding: {
      site: sitesAdding,
      device: devicesAdding,
      fleet: fleetsAdding,
      integration: integrationsAdding,
      monitor: monitorsAdding,
    },
    isUpdating: {
      site: sitesUpdating,
      device: devicesUpdating,
      fleet: fleetsUpdating,
      integration: integrationsUpdating,
      monitor: monitorsUpdating,
    },
  };
};
