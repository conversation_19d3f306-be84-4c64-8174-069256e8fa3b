import type { FleetResponse } from "api/ingestion/places";
import { usePlacesApi } from "api/ingestion/places";
import { useFleetsStore } from "stores/fleetsStore";

export const useFleetsData = () => {
  const {
    fleets,
    isLoading,
    isAdding,
    isUpdating,
    setFleets,
    setLoading,
    setAdding,
    setUpdating,
  } = useFleetsStore();

  const {
    getFleets,
    addFleet,
    editFleet: editFleetApi,
    deleteFleet: deleteFleetApi,
  } = usePlacesApi();

  const fetchFleetsIfNeeded = async () => {
    if (fleets.length > 0) {
      return fleets;
    }

    setLoading(true);
    try {
      const fleetsData = await getFleets();
      setFleets(fleetsData);
      return fleetsData;
    } catch (error) {
      console.error("Error fetching fleets:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshFleets = async () => {
    setLoading(true);
    try {
      const fleetsData = await getFleets();
      setFleets(fleetsData);
      return fleetsData;
    } catch (error) {
      console.error("Error refreshing fleets:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const addNewFleet = async (fleetData: unknown) => {
    setAdding(true);
    try {
      const result = await addFleet(fleetData);
      if (result.success) {
        await refreshFleets();
        return {
          success: true,
          fleetId: result.fleetId,
        };
      }
      return { success: false, error: result.error };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : "Unknown error",
        },
      };
    } finally {
      setAdding(false);
    }
  };

  const editFleet = async (
    fleetId: string,
    fleetData: Partial<FleetResponse>,
  ) => {
    setUpdating(true);
    try {
      const result = await editFleetApi(fleetId, fleetData);
      if (result.success) {
        await refreshFleets();
        return { success: true };
      }
      return { success: false, error: result.error };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error
              ? error.message
              : "An unexpected error occurred while updating the fleet",
        },
      };
    } finally {
      setUpdating(false);
    }
  };

  const deleteFleet = async (fleetId: string) => {
    setUpdating(true);
    try {
      const result = await deleteFleetApi(fleetId);
      if (result.success) {
        await refreshFleets();
        return { success: true };
      }
      return { success: false, error: result.error };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error
              ? error.message
              : "An unexpected error occurred while deleting the fleet",
        },
      };
    } finally {
      setUpdating(false);
    }
  };

  return {
    // Data
    fleets,
    // Loading states
    isLoading,
    isAdding,
    isUpdating,
    // Methods
    fetchFleetsIfNeeded,
    refreshFleets,
    addNewFleet,
    editFleet,
    deleteFleet,
  };
};
