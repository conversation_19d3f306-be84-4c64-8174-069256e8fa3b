import type { SiteResponse } from "api/ingestion/places";
import { usePlacesApi } from "api/ingestion/places";
import { useSitesStore } from "stores/sitesStore";

export const useSitesData = () => {
  const {
    sites,
    isLoading,
    isAdding,
    isUpdating,
    setSites,
    setLoading,
    setAdding,
    setUpdating,
  } = useSitesStore();

  const {
    getSites,
    addSite,
    editSite: editSiteApi,
    deleteSite: deleteSiteApi,
  } = usePlacesApi();

  const fetchSitesIfNeeded = async () => {
    if (sites.length > 0) {
      return sites;
    }

    setLoading(true);
    try {
      const sitesData = await getSites();
      setSites(sitesData);
      return sitesData;
    } catch (error) {
      console.error("Error fetching sites:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshSites = async () => {
    setLoading(true);
    try {
      const sitesData = await getSites();
      setSites(sitesData);
      return sitesData;
    } catch (error) {
      console.error("Error refreshing sites:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const addNewSite = async (siteData: any) => {
    setAdding(true);
    try {
      const result = await addSite(siteData);
      if (result.success) {
        await refreshSites();
        return {
          success: true,
          siteId: result.siteId,
        };
      }
      return { success: false, error: result.error };
    } catch (error: any) {
      return { success: false, error: { message: error.message } };
    } finally {
      setAdding(false);
    }
  };

  const editSite = async (siteId: string, siteData: Partial<SiteResponse>) => {
    setUpdating(true);
    try {
      const result = await editSiteApi(siteId, siteData);
      if (result.success) {
        await refreshSites();
        return { success: true };
      }
      return { success: false, error: result.error };
    } catch (error: any) {
      return {
        success: false,
        error: {
          message:
            error?.message ||
            "An unexpected error occurred while updating the site",
        },
      };
    } finally {
      setUpdating(false);
    }
  };

  const deleteSite = async (siteId: string) => {
    setUpdating(true);
    try {
      const result = await deleteSiteApi(siteId);
      if (result.success) {
        await refreshSites();
        return { success: true };
      }
      return { success: false, error: result.error };
    } catch (error: any) {
      return {
        success: false,
        error: {
          message:
            error?.message ||
            "An unexpected error occurred while deleting the site",
        },
      };
    } finally {
      setUpdating(false);
    }
  };

  return {
    // Data
    sites,
    // Loading states
    isLoading,
    isAdding,
    isUpdating,
    // Methods
    fetchSitesIfNeeded,
    refreshSites,
    addNewSite,
    editSite,
    deleteSite,
  };
};
