import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import type { Monitor } from "../api/enterprise/monitors";
import { useMonitorsApi } from "../api/enterprise/monitors";
import type { Thing } from "../api/ingestion/things";
import { useThingsApi } from "../api/ingestion/things";
import type { ErrorType } from "../api/shared";
import { useAuth } from "../context/AuthContext";
import { useThingKeys } from "./useThingKeys";

export const useMonitor = (thingId: string, monitorId: string) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { getMonitorForThing, deleteMonitorForThing, updateMonitorForThing } =
    useMonitorsApi();
  const { getThings } = useThingsApi();

  const [monitor, setMonitor] = useState<Monitor | null>(null);
  const [originalMonitor, setOriginalMonitor] = useState<Monitor | null>(null);
  const [thing, setThing] = useState<Thing | null>(null);
  const [things, setThings] = useState<Thing[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [saveError, setSaveError] = useState<ErrorType | null>(null);
  const [deleteError, setDeleteError] = useState<ErrorType | null>(null);
  const [fetchError, setFetchError] = useState<ErrorType | null>(null);

  // Track items to delete by their names
  const [metricsToRemove, setMetricsToRemove] = useState<string[]>([]);
  const [variablesToRemove, setVariablesToRemove] = useState<string[]>([]);
  const [alertConfigsToRemove, setAlertConfigsToRemove] = useState<string[]>(
    [],
  );

  const {
    thingPropertyKeys,
    thingMetricKeys,
    isLoading: isLoadingKeys,
    error: keysError,
  } = useThingKeys();

  // biome-ignore lint/correctness/useExhaustiveDependencies: getMonitorForThing, getThings are not dependent on anything
  useEffect(() => {
    const fetchData = async () => {
      if (!thingId || !monitorId || !user?.partnerId) return;

      try {
        const [monitorData, thingsData] = await Promise.all([
          getMonitorForThing(thingId, monitorId, user.partnerId),
          getThings(),
        ]);

        setMonitor(monitorData);
        setOriginalMonitor(monitorData);
        setThings(thingsData);
        const thingData = thingsData.find((t) => t.thingId === thingId);
        if (thingData) {
          setThing(thingData);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setFetchError(error as ErrorType);
      }
    };

    fetchData();
  }, [thingId, monitorId, user?.partnerId]);

  const handleSave = async () => {
    if (
      !thingId ||
      !monitorId ||
      !user?.partnerId ||
      !monitor ||
      !originalMonitor
    )
      return;

    setIsSaving(true);
    setSaveError(null);

    try {
      // Create a complete monitor object with the changes
      const updateData = {
        ...monitor,
        alertConfigs: monitor.alertConfigs.map((alert) => {
          // add integrationIds: [] if not present
          if (!alert.integrationIds) {
            alert.integrationIds = [];
          }
          return alert;
        }),
        metricsToRemove: metricsToRemove,
        variablesToRemove: variablesToRemove,
        alertConfigsToRemove: alertConfigsToRemove,
      };

      // Send the update to the API
      await updateMonitorForThing(
        thingId,
        monitorId,
        user.partnerId,
        updateData,
      );

      // Reset change tracking
      setMetricsToRemove([]);
      setVariablesToRemove([]);
      setAlertConfigsToRemove([]);

      // Refresh the monitor data
      const updatedMonitor = await getMonitorForThing(
        thingId,
        monitorId,
        user.partnerId,
      );
      setMonitor(updatedMonitor);
      setOriginalMonitor(updatedMonitor);

      setIsEditing(false);
    } catch (error) {
      setSaveError(error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!thingId || !monitorId || !user?.partnerId) return;
    setIsDeleting(true);
    setDeleteError(null);

    try {
      await deleteMonitorForThing(thingId, monitorId, user.partnerId);
      navigate("/monitors");
    } catch (error) {
      setDeleteError(error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleUpdateMonitor = (updates: Partial<Monitor>) => {
    if (!monitor) return;

    // Update the monitor state
    setMonitor({ ...monitor, ...updates });
    setIsEditing(true);
  };

  // Helper functions to track deletions
  const addMetricToRemove = (metricName: string) => {
    setMetricsToRemove((prev) => [...prev, metricName]);
    setIsEditing(true);
  };

  const addVariableToRemove = (variableName: string) => {
    setVariablesToRemove((prev) => [...prev, variableName]);
    setIsEditing(true);
  };

  const addAlertToRemove = (alertName: string) => {
    setAlertConfigsToRemove((prev) => [...prev, alertName]);
    setIsEditing(true);
  };

  const resetToOriginalMonitor = () => {
    setMonitor(originalMonitor);
    setIsEditing(false);
    setMetricsToRemove([]);
    setVariablesToRemove([]);
    setAlertConfigsToRemove([]);
    setIsSaving(false);
    setIsDeleting(false);
    setSaveError(null);
    setDeleteError(null);
  };

  return {
    monitor,
    thing,
    things,
    thingPropertyKeys,
    thingMetricKeys,
    isEditing,
    isSaving,
    isDeleting,
    saveError,
    deleteError,
    handleSave,
    handleDelete,
    handleUpdateMonitor,
    setIsEditing,
    setSaveError,
    setDeleteError,
    addMetricToRemove,
    addVariableToRemove,
    addAlertToRemove,
    resetToOriginalMonitor,
    fetchError,
  };
};
