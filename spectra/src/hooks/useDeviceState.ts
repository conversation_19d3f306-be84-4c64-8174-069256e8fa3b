/**
 * Hook that enhances device data with device states, lastEventTime, and reportingInterval
 *
 * @example
 * ```typescript
 * import { useDeviceState, DeviceStates } from "hooks/useDeviceState";
 *
 * const MyComponent = () => {
 *   const { devices, isLoading } = useDeviceState();
 *
 *   const onlineDevices = devices.filter(device => device.deviceState === DeviceStates.ONLINE);
 *   const offlineDevices = devices.filter(device => device.deviceState === DeviceStates.OFFLINE);
 *
 *   if (isLoading) return <div>Loading...</div>;
 *
 *   return (
 *     <div>
 *       <h2>Online Devices: {onlineDevices.length}</h2>
 *       <h2>Offline Devices: {offlineDevices.length}</h2>
 *       {devices.map(device => (
 *         <div key={device.thingId}>
 *           <h3>{device.thingName}</h3>
 *           <p>State: {device.deviceState}</p>
 *           <p>Last Event: {device.lastEventTime}</p>
 *           <p>Reporting Interval: {device.reportingInterval}s</p>
 *         </div>
 *       ))}
 *     </div>
 *   );
 * };
 * ```
 */

import { useDataApi } from "api/data";
import type { Thing } from "api/ingestion/things";
import { useEffect, useState } from "react";
import { useAppData } from "./useAppData";

export enum DeviceStates {
  ONLINE = "online",
  OFFLINE = "offline",
  UNKNOWN = "unknown",
  UNINITIALIZED = "uninitialized",
}

export interface EnhancedDevice extends Thing {
  deviceState: DeviceStates;
  lastEventTime: string | null;
  reportingInterval: number | null;
}

export const useDeviceState = () => {
  const { devices, fetchDevicesIfNeeded, isLoading } = useAppData();
  const [enhancedDevices, setEnhancedDevices] = useState<EnhancedDevice[]>([]);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const { getThingLastEventTime } = useDataApi();

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchDevicesIfNeeded is not a dependency of the useEffect hook
  useEffect(() => {
    fetchDevicesIfNeeded();
  }, []);

  // TODO: this is not great... we should make this more robust if we intend on using it
  // biome-ignore lint/correctness/useExhaustiveDependencies: isLoading and getThingLastEventTime are not dependencies of the useEffect hook
  useEffect(() => {
    if (isLoading.devices || devices.length === 0) {
      setEnhancedDevices([]);
      return;
    }

    const enhanceDevices = async () => {
      setIsEnhancing(true);

      try {
        const enhancedDevicePromises = devices.map(async (device) => {
          // Get reporting interval from device properties
          const reportingInterval = device.properties?.reportingInterval
            ? Number(device.properties.reportingInterval)
            : null;

          let lastEventTime: string | null = null;
          try {
            // Fetch lastEventTime
            lastEventTime = await getThingLastEventTime(
              device.placeType ?? "site",
              device.siteId,
              device.thingId,
            );
          } catch (error) {
            console.error(
              `Error fetching last event time for device ${device.thingId}:`,
              error,
            );
          }

          // If lastEventTime is null, empty return UNINITIALIZED
          if (!lastEventTime || lastEventTime === "") {
            return {
              ...device,
              deviceState: DeviceStates.UNINITIALIZED,
              lastEventTime: lastEventTime,
              reportingInterval: reportingInterval,
            } as EnhancedDevice;
          }

          // If lastEventTime is not a valid date, return UNINITIALIZED
          const parseTime = (time: string): number | null => {
            try {
              const date = new Date(time).getTime();
              if (!Number.isNaN(date)) {
                return date;
              }
            } catch (e) {
              console.log("Error parsing date:", time, e);
            }

            return null;
          };

          const lastEvent = parseTime(lastEventTime);
          if (lastEvent === null) {
            return {
              ...device,
              deviceState: DeviceStates.UNINITIALIZED,
              lastEventTime: lastEventTime,
              reportingInterval: reportingInterval,
            } as EnhancedDevice;
          }

          // If reportingInterval is null, return UNKNOWN
          if (reportingInterval === null) {
            return {
              ...device,
              deviceState: DeviceStates.UNKNOWN,
              lastEventTime: lastEventTime,
              reportingInterval: null,
            } as EnhancedDevice;
          }

          // If lastEventTime is a valid date, return ONLINE or OFFLINE
          const now = new Date().getTime();
          const timeElapsed = now - lastEvent;
          const isOnline = timeElapsed < reportingInterval * 60 * 1000;

          return {
            ...device,
            deviceState: isOnline ? DeviceStates.ONLINE : DeviceStates.OFFLINE,
            lastEventTime,
            reportingInterval,
          } as EnhancedDevice;
        });

        const enhancedDevicesResult = await Promise.all(enhancedDevicePromises);
        setEnhancedDevices(enhancedDevicesResult);
      } catch (error) {
        console.error("Error enhancing devices:", error);
        // Fallback to basic device data with UNKNOWN state
        const fallbackDevices = devices.map((device) => ({
          ...device,
          deviceState: DeviceStates.UNKNOWN,
          lastEventTime: null,
          reportingInterval: null,
        })) as EnhancedDevice[];
        setEnhancedDevices(fallbackDevices);
      } finally {
        setIsEnhancing(false);
      }
    };

    enhanceDevices();
  }, [devices]);

  return {
    devices: enhancedDevices,
    isLoading: isLoading.devices || isEnhancing,
    fetchDevicesIfNeeded,
  };
};
