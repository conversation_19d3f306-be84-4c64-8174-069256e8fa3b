import { useIntegrationsApi } from "api/ingestion/integrations";
import { useIntegrationsStore } from "stores/integrationsStore";

export const useIntegrationsData = () => {
  const {
    integrations,
    isLoading,
    isAdding,
    isUpdating,
    setIntegrations,
    setLoading,
    setAdding,
    setUpdating,
  } = useIntegrationsStore();

  const { getIntegrations, createIntegration, deleteIntegration } =
    useIntegrationsApi();

  const fetchIntegrationsIfNeeded = async () => {
    if (integrations.length > 0) {
      return integrations;
    }

    setLoading(true);
    try {
      const integrationsData = await getIntegrations();
      setIntegrations(integrationsData);
      return integrationsData;
    } catch (error) {
      console.error("Error fetching integrations:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshIntegrations = async () => {
    setLoading(true);
    try {
      const integrationsData = await getIntegrations();
      setIntegrations(integrationsData);
      return integrationsData;
    } catch (error) {
      console.error("Error refreshing integrations:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const addNewIntegration = async (integrationData: unknown) => {
    setAdding(true);
    try {
      const result = await createIntegration(integrationData);
      if (result) {
        await refreshIntegrations();
        return {
          success: true,
          integrationId: result.integrationId,
        };
      }
      return {
        success: false,
        error: { message: "Failed to create integration" },
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : "Unknown error",
        },
      };
    } finally {
      setAdding(false);
    }
  };

  const removeIntegration = async (integrationId: string) => {
    setUpdating(true);
    try {
      const result = await deleteIntegration(integrationId);
      if (result) {
        await refreshIntegrations();
        return { success: true };
      }
      return {
        success: false,
        error: { message: "Failed to delete integration" },
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error
              ? error.message
              : "An unexpected error occurred while deleting the integration",
        },
      };
    } finally {
      setUpdating(false);
    }
  };

  return {
    // Data
    integrations,
    // Loading states
    isLoading,
    isAdding,
    isUpdating,
    // Methods
    fetchIntegrationsIfNeeded,
    refreshIntegrations,
    addNewIntegration,
    removeIntegration,
  };
};
