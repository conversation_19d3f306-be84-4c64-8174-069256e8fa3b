import { type DataResponse, type Datapoint, useDataApi } from "api/data";
import { useAuth } from "context/AuthContext";
import { useCallback, useEffect, useRef, useState } from "react";
import { useLastPolledTimeStore } from "stores/lastPolledTimeStore";
import { type Dayjs, dayjs } from "utils/dayjs";
import { typeToLabel } from "utils/typeToLabel";

const DEFAULT_POLLING_INTERVAL_MS = 5000;
const DEFAULT_MIN_TIME_DIFF_SECONDS = 1;

export type LogEntry = {
  dayjsTime: Dayjs;
} & DataResponse;

interface UsePollingDeviceLogsProps {
  placeType: string;
  placeId: string;
  thingId: string;
  pollingIntervalMs?: number;
  minTimeDiffSeconds?: number;
}

const deduplicateByKey = (
  logs: LogEntry[],
  key: keyof LogEntry,
): LogEntry[] => {
  const values = new Set<string>();
  return logs.filter((log) => {
    const value = log[key] as string;
    if (values.has(value)) {
      return false; // skip duplicate
    }
    values.add(value);
    return true; // keep unique
  });
};

const convertDataPoint = (dp: Datapoint): Datapoint => {
  // dont show units for string and count
  const unit = dp.unit === "string" || dp.unit === "count" ? "" : dp.unit;
  return {
    type: typeToLabel(dp.type),
    value: dp.value,
    unit,
  };
};

const dataToLogEntry = (response: DataResponse): LogEntry => ({
  time: response.time,
  dayjsTime: dayjs(response.time).utc(),
  dataPoints: response.dataPoints.map(convertDataPoint),
});

export const usePollingDeviceLogs = ({
  placeType,
  placeId,
  thingId,
  pollingIntervalMs = DEFAULT_POLLING_INTERVAL_MS,
  minTimeDiffSeconds = DEFAULT_MIN_TIME_DIFF_SECONDS,
}: UsePollingDeviceLogsProps) => {
  const [isPolling, setIsPolling] = useState(false);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [lastFetched, setLastFetched] = useState<dayjs.Dayjs | null>(null);
  const lastEventTime = useLastPolledTimeStore(
    (state) => state.lastEventTime[thingId],
  );
  const setLastEventTime = useLastPolledTimeStore(
    (state) => state.setLastEventTime,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingOlderLogs, setIsLoadingOlderLogs] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showUtcTimestamps, setShowUtcTimestamps] = useState(false);

  const { getThingRecentData, getThingLastEventTime, getRecentEventsForThing } =
    useDataApi();
  const { user } = useAuth();

  const isRequestInProgressRef = useRef(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // reset on placeType, placeId, thingId change
  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to reset the state when the props change
  useEffect(() => {
    setIsPolling(true);
    setLogs([]);
    setLastEventTime(thingId, null);
    setLastFetched(null);
    setError(null);
    setIsLoading(false);
    setIsLoadingOlderLogs(false);
    isRequestInProgressRef.current = false;
  }, [placeType, placeId, thingId]);

  // Memoize the fetch function to fix dependency issues
  // biome-ignore lint/correctness/useExhaustiveDependencies: getThingRecentData and getThingLastEventTime are not dependencies of this hook
  const fetchLogs = useCallback(
    async (cleanUpdate = false) => {
      if (!user || isRequestInProgressRef.current) return;

      isRequestInProgressRef.current = true;
      setIsLoading(true);
      setError(null);

      try {
        const now = dayjs();
        let startTime: dayjs.Dayjs;

        if (lastEventTime) {
          startTime = lastEventTime;
        } else {
          const lastEventTimeStr = await getThingLastEventTime(
            placeType,
            placeId,
            thingId,
          );
          startTime = dayjs(lastEventTimeStr);
          if (!startTime.isValid()) {
            throw new Error("No events found for this device");
          }
        }

        // Only fetch if there's a meaningful time range
        if (
          now.diff(startTime, "seconds") < minTimeDiffSeconds &&
          !cleanUpdate
        ) {
          setIsLoading(false);
          isRequestInProgressRef.current = false;
          return;
        }

        const data = await getThingRecentData(
          placeType,
          placeId,
          thingId,
          startTime,
        );

        // do not process data if polling is not active
        if (!intervalRef.current) return;

        if (data && data.length > 0) {
          const newLogs: LogEntry[] = data.map(dataToLogEntry);

          // Update last event time to the latest timestamp from the data
          const latestTimestamp = newLogs[newLogs.length - 1]?.dayjsTime;
          if (latestTimestamp) {
            setLastEventTime(thingId, latestTimestamp);
          }

          setLogs((prevLogs) => {
            // sort in descending order (newest first)
            const sortedLogs = newLogs.sort(
              (a, b) => b.dayjsTime.valueOf() - a.dayjsTime.valueOf(),
            );

            // Combine new logs with existing ones unless cleanUpdate
            const allLogs = cleanUpdate
              ? sortedLogs
              : [...sortedLogs, ...prevLogs];

            // filter out duplicates
            return deduplicateByKey(allLogs, "time");
          });
        }

        setLastFetched(now);
      } catch (err) {
        console.error("Error fetching logs:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch logs");
      } finally {
        setIsLoading(false);
        isRequestInProgressRef.current = false;
      }
    },
    [user, lastEventTime, placeType, placeId, thingId],
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: getThingLastEventTime and getRecentEventsForThing are not dependencies of this hook
  const fetchOlderLogs = useCallback(async () => {
    if (!user || isLoadingOlderLogs) return;

    setIsLoadingOlderLogs(true);
    setError(null);

    try {
      // Determine the timestamp to use for fetching older logs
      let timestamp: Dayjs = dayjs();

      if (logs.length > 0) {
        // Use the oldest log's time
        const oldestLog = logs[logs.length - 1];
        timestamp = oldestLog.dayjsTime;
      } else {
        // Get the last event time from the API
        const lastEventTimeStr = await getThingLastEventTime(
          placeType,
          placeId,
          thingId,
        );
        const lastEventTime = dayjs(lastEventTimeStr);
        if (lastEventTime.isValid()) {
          timestamp = lastEventTime;
        }
      }

      const olderLogs = await getRecentEventsForThing(
        placeType,
        placeId,
        thingId,
        timestamp,
        10, // numberOfEvents
      );

      if (olderLogs && olderLogs.length > 0) {
        const newLogEntries: LogEntry[] = olderLogs.map(dataToLogEntry);

        setLogs((prevLogs) => {
          // sort in descending order (newest first)
          const sortedLogs = newLogEntries.sort(
            (a, b) => b.dayjsTime.valueOf() - a.dayjsTime.valueOf(),
          );

          // Append older logs to the end of the array
          const combinedLogs = [...prevLogs, ...sortedLogs];

          // filter out duplicates
          return deduplicateByKey(combinedLogs, "time");
        });
      }
    } catch (err) {
      console.error("Error fetching older logs:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch older logs",
      );
    } finally {
      setIsLoadingOlderLogs(false);
    }
  }, [user, logs, lastEventTime, placeType, placeId, thingId]);

  // Polling effect
  // biome-ignore lint/correctness/useExhaustiveDependencies: pollingIntervalMs is not a dependency of this hook
  useEffect(() => {
    if (isPolling && !isRequestInProgressRef.current) {
      // Fetch immediately when starting
      fetchLogs();

      // Then fetch every pollingIntervalMs
      intervalRef.current = setInterval(fetchLogs, pollingIntervalMs);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isPolling, fetchLogs]);

  const handleStartPolling = useCallback(() => {
    setError(null);
    setIsPolling(true);
  }, []);

  const handleStopPolling = useCallback(() => {
    setIsPolling(false);
    isRequestInProgressRef.current = false; // Reset request flag when stopping
  }, []);

  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  return {
    // State
    isPolling,
    logs,
    lastFetched,
    isLoading,
    isLoadingOlderLogs,
    error,
    showUtcTimestamps,
    setShowUtcTimestamps,

    // Actions
    handleStartPolling,
    handleStopPolling,
    fetchOlderLogs,
    clearLogs,
  };
};
