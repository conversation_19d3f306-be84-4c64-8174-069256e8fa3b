import type { Thing } from "api/ingestion/things";
import { useThingsApi } from "api/ingestion/things";
import { useDevicesStore } from "stores/devicesStore";

export const useDevicesData = () => {
  const {
    devices,
    deviceTypes,
    isLoading,
    isAdding,
    isUpdating,
    setDevices,
    setDeviceTypes,
    setLoading,
    setAdding,
    setUpdating,
  } = useDevicesStore();

  const {
    getThings,
    addDevice: addThingApi,
    editThing: editThingApi,
    deleteThing: deleteThingApi,
  } = useThingsApi();

  const fetchDevicesIfNeeded = async () => {
    if (devices.length > 0) {
      return devices;
    }

    setLoading(true);
    try {
      const devicesData = await getThings();
      setDevices(devicesData);
      return devicesData;
    } catch (error) {
      console.error("Error fetching devices:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshDevices = async () => {
    setLoading(true);
    try {
      const devicesData = await getThings();
      setDevices(devicesData);
      return devicesData;
    } catch (error) {
      console.error("Error refreshing devices:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const addDevice = async (
    placeType: string,
    placeId: string,
    deviceData: Thing,
  ) => {
    setAdding(true);
    try {
      const result = await addThingApi(placeType, placeId, deviceData);
      if (result.success) {
        await refreshDevices();
        return {
          success: true,
          thingId: result.thingId,
        };
      }
      return { success: false, error: result.error };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : "Unknown error",
        },
      };
    } finally {
      setAdding(false);
    }
  };

  const editThing = async (
    placeType: string,
    placeId: string,
    thingId: string,
    thingData: Partial<Thing>,
  ) => {
    setUpdating(true);
    try {
      const result = await editThingApi(placeType, placeId, thingId, thingData);
      if (result.success) {
        await refreshDevices();
        return { success: true };
      }
      return { success: false, error: result.error };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error
              ? error.message
              : "An unexpected error occurred while updating the device",
        },
      };
    } finally {
      setUpdating(false);
    }
  };

  const deleteThing = async (
    placeType: string,
    placeId: string,
    thingId: string,
  ) => {
    setUpdating(true);
    try {
      const result = await deleteThingApi(placeType, placeId, thingId);
      if (result.success) {
        await refreshDevices();
        return { success: true };
      }
      return { success: false, error: result.error };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error
              ? error.message
              : "An unexpected error occurred while deleting the device",
        },
      };
    } finally {
      setUpdating(false);
    }
  };

  return {
    // Data
    devices,
    deviceTypes,
    // Loading states
    isLoading,
    isAdding,
    isUpdating,
    // Methods
    fetchDevicesIfNeeded,
    refreshDevices,
    addDevice,
    editThing,
    deleteThing,
  };
};
