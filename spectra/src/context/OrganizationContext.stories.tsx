import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { AuthProvider } from "./AuthContext";
import { OrganizationProvider, useOrganization } from "./OrganizationContext";

// Mock component to display organization data
const OrganizationDisplay = () => {
  const { organization, brandingConfig, isLoading, error } = useOrganization();

  if (isLoading) return <div>Loading organization data...</div>;
  if (error) return <div style={{ color: "red" }}>Error: {error}</div>;
  if (!organization) return <div>No organization data available</div>;

  return (
    <div style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}>
      <h2>Organization Information</h2>
      <div style={{ marginBottom: "20px" }}>
        <h3>Organization Details</h3>
        <p>
          <strong>Name:</strong> {organization.name}
        </p>
        <p>
          <strong>Display Name:</strong> {organization.displayName}
        </p>
        <p>
          <strong>Organization ID:</strong> {organization.organizationId}
        </p>
        <p>
          <strong>Is Root:</strong> {organization.isRoot ? "Yes" : "No"}
        </p>
        <p>
          <strong>Identity Provider:</strong>{" "}
          {organization.identityConfig.identityProvider}
        </p>
      </div>

      <div>
        <h3>Branding Configuration</h3>
        <p>
          <strong>Has Logo:</strong>{" "}
          {brandingConfig?.hasUploadedLogo ? "Yes" : "No"}
        </p>
        <p>
          <strong>Logo URL:</strong> {brandingConfig?.logoUrl || "None"}
        </p>
        <div style={{ marginBottom: "10px" }}>
          <strong>Primary Color:</strong>
          <span
            style={{
              backgroundColor: brandingConfig?.primaryColor,
              padding: "5px 10px",
              marginLeft: "10px",
              color: "white",
              borderRadius: "4px",
            }}
          >
            {brandingConfig?.primaryColor}
          </span>
        </div>
        <div>
          <strong>Secondary Color:</strong>
          <span
            style={{
              backgroundColor: brandingConfig?.secondaryColor,
              padding: "5px 10px",
              marginLeft: "10px",
              color: "white",
              borderRadius: "4px",
            }}
          >
            {brandingConfig?.secondaryColor}
          </span>
        </div>
      </div>
    </div>
  );
};

const meta: Meta<typeof OrganizationProvider> = {
  title: "Context/OrganizationProvider",
  component: OrganizationProvider,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "Context provider that manages organization and branding data from the identity platform API.",
      },
    },
  },
  decorators: [
    (Story) => (
      <AuthProvider>
        <Story />
      </AuthProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <OrganizationProvider>
      <OrganizationDisplay />
    </OrganizationProvider>
  ),
};

export const WithMockData: Story = {
  render: () => {
    // Mock the organization data for demonstration
    const mockOrganization = {
      organizationId: "org-12345",
      parentOrganizationId: null,
      childOrganizationIds: [],
      ancestryPath: "/org-12345",
      name: "aerovy-demo",
      displayName: "Aerovy Demo Organization",
      isRoot: true,
      isSoftDeleted: false,
      identityConfig: {
        identityProvider: "auth0",
        providerOrgId: "org_abc123",
      },
      brandingConfig: {
        hasUploadedLogo: true,
        logoUrl: "https://example.com/logo.png",
        primaryColor: "#3B82F6",
        secondaryColor: "#EF4444",
      },
      createdAt: "2024-01-01T00:00:00Z",
      createdBy: "system",
    };

    return (
      <div style={{ padding: "20px" }}>
        <h2>Mock Organization Data</h2>
        <div style={{ marginBottom: "20px" }}>
          <h3>Organization Details</h3>
          <p>
            <strong>Name:</strong> {mockOrganization.name}
          </p>
          <p>
            <strong>Display Name:</strong> {mockOrganization.displayName}
          </p>
          <p>
            <strong>Organization ID:</strong> {mockOrganization.organizationId}
          </p>
          <p>
            <strong>Is Root:</strong> {mockOrganization.isRoot ? "Yes" : "No"}
          </p>
          <p>
            <strong>Identity Provider:</strong>{" "}
            {mockOrganization.identityConfig.identityProvider}
          </p>
        </div>

        <div>
          <h3>Branding Configuration</h3>
          <p>
            <strong>Has Logo:</strong>{" "}
            {mockOrganization.brandingConfig.hasUploadedLogo ? "Yes" : "No"}
          </p>
          <p>
            <strong>Logo URL:</strong> {mockOrganization.brandingConfig.logoUrl}
          </p>
          <div style={{ marginBottom: "10px" }}>
            <strong>Primary Color:</strong>
            <span
              style={{
                backgroundColor: mockOrganization.brandingConfig.primaryColor,
                padding: "5px 10px",
                marginLeft: "10px",
                color: "white",
                borderRadius: "4px",
              }}
            >
              {mockOrganization.brandingConfig.primaryColor}
            </span>
          </div>
          <div>
            <strong>Secondary Color:</strong>
            <span
              style={{
                backgroundColor: mockOrganization.brandingConfig.secondaryColor,
                padding: "5px 10px",
                marginLeft: "10px",
                color: "white",
                borderRadius: "4px",
              }}
            >
              {mockOrganization.brandingConfig.secondaryColor}
            </span>
          </div>
        </div>
      </div>
    );
  },
};
