import { createContext, useContext, useEffect, useState } from "react";
import { useDevicesStore } from "stores/devicesStore";
import { useFleetsStore } from "stores/fleetsStore";
import { useIntegrationsStore } from "stores/integrationsStore";
import { useMonitorsStore } from "stores/monitorsStore";
import { useSitesStore } from "stores/sitesStore";
import { useAppData } from "../hooks/useAppData";

type JumpBarContextType = {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  isLoading: boolean;
};

const JumpBarContext = createContext<JumpBarContextType | undefined>(undefined);

export const JumpBarProvider = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isDataFetched, setIsDataFetched] = useState(false);

  const { fetchAllIfNeeded } = useAppData();

  // Get loading states from individual stores
  const sitesLoading = useSitesStore((state) => state.isLoading);
  const devicesLoading = useDevicesStore((state) => state.isLoading);
  const fleetsLoading = useFleetsStore((state) => state.isLoading);
  const integrationsLoading = useIntegrationsStore((state) => state.isLoading);
  const monitorsLoading = useMonitorsStore((state) => state.isLoading);

  const fetchData = async () => {
    if (isDataFetched) return;

    try {
      await fetchAllIfNeeded();
      setIsDataFetched(true);
    } catch (error) {
      console.error("Error fetching jump bar data:", error);
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchData is not dependency
  useEffect(() => {
    if (isOpen && !isDataFetched) {
      fetchData();
    }
  }, [isOpen]);

  return (
    <JumpBarContext.Provider
      value={{
        isOpen,
        setIsOpen,
        isLoading:
          sitesLoading ||
          devicesLoading ||
          fleetsLoading ||
          integrationsLoading ||
          monitorsLoading,
      }}
    >
      {children}
    </JumpBarContext.Provider>
  );
};

export const useJumpBar = () => {
  const context = useContext(JumpBarContext);
  if (!context) {
    throw new Error("useJumpBar must be used within a JumpBarProvider");
  }
  return context;
};
