import type React from "react";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import {
  type Organization,
  useIdentityPlatformApi,
} from "../api/ingestion/identityPlatform";
import { useAuth } from "./AuthContext";

type BrandingConfig = {
  hasUploadedLogo: boolean;
  logoUrl: string | null;
  primaryColor: string;
  secondaryColor: string;
};

type OrganizationContextType = {
  organization: Organization | null;
  brandingConfig: BrandingConfig | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
};

const OrganizationContext = createContext<OrganizationContextType | undefined>(
  undefined,
);

/**
 * Hook to access organization and branding data from the Identity Platform.
 *
 * @returns Organization context containing organization details, branding config, loading state, and error handling
 * @throws Error if used outside of OrganizationProvider
 *
 * @example
 * ```tsx
 * const { organization, brandingConfig, isLoading } = useOrganization();
 *
 * if (isLoading) return <Spinner />;
 * return <div style={{ color: brandingConfig?.primaryColor }}>
 *   {organization?.displayName}
 * </div>;
 * ```
 */
export const useOrganization = () => {
  const context = useContext(OrganizationContext);
  if (!context) {
    throw new Error(
      "useOrganization must be used within an OrganizationProvider",
    );
  }
  return context;
};

/**
 * Provider component that fetches and manages organization and branding data.
 *
 * Automatically fetches organization data when an auth token is available and provides
 * organization details, branding configuration, loading states, and error handling
 * to child components through the useOrganization hook.
 *
 * @param children - React components that will have access to organization context
 */
export const OrganizationProvider = ({
  children,
}: { children: React.ReactNode }) => {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [brandingConfig, setBrandingConfig] = useState<BrandingConfig | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { token } = useAuth();
  const { getUserPlatformOrganization } = useIdentityPlatformApi();

  // biome-ignore lint/correctness/useExhaustiveDependencies: getUserPlatformOrganization is not a dependency of the useCallback hook
  const fetchOrganizationData = useCallback(async () => {
    if (!token) return;

    setIsLoading(true);
    setError(null);

    try {
      const orgData = await getUserPlatformOrganization();
      setOrganization(orgData);
      setBrandingConfig(orgData.brandingConfig);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to fetch organization data";
      setError(errorMessage);
      console.error("Failed to fetch organization data:", err);
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  useEffect(() => {
    if (token) {
      fetchOrganizationData();
    } else {
      // Clear data when token is not available
      setOrganization(null);
      setBrandingConfig(null);
      setError(null);
    }
  }, [token, fetchOrganizationData]);

  return (
    <OrganizationContext.Provider
      value={{
        organization,
        brandingConfig,
        isLoading,
        error,
        refetch: fetchOrganizationData,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
};
