import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { dayjs } from "utils/dayjs";

import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { ReactComponent as ChevronDown } from "images/icons/chevron.down.svg";
import ButtonComponent from "../components/uikit/button";

const USER_TIME_ZONE_DISPLAY = dayjs().format("z");
const USER_TIME_ZONE = dayjs.tz.guess();

export enum TimeRangeOptions {
  Past24Hours = "Past Day",
  Today = "Today",
  Yesterday = "Yesterday",
  Past7d = "Past Week",
  YearToDate = "Year to Date",
  CustomRange = "Custom Range",
}

type SelectedTimeRangeContextType = {
  start: dayjs.Dayjs;
  end: dayjs.Dayjs;
  setStart: (start: dayjs.Dayjs) => void;
  setEnd: (end: dayjs.Dayjs) => void;
  isUTC: boolean;
  setIsUTC: (isUTC: boolean) => void;
  defaultTimeRange: TimeRangeOptions;
};

const SelectedTimeRangeContext = createContext<
  SelectedTimeRangeContextType | undefined
>(undefined);

export const useSelectedTimeRange = () => {
  const context = useContext(SelectedTimeRangeContext);
  if (!context) {
    throw new Error(
      "useSelectedTimeRange must be used within a SelectedTimeRangeProvider",
    );
  }
  return context;
};

// Helper function to calculate start and end dates for a given time range option
const calculateTimeRangeValues = (
  timeOption: TimeRangeOptions,
): { start: dayjs.Dayjs; end: dayjs.Dayjs } => {
  const now = dayjs();

  switch (timeOption) {
    case TimeRangeOptions.Past24Hours:
      return {
        start: now.subtract(1, "day"),
        end: now,
      };
    case TimeRangeOptions.Today:
      return {
        start: now.startOf("day"),
        end: now.endOf("day"),
      };
    case TimeRangeOptions.Yesterday: {
      const yesterday = now.subtract(1, "day");
      return {
        start: yesterday.startOf("day"),
        end: yesterday.endOf("day"),
      };
    }
    case TimeRangeOptions.Past7d:
      return {
        start: now.subtract(1, "week"),
        end: now,
      };
    case TimeRangeOptions.YearToDate:
      return {
        start: now.startOf("year"),
        end: now,
      };
    default:
      // For custom range or fallback, return a sensible default (past week)
      return {
        start: now.subtract(1, "week"),
        end: now,
      };
  }
};

export const SelectedTimeRangeProvider: React.FC<{
  children: React.ReactNode;
  defaultTimeRange?: TimeRangeOptions;
}> = ({ children, defaultTimeRange = TimeRangeOptions.Past7d }) => {
  const initialValues = calculateTimeRangeValues(defaultTimeRange);
  const [start, setStart] = useState(initialValues.start);
  const [end, setEnd] = useState(initialValues.end);
  const [isUTC, setIsUTC] = useState(false);

  return (
    <SelectedTimeRangeContext.Provider
      value={{
        start,
        end,
        setStart,
        setEnd,
        isUTC,
        setIsUTC,
        defaultTimeRange,
      }}
    >
      {children}
    </SelectedTimeRangeContext.Provider>
  );
};

const getFormattedTime = (
  timeRange: TimeRangeOptions,
  start: dayjs.Dayjs,
  end: dayjs.Dayjs,
  isUTC: boolean,
) => {
  if (timeRange !== TimeRangeOptions.CustomRange) {
    return timeRange;
  }

  // create a string in format [start.day] [start.time] - [end.day]? [end.time]

  const timezone: string = isUTC ? "UTC" : USER_TIME_ZONE_DISPLAY;
  if (start.date() === end.date()) {
    return `${start.format("lll")} - ${end.format("LT")} ${timezone}`;
  }

  return `${start.format("lll")} - ${end.format("lll")} ${timezone}`;
};

// Helper function to get preview text for preset ranges
const getPresetRangePreview = (
  timeOption: TimeRangeOptions,
  isUTC: boolean,
): string => {
  const now = dayjs();
  const timezone = isUTC ? "UTC" : USER_TIME_ZONE_DISPLAY;

  switch (timeOption) {
    case TimeRangeOptions.Past24Hours: {
      const start = now.subtract(1, "day");
      return `${start.format("MMM D, h:mm A")} - ${now.format(
        "MMM D, h:mm A",
      )} ${timezone}`;
    }
    case TimeRangeOptions.Today: {
      const start = now.startOf("day");
      const end = now.endOf("day");
      return `${start.format("MMM D, h:mm A")} - ${end.format(
        "h:mm A",
      )} ${timezone}`;
    }
    case TimeRangeOptions.Yesterday: {
      const yesterday = now.subtract(1, "day");
      const start = yesterday.startOf("day");
      const end = yesterday.endOf("day");
      return `${start.format("MMM D, h:mm A")} - ${end.format(
        "h:mm A",
      )} ${timezone}`;
    }
    case TimeRangeOptions.Past7d: {
      const start = now.subtract(1, "week");
      return `${start.format("MMM D, h:mm A")} - ${now.format(
        "MMM D, h:mm A",
      )} ${timezone}`;
    }
    case TimeRangeOptions.YearToDate: {
      const start = now.startOf("year");
      return `${start.format("MMM D, h:mm A")} - ${now.format(
        "MMM D, h:mm A",
      )} ${timezone}`;
    }
    default:
      return "";
  }
};

// Enhanced preset menu item component with hover preview
const PresetMenuItem = ({
  timeOption,
  selectedTimeRange,
  isUTC,
  onClick,
  children,
  isFocused = false,
  index,
}: {
  timeOption: TimeRangeOptions;
  selectedTimeRange: TimeRangeOptions;
  isUTC: boolean;
  onClick: () => void;
  children: React.ReactNode;
  isFocused?: boolean;
  index: number;
}) => {
  const [showPreview, setShowPreview] = useState(false);
  const previewText = getPresetRangePreview(timeOption, isUTC);

  return (
    <div
      className="relative"
      onMouseEnter={() => setShowPreview(true)}
      onMouseLeave={() => setShowPreview(false)}
    >
      <ButtonComponent.Menu
        className={`
          ${selectedTimeRange === timeOption ? "bg-blue90" : "hover:bg-gray95"}
          ${isFocused ? "bg-blue-100 ring-2 ring-blue-300" : ""}
          transition-colors duration-150
        `}
        onClick={onClick}
        role="menuitem"
        aria-label={`${children}. ${previewText}. ${
          selectedTimeRange === timeOption ? "Currently selected." : ""
        }`}
        tabIndex={isFocused ? 0 : -1}
      >
        <div className="flex flex-col">
          <span>{children}</span>
          {(showPreview || isFocused) && (
            <span className="text-xs text-gray-500 mt-1 animate-in fade-in duration-200">
              {previewText}
            </span>
          )}
        </div>
      </ButtonComponent.Menu>
    </div>
  );
};

export const TimeRangeSelector = () => {
  const { start, end, setStart, setEnd, isUTC, setIsUTC, defaultTimeRange } =
    useSelectedTimeRange();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [selectedTimeRangeDisplay, setSelectedTimeRangeDisplay] =
    useState<string>("");

  const [validationError, setValidationError] = useState<string>("");
  const [focusedPresetIndex, setFocusedPresetIndex] = useState<number>(-1);

  const menuRef = useRef<HTMLDivElement>(null);
  const triggerButtonRef = useRef<HTMLButtonElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced date change handler to prevent excessive re-renders
  const debouncedDateChange = useCallback(
    (field: "start" | "end", value: dayjs.Dayjs | null) => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      debounceTimeoutRef.current = setTimeout(() => {
        if (value) {
          setTimeRange(TimeRangeOptions.CustomRange, {
            autoclose: false,
            [field]: value,
          });
        }
      }, 300); // 300ms debounce
    },
    [],
  );

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const determineTimeRange = useCallback(() => {
    const now = dayjs();

    if (
      end.isSame(now, "hour") &&
      start.isSame(now.subtract(1, "day"), "hour")
    ) {
      return TimeRangeOptions.Past24Hours;
    }

    if (start.isSame(now.startOf("day")) && end.isSame(now.endOf("day"))) {
      return TimeRangeOptions.Today;
    }

    const yesterday = now.subtract(1, "day");
    if (
      start.isSame(yesterday.startOf("day")) &&
      end.isSame(yesterday.endOf("day"))
    ) {
      return TimeRangeOptions.Yesterday;
    }

    if (
      end.isSame(now, "hour") &&
      start.isSame(now.subtract(1, "week"), "hour")
    ) {
      return TimeRangeOptions.Past7d;
    }

    if (start.isSame(now.startOf("year")) && end.isSame(now, "hour")) {
      return TimeRangeOptions.YearToDate;
    }

    return TimeRangeOptions.CustomRange;
  }, [start, end]);

  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRangeOptions>(
    () => determineTimeRange(),
  );

  useEffect(() => {
    setSelectedTimeRange(determineTimeRange());
  }, [determineTimeRange]);

  // Memoize the formatted display to prevent unnecessary recalculations
  const memoizedDisplay = useMemo(() => {
    return getFormattedTime(selectedTimeRange, start, end, isUTC);
  }, [selectedTimeRange, start, end, isUTC]);

  useEffect(() => {
    setSelectedTimeRangeDisplay(memoizedDisplay);
  }, [memoizedDisplay]);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      menuRef.current &&
      !menuRef.current.contains(event.target as Node) &&
      !(event.target as Element).closest?.(".MuiPickersPopper-root") // Check if the click is inside the DateTimePicker
    ) {
      setIsMenuOpen(false);
    }
  };

  // Keyboard navigation handler
  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isMenuOpen) return;

    const presetOptions = [
      TimeRangeOptions.Past24Hours,
      TimeRangeOptions.Today,
      TimeRangeOptions.Yesterday,
      TimeRangeOptions.Past7d,
      TimeRangeOptions.YearToDate,
    ];

    switch (event.key) {
      case "Escape":
        setIsMenuOpen(false);
        triggerButtonRef.current?.focus();
        break;
      case "ArrowDown":
        event.preventDefault();
        setFocusedPresetIndex((prev) =>
          prev < presetOptions.length - 1 ? prev + 1 : 0,
        );
        break;
      case "ArrowUp":
        event.preventDefault();
        setFocusedPresetIndex((prev) =>
          prev > 0 ? prev - 1 : presetOptions.length - 1,
        );
        break;
      case "Enter":
        event.preventDefault();
        if (
          focusedPresetIndex >= 0 &&
          focusedPresetIndex < presetOptions.length
        ) {
          setTimeRange(presetOptions[focusedPresetIndex]);
        }
        break;
      case "t":
      case "T":
        if (event.ctrlKey || event.metaKey) return; // Don't interfere with browser shortcuts
        event.preventDefault();
        setTimeRange(TimeRangeOptions.Today);
        break;
      case "y":
      case "Y":
        if (event.ctrlKey || event.metaKey) return;
        event.preventDefault();
        setTimeRange(TimeRangeOptions.Yesterday);
        break;
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: adding handleClickOutside to the dependency array causes a re-render loop
  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isMenuOpen, focusedPresetIndex]);

  const setTimeRange = (
    timeOption: TimeRangeOptions,
    overrides: {
      isUTC?: boolean;
      autoclose?: boolean;
      start?: dayjs.Dayjs;
      end?: dayjs.Dayjs;
    } = {},
  ) => {
    const defaults = { isUTC: isUTC, autoclose: true, start: start, end: end };
    const options = Object.assign(defaults, overrides);

    // Clear any validation errors
    setValidationError("");

    // Validate date range
    if (options.start && options.end && options.start.isAfter(options.end)) {
      setValidationError("Start date must be before end date");
      return;
    }

    setSelectedTimeRange(timeOption);

    if (options.autoclose) {
      setIsMenuOpen(false);
    }

    // update the time(s) if necessary
    const now = dayjs();
    switch (timeOption) {
      case TimeRangeOptions.Past24Hours: {
        setIsUTC(options.isUTC);
        setStart(now.subtract(1, "day"));
        setEnd(now);
        break;
      }
      case TimeRangeOptions.Today: {
        setIsUTC(options.isUTC);
        setStart(now.startOf("day"));
        setEnd(now.endOf("day"));
        break;
      }
      case TimeRangeOptions.Yesterday: {
        setIsUTC(options.isUTC);
        const yesterday = now.subtract(1, "day");
        setStart(yesterday.startOf("day"));
        setEnd(yesterday.endOf("day"));
        break;
      }
      case TimeRangeOptions.Past7d: {
        setIsUTC(options.isUTC);
        setStart(now.subtract(1, "week"));
        setEnd(now);
        break;
      }
      case TimeRangeOptions.YearToDate: {
        setIsUTC(options.isUTC);
        setStart(now.startOf("year"));
        setEnd(now);
        break;
      }
      case TimeRangeOptions.CustomRange: {
        setIsUTC(options.isUTC);

        const newStart = options.isUTC
          ? options.start.tz("UTC")
          : options.start.tz(USER_TIME_ZONE);
        const newEnd = options.isUTC
          ? options.end.tz("UTC")
          : options.end.tz(USER_TIME_ZONE);
        setStart(newStart);
        setEnd(newEnd);

        break;
      }
    }

    // set formatted string
    const customRangeString = getFormattedTime(
      timeOption,
      options.start,
      options.end,
      options.isUTC,
    );
    setSelectedTimeRangeDisplay(customRangeString);
  };

  return (
    <div className="relative" ref={menuRef}>
      <ButtonComponent.Pill
        iconAfter={
          <div
            className={`transition-transform duration-200 ${
              isMenuOpen ? "rotate-180" : ""
            }`}
          >
            <ChevronDown />
          </div>
        }
        onClick={toggleMenu}
        aria-expanded={isMenuOpen}
        aria-haspopup="menu"
        aria-label={`Time range selector. Current selection: ${selectedTimeRangeDisplay}. Press Enter to open menu, or use keyboard shortcuts: T for Today, Y for Yesterday.`}
        className={`
          transition-all duration-200 ease-in-out
          ${
            selectedTimeRange === defaultTimeRange
              ? "border border-space80 hover:bg-gray95 text-space70 hover:border-gray-400"
              : "!bg-blue50 text-white !border-0 hover:bg-blue-600 shadow-sm"
          }
          ${isMenuOpen ? "ring-2 ring-blue-200 ring-opacity-50" : ""}
        `}
      >
        {selectedTimeRangeDisplay}
      </ButtonComponent.Pill>
      <div
        className={`
          origin-top-right w-[280px] absolute right-0 pb-1 mt-2
          border border-gray95 rounded-md shadow-lg bg-white divide-y
          focus:outline-none z-20
          transition-all duration-200 ease-out
          ${
            isMenuOpen
              ? "opacity-100 scale-100 translate-y-0"
              : "opacity-0 scale-95 translate-y-1 pointer-events-none"
          }
        `}
        role="menu"
        aria-orientation="vertical"
        aria-label="Time range selection menu"
        aria-live="polite"
        aria-atomic="true"
      >
        {/* Screen reader announcements */}
        <div className="sr-only" aria-live="polite" aria-atomic="true">
          {validationError && `Error: ${validationError}`}
          {isMenuOpen &&
            "Time range menu opened. Use arrow keys to navigate, Enter to select, or Escape to close."}
        </div>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          {/* Past 24h */}
          <PresetMenuItem
            timeOption={TimeRangeOptions.Past24Hours}
            selectedTimeRange={selectedTimeRange}
            isUTC={isUTC}
            onClick={() => setTimeRange(TimeRangeOptions.Past24Hours)}
            isFocused={focusedPresetIndex === 0}
            index={0}
          >
            Past Day
          </PresetMenuItem>

          {/* Today */}
          <PresetMenuItem
            timeOption={TimeRangeOptions.Today}
            selectedTimeRange={selectedTimeRange}
            isUTC={isUTC}
            onClick={() => setTimeRange(TimeRangeOptions.Today)}
            isFocused={focusedPresetIndex === 1}
            index={1}
          >
            Today
          </PresetMenuItem>

          {/* Yesterday */}
          <PresetMenuItem
            timeOption={TimeRangeOptions.Yesterday}
            selectedTimeRange={selectedTimeRange}
            isUTC={isUTC}
            onClick={() => setTimeRange(TimeRangeOptions.Yesterday)}
            isFocused={focusedPresetIndex === 2}
            index={2}
          >
            Yesterday
          </PresetMenuItem>

          {/* Past 7d */}
          <PresetMenuItem
            timeOption={TimeRangeOptions.Past7d}
            selectedTimeRange={selectedTimeRange}
            isUTC={isUTC}
            onClick={() => setTimeRange(TimeRangeOptions.Past7d)}
            isFocused={focusedPresetIndex === 3}
            index={3}
          >
            Past Week
          </PresetMenuItem>

          {/* Year to Date */}
          <PresetMenuItem
            timeOption={TimeRangeOptions.YearToDate}
            selectedTimeRange={selectedTimeRange}
            isUTC={isUTC}
            onClick={() => setTimeRange(TimeRangeOptions.YearToDate)}
            isFocused={focusedPresetIndex === 4}
            index={4}
          >
            Year to Date
          </PresetMenuItem>
          {/* Custom Range */}
          <div
            className={`px-4 w-full py-4 text-xs text-left border-t border-gray-100 ${
              selectedTimeRange === TimeRangeOptions.CustomRange
                ? "bg-blue90"
                : "hover:bg-gray95"
            }`}
          >
            <div className="mb-3">
              <p className="font-medium text-sm">Custom Range</p>
            </div>

            {/* Validation Error */}
            {validationError && (
              <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                {validationError}
              </div>
            )}

            {/* Start Date Section */}
            <div className="mb-3">
              <div className="mb-2">
                <label
                  htmlFor="start-date-picker"
                  className="text-sm font-medium text-gray-700"
                >
                  Start Date & Time
                </label>
              </div>
              <div>
                {isMenuOpen && (
                  <DateTimePicker
                    value={start}
                    onChange={(value) => debouncedDateChange("start", value)}
                    maxDateTime={end}
                    slotProps={{
                      textField: {
                        id: "start-date-picker",
                        size: "small",
                        placeholder: "Select start date & time",
                        "aria-label": "Start date and time",
                      },
                    }}
                    closeOnSelect={true}
                    timezone={isUTC ? "UTC" : "system"}
                  />
                )}
              </div>
            </div>

            {/* Visual Connector */}
            <div className="flex items-center justify-center my-2">
              <div className="w-8 h-px bg-gray-300" />
              <span className="mx-2 text-xs text-gray-500 bg-white px-2">
                to
              </span>
              <div className="w-8 h-px bg-gray-300" />
            </div>

            {/* End Date Section */}
            <div className="mb-4">
              <div className="mb-2">
                <label
                  htmlFor="end-date-picker"
                  className="text-sm font-medium text-gray-700"
                >
                  End Date & Time
                </label>
              </div>
              <div>
                {isMenuOpen && (
                  <DateTimePicker
                    value={end}
                    onChange={(value) => debouncedDateChange("end", value)}
                    onClose={() => setIsMenuOpen(false)}
                    minDateTime={start}
                    slotProps={{
                      textField: {
                        id: "end-date-picker",
                        size: "small",
                        placeholder: "Select end date & time",
                        "aria-label": "End date and time",
                      },
                    }}
                    closeOnSelect={true}
                    timezone={isUTC ? "UTC" : "system"}
                  />
                )}
              </div>
            </div>

            {/* Timezone Toggle */}
            <div className="border-t border-gray-200 pt-3">
              <p className="text-xs text-gray-600 mb-2">Timezone</p>
              <div className="inline-flex w-full border border-gray-200 rounded-md overflow-hidden">
                <ButtonComponent.Option
                  className={`${
                    isUTC
                      ? "bg-blue-500 text-white"
                      : "hover:bg-gray-50 bg-white text-gray-700"
                  } transition-colors duration-150`}
                  onClick={() => {
                    setTimeRange(TimeRangeOptions.CustomRange, { isUTC: true });
                  }}
                >
                  UTC
                </ButtonComponent.Option>
                <ButtonComponent.Option
                  className={`${
                    !isUTC
                      ? "bg-blue-500 text-white"
                      : "hover:bg-gray-50 bg-white text-gray-700"
                  } transition-colors duration-150`}
                  onClick={() => {
                    setTimeRange(TimeRangeOptions.CustomRange, {
                      isUTC: false,
                    });
                  }}
                >
                  Local ({USER_TIME_ZONE_DISPLAY})
                </ButtonComponent.Option>
              </div>
            </div>
          </div>
        </LocalizationProvider>
      </div>
    </div>
  );
};
