import type { Monitor } from "api/enterprise/monitors";
import type { IntegrationResponse } from "api/ingestion/integrations";
import Fuse from "fuse.js";
import { useAppData } from "hooks/useAppData";
import {
  type KeyboardEvent as ReactKeyboardEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useNavigate } from "react-router-dom";
import type { FleetResponse, SiteResponse } from "../api/ingestion/places";
import type { Thing } from "../api/ingestion/things";
import { useJumpBar } from "../context/JumpBarContext";
import { useSelectedDevice } from "../context/SelectedDeviceContext";
import { SearchModal } from "./uikit/SearchModal/SearchModal";
import { SearchResultSection } from "./uikit/SearchResultSection/SearchResultSection";

export const JumpBar = () => {
  const { setSelectedDevice } = useSelectedDevice();
  const { isOpen, setIsOpen, isLoading } = useJumpBar();
  const [search, setSearch] = useState("");
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const { sites, devices, fleets, monitors, integrations } = useAppData();

  const navigate = useNavigate();

  const resultsContainerRef = useRef<HTMLDivElement>(null);
  const selectedItemRef = useRef<HTMLDivElement>(null);

  const fuseOptions = {
    threshold: 0.6, // Lower threshold means more strict matching (0.0 is exact match)
    ignoreLocation: true,
    useExtendedSearch: true,
    findAllMatches: true,
    minMatchCharLength: 2,
    distance: 100,
    includeScore: true,
    keys: [],
  };

  const sitesFuse = useMemo(() => {
    return new Fuse(sites, {
      ...fuseOptions,
      keys: ["siteName", "siteId"],
    });
  }, [sites]);

  const devicesFuse = useMemo(() => {
    return new Fuse(devices, {
      ...fuseOptions,
      keys: ["thingName", "thingId"],
    });
  }, [devices]);

  const fleetsFuse = useMemo(() => {
    return new Fuse(fleets, {
      ...fuseOptions,
      keys: ["fleetName", "fleetId"],
    });
  }, [fleets]);

  const monitorsFuse = useMemo(() => {
    return new Fuse(monitors, {
      ...fuseOptions,
      keys: ["monitorName", "monitorId"],
    });
  }, [monitors]);

  const integrationsFuse = useMemo(() => {
    return new Fuse(integrations, {
      ...fuseOptions,
      keys: ["integrationName", "integrationId"],
    });
  }, [integrations]);

  const filteredResults = () => {
    if (!search.trim()) {
      // If search is empty, return all items
      return {
        sites: sites.slice(0, 10),
        fleets: fleets.slice(0, 10),
        devices: devices.slice(0, 10),
        monitors: monitors.slice(0, 10),
        integrations: integrations.slice(0, 10),
      };
    }

    // Get search results and sort by score (lower is better)
    const siteResults = sitesFuse.search(search);
    const deviceResults = devicesFuse.search(search);
    const fleetResults = fleetsFuse.search(search);
    const monitorResults = monitorsFuse.search(search);
    const integrationResults = integrationsFuse.search(search);

    return {
      sites: siteResults
        .sort((a, b) => (a.score || 1) - (b.score || 1))
        .map((result) => result.item),
      fleets: fleetResults
        .sort((a, b) => (a.score || 1) - (b.score || 1))
        .map((result) => result.item),
      devices: deviceResults
        .sort((a, b) => (a.score || 1) - (b.score || 1))
        .map((result) => result.item),
      monitors: monitorResults
        .sort((a, b) => (a.score || 1) - (b.score || 1))
        .map((result) => result.item),
      integrations: integrationResults
        .sort((a, b) => (a.score || 1) - (b.score || 1))
        .map((result) => result.item),
    };
  };

  const {
    sites: filteredSites,
    fleets: filteredFleets,
    devices: filteredDevices,
    monitors: filteredMonitors,
    integrations: filteredIntegrations,
  } = filteredResults();

  // biome-ignore lint/correctness/useExhaustiveDependencies: need to reset on search change
  useEffect(() => {
    setSelectedIndex(-1);
  }, [search]);

  useEffect(() => {
    if (isOpen) {
      setSelectedIndex(-1);
    }
  }, [isOpen]);

  // Import the SearchResultItemData type
  type SearchResultItem<T> = {
    id: string;
    name: string;
    type?: string;
    originalItem: T;
    [key: string]: unknown;
  };

  // Create mapped items for each result type
  const siteItems = useMemo(
    () =>
      filteredSites.map(
        (site) =>
          ({
            id: site.siteId,
            name: site.siteName,
            originalItem: site,
          }) as SearchResultItem<SiteResponse>,
      ),
    [filteredSites],
  );

  const fleetItems = useMemo(
    () =>
      filteredFleets.map(
        (fleet) =>
          ({
            id: fleet.fleetId,
            name: fleet.fleetName,
            originalItem: fleet,
          }) as SearchResultItem<FleetResponse>,
      ),
    [filteredFleets],
  );

  const deviceItems = useMemo(
    () =>
      filteredDevices.map(
        (device) =>
          ({
            id: device.thingId,
            name: device.thingName,
            type: device.thingType,
            originalItem: device,
          }) as SearchResultItem<Thing>,
      ),
    [filteredDevices],
  );

  const monitorItems = useMemo(
    () =>
      filteredMonitors.map(
        (monitor) =>
          ({
            id: monitor.monitorId,
            name: monitor.monitorName,
            originalItem: monitor,
          }) as SearchResultItem<Monitor>,
      ),
    [filteredMonitors],
  );

  const integrationItems = useMemo(
    () =>
      filteredIntegrations.map(
        (integration) =>
          ({
            id: integration.integrationId,
            name: integration.integrationName,
            originalItem: integration,
          }) as SearchResultItem<IntegrationResponse>,
      ),
    [filteredIntegrations],
  );

  // Define a type for search result section configuration
  type SearchResultSectionConfig<T> = {
    title: string;
    items: SearchResultItem<T>[];
    baseIndex: number;
    showTypeIcon?: boolean;
  };

  // Create configurations for each search result section
  const searchResultSections: SearchResultSectionConfig<
    SiteResponse | FleetResponse | Thing
  >[] = [
    {
      title: "Sites",
      items: siteItems,
      baseIndex: 0,
      showTypeIcon: false,
    },
    {
      title: "Fleets",
      items: fleetItems,
      baseIndex: siteItems.length,
      showTypeIcon: false,
    },
    {
      title: "Devices",
      items: deviceItems,
      baseIndex: siteItems.length + fleetItems.length,
      showTypeIcon: true,
    },
    {
      title: "Monitors",
      items: monitorItems,
      baseIndex: siteItems.length + fleetItems.length + deviceItems.length,
      showTypeIcon: false,
    },
    {
      title: "Integrations",
      items: integrationItems,
      baseIndex:
        siteItems.length +
        fleetItems.length +
        deviceItems.length +
        monitorItems.length,
      showTypeIcon: false,
    },
  ];

  // Memoize the combined results to avoid recalculating on every render
  const allFilteredItems = useMemo(() => {
    return [
      ...siteItems,
      ...fleetItems,
      ...deviceItems,
      ...monitorItems,
      ...integrationItems,
    ];
  }, [siteItems, fleetItems, deviceItems, monitorItems, integrationItems]);

  // Function to handle item selection
  const handleItemSelection = useCallback(
    (item: SearchResultItem<SiteResponse | FleetResponse | Thing>) => {
      const originalItem = item.originalItem;

      if ("thingId" in originalItem) {
        navigate("/devices");
        setSelectedDevice(originalItem);
      } else if ("siteId" in originalItem) {
        navigate(`/sites/${originalItem.siteId}`);
      } else if ("fleetId" in originalItem) {
        navigate(`/fleets/${originalItem.fleetId}`);
      } else if ("monitorId" in originalItem) {
        navigate(
          `/monitors/${originalItem.resourceId}/${originalItem.monitorId}`,
        );
      } else if ("integrationId" in originalItem) {
        navigate(`/integrations/${originalItem.integrationId}`);
      }

      setIsOpen(false);
    },
    [navigate, setIsOpen, setSelectedDevice],
  );

  const handleKeyNavigation = useCallback(
    (e: ReactKeyboardEvent<HTMLInputElement>) => {
      if (!isOpen) return;

      const allItems = allFilteredItems;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setSelectedIndex((prev) =>
            prev < allItems.length - 1 ? prev + 1 : prev,
          );
          break;
        case "ArrowUp":
          e.preventDefault();
          setSelectedIndex((prev) => (prev > -1 ? prev - 1 : -1));
          break;
        case "Enter":
          e.preventDefault();
          if (selectedIndex >= 0 && selectedIndex < allItems.length) {
            const selectedItem = allItems[selectedIndex];
            handleItemSelection(selectedItem);
          }
          break;
      }
    },
    [isOpen, allFilteredItems, selectedIndex, handleItemSelection],
  );

  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault();
        setIsOpen(true);
      } else if (e.key === "Escape" && isOpen) {
        e.preventDefault();
        e.stopPropagation();
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleGlobalKeyDown);
    return () => document.removeEventListener("keydown", handleGlobalKeyDown);
  }, [setIsOpen, isOpen]);

  useEffect(() => {
    if (
      selectedIndex >= 0 &&
      selectedItemRef.current &&
      resultsContainerRef.current
    ) {
      selectedItemRef.current.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });
    }
  }, [selectedIndex]);

  if (!isOpen) return null;

  // Handle item key down
  const handleItemKeyDown = (
    e: React.KeyboardEvent,
    item: SearchResultItem<SiteResponse | FleetResponse | Thing>,
  ) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleItemSelection(item);
    }
  };

  // Search input component
  const searchInput = (
    <input
      type="text"
      className="w-full px-4 py-2 text-lg border rounded-lg"
      placeholder="Search sites, fleets and devices..."
      value={search}
      onChange={(e) => setSearch(e.target.value)}
      onKeyDown={handleKeyNavigation}
      // biome-ignore lint/a11y/noAutofocus: need autofocus
      autoFocus
    />
  );

  // Keyboard shortcuts
  const shortcuts = [
    { key: "↑↓", description: "to navigate" },
    { key: "enter", description: "to select" },
    { key: "esc", description: "to close" },
  ];

  // Function to render a search result section
  const renderSearchResultSection = <
    T extends SiteResponse | FleetResponse | Thing,
  >(
    config: SearchResultSectionConfig<T>,
  ) => (
    <SearchResultSection
      key={config.title}
      title={config.title}
      items={config.items}
      search={search}
      selectedIndex={selectedIndex}
      baseIndex={config.baseIndex}
      selectedItemRef={selectedItemRef}
      onItemClick={handleItemSelection}
      onItemKeyDown={handleItemKeyDown}
      showDeviceType={config.showTypeIcon}
    />
  );

  return (
    <SearchModal
      isOpen={isOpen}
      searchInput={searchInput}
      resultsContainerRef={resultsContainerRef}
      isLoading={isLoading}
      search={search}
      shortcuts={shortcuts}
      noResultsMessage="No results found for"
    >
      {searchResultSections.map(renderSearchResultSection)}
    </SearchModal>
  );
};
