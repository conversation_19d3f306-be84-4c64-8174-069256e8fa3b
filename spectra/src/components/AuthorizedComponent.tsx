import NoAccessPage from "pages/noAccess/NoAccessPage";
import type { ReactNode } from "react";
import { useAuth } from "../context/AuthContext";

type AuthorizedComponentProps = {
  /** The permission required to show the component */
  requiredPermission: string;
  /** The component(s) to render if authorized */
  children: ReactNode;
  /** Optional fallback component to render if not authorized */
  fallback?: ReactNode;
};

/**
 * Higher Order Component that conditionally renders children based on user permissions
 *
 * @example
 * ```tsx
 * <AuthorizedComponent requiredPermission="write:ingest_admin">
 *   <ButtonComponent>Admin Only Button</ButtonComponent>
 * </AuthorizedComponent>
 * ```
 *
 * @example
 * ```tsx
 * <AuthorizedComponent
 *   requiredPermission="read:ingest_admin"
 *   fallback={<div>Insufficient permissions</div>}
 * >
 *   <AdminPanel />
 * </AuthorizedComponent>
 * ```
 */
export const AuthorizedComponent = ({
  requiredPermission,
  children,
  fallback = <NoAccessPage />,
}: AuthorizedComponentProps) => {
  const { permissions } = useAuth();
  const authorized = permissions.includes(requiredPermission);

  if (!authorized) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Hook version for use within components that need conditional logic
 *
 * @example
 * ```tsx
 * const MyComponent = () => {
 *   const canEdit = useAuthorization("write:ingest_admin");
 *
 *   return (
 *     <div>
 *       <ViewButton />
 *       {canEdit && <EditButton />}
 *     </div>
 *   );
 * };
 * ```
 */
export const useAuthorization = (requiredPermission: string): boolean => {
  const { permissions } = useAuth();
  return permissions.includes(requiredPermission);
};
