import "@testing-library/jest-dom";
import { fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import TextField from "./textField";

describe("TextField", () => {
  describe("Basic TextField", () => {
    const mockOnChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
    });

    it("renders with label and handles input changes", () => {
      render(<TextField label="Test Label" value="" onChange={mockOnChange} />);

      // Check if label is rendered
      expect(screen.getByText("Test Label")).toBeInTheDocument();

      // Get the input element
      const input = screen.getByRole("textbox");

      // Simulate user typing
      fireEvent.change(input, { target: { value: "test input" } });

      // Verify onChange was called with correct value
      expect(mockOnChange).toHaveBeenCalledWith("test input");
    });

    it("disables input when disabled prop is true", () => {
      render(
        <TextField
          label="Test Label"
          value=""
          onChange={mockOnChange}
          disabled={true}
        />,
      );

      const input = screen.getByRole("textbox");
      expect(input).toBeDisabled();
    });

    it("applies custom className when provided", () => {
      render(
        <TextField
          label="Test Label"
          value=""
          onChange={mockOnChange}
          className="custom-class"
        />,
      );

      const container = screen.getByText("Test Label").parentElement;
      expect(container).toHaveClass("custom-class");
    });
  });

  describe("Required TextField", () => {
    const mockOnChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
    });

    it("renders with required indicator and placeholder", () => {
      render(
        <TextField
          label="Required Field"
          value=""
          onChange={mockOnChange}
          required={true}
          placeholder="Enter text here"
        />,
      );

      // Check if label and required indicator are rendered
      expect(screen.getByText("Required Field")).toBeInTheDocument();
      expect(screen.getByText("*")).toBeInTheDocument();

      // Check if placeholder is rendered
      const input = screen.getByPlaceholderText("Enter text here");
      expect(input).toBeInTheDocument();

      // Verify required attribute is set
      expect(input).toBeRequired();
    });

    it("shows error message when error prop is provided", () => {
      render(
        <TextField
          label="Required Field"
          value=""
          onChange={mockOnChange}
          required={true}
          error="This field is required"
        />,
      );

      // Check if error message is rendered
      expect(screen.getByText("This field is required")).toBeInTheDocument();

      // Check if error styling is applied
      const container = screen.getByText("Required Field").parentElement;
      expect(container).toHaveClass("outline-red-500");
    });
  });
});
