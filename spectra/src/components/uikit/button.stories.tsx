import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import ButtonComponent from "./button";

const meta = {
  title: "UIKit/Button",
  component: ButtonComponent,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof ButtonComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample icon for demonstrations
const SampleIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    aria-labelledby="closeIconTitle"
  >
    <title id="closeIconTitle">Close Icon</title>
    <path d="M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z" />
  </svg>
);

// Base Button Stories
export const Default: Story = {
  args: {
    children: "Button",
    className: "bg-blue50 text-white px-4 py-2 rounded-md",
  },
};

export const BaseButtonWithIcon: Story = {
  args: {
    children: "Button with Icon",
    iconBefore: <SampleIcon />,
    className:
      "bg-blue50 text-white px-4 py-2 rounded-md flex items-center gap-2",
  },
};

// Pill Button Stories
export const PillButton: Story = {
  args: {
    children: "Pill Button",
  },
  render: (args) => <ButtonComponent.Pill {...args} />,
};

export const PillButtonWithIcon: Story = {
  args: {
    children: "Pill with Icon",
    iconBefore: <SampleIcon />,
  },
  render: (args) => <ButtonComponent.Pill {...args} />,
};

// Menu Button Stories
export const MenuButton: Story = {
  args: {
    children: "Menu Item",
    className: "hover:bg-blue95",
  },
  render: (args) => <ButtonComponent.Menu {...args} />,
};

export const MenuButtonWithIcon: Story = {
  args: {
    children: "Menu with Icon",
    iconBefore: <SampleIcon />,
    className: "hover:bg-blue95",
  },
  render: (args) => <ButtonComponent.Menu {...args} />,
};

// Option Button Stories
export const OptionButton: Story = {
  args: {
    children: "Option",
    className: "hover:bg-blue95",
  },
  render: (args) => <ButtonComponent.Option {...args} />,
};

export const OptionButtonWithIcon: Story = {
  args: {
    children: "Option with Icon",
    iconBefore: <SampleIcon />,
    className: "hover:bg-blue95",
  },
  render: (args) => <ButtonComponent.Option {...args} />,
};
