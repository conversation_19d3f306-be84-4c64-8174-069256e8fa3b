import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { useEffect, useState } from "react";
import { dayjs } from "utils/dayjs";

import {
    SelectedTimeRangeProvider,
    TimeRangeSelector,
    useSelectedTimeRange
} from "../../context/SelectedTimeRangeContext";

// Wrapper component to provide context and demonstrate state changes
const TimeRangeSelectorWithContext = ({
  initialStart = dayjs().subtract(1, "week"),
  initialEnd = dayjs(),
  initialIsUTC = false,
  showState = false,
  showValidationError = false
}) => {
  const [validationError, setValidationError] = useState<string>("");

  useEffect(() => {
    if (showValidationError) {
      setValidationError("Start date must be before end date");
    } else {
      setValidationError("");
    }
  }, [showValidationError]);

  return (
    <SelectedTimeRangeProvider>
      <div className="flex flex-col gap-4">
        <TimeRangeSelector />

        {showState && <TimeRangeSelectorState />}

        {validationError && (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
            {validationError}
          </div>
        )}

        <div className="text-xs text-gray-500 mt-4">
          <h3 className="font-medium mb-1">Keyboard Shortcuts:</h3>
          <ul className="list-disc pl-4">
            <li><kbd className="px-1 py-0.5 bg-gray-100 border border-gray-300 rounded">T</kbd> - Select Today</li>
            <li><kbd className="px-1 py-0.5 bg-gray-100 border border-gray-300 rounded">Y</kbd> - Select Yesterday</li>
            <li><kbd className="px-1 py-0.5 bg-gray-100 border border-gray-300 rounded">↑/↓</kbd> - Navigate menu items</li>
            <li><kbd className="px-1 py-0.5 bg-gray-100 border border-gray-300 rounded">Enter</kbd> - Select focused item</li>
            <li><kbd className="px-1 py-0.5 bg-gray-100 border border-gray-300 rounded">Esc</kbd> - Close menu</li>
          </ul>
        </div>
      </div>
    </SelectedTimeRangeProvider>
  );
};

// Component to display the current state of the TimeRangeSelector
const TimeRangeSelectorState = () => {
  const { start, end, isUTC } = useSelectedTimeRange();

  return (
    <div className="p-3 bg-gray-50 border border-gray-200 rounded text-xs">
      <h3 className="font-medium mb-2">Current State:</h3>
      <div className="grid grid-cols-2 gap-2">
        <div className="font-medium">Start:</div>
        <div>{start.format("YYYY-MM-DD HH:mm:ss")}</div>
        <div className="font-medium">End:</div>
        <div>{end.format("YYYY-MM-DD HH:mm:ss")}</div>
        <div className="font-medium">Timezone:</div>
        <div>{isUTC ? "UTC" : "Local"}</div>
      </div>
    </div>
  );
};

const meta = {
  title: "UIKit/TimeRangeSelector",
  component: TimeRangeSelectorWithContext,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
# TimeRangeSelector

A component for selecting date/time ranges with preset options and custom range selection.

## Features
- Preset options (Past Day, Today, Yesterday, Past Week)
- Custom range selection with date/time pickers
- Timezone toggle (UTC/Local)
- Keyboard navigation and shortcuts
- Screen reader support
- Hover previews showing actual date ranges

## Accessibility
- Full keyboard navigation
- ARIA labels and live regions
- Focus management
- Screen reader announcements
`
      }
    }
  },
  argTypes: {
    showState: {
      control: 'boolean',
      description: 'Show the current state of the TimeRangeSelector',
      defaultValue: false
    },
    showValidationError: {
      control: 'boolean',
      description: 'Show a validation error example',
      defaultValue: false
    },
    initialStart: {
      control: 'date',
      description: 'Initial start date',
    },
    initialEnd: {
      control: 'date',
      description: 'Initial end date',
    },
    initialIsUTC: {
      control: 'boolean',
      description: 'Initial timezone setting (UTC or Local)',
      defaultValue: false
    }
  },
  tags: ["autodocs"],
} satisfies Meta<typeof TimeRangeSelectorWithContext>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story with Past Week selected
export const Default: Story = {
  args: {
    showState: true,
    showValidationError: false,
    initialStart: dayjs().subtract(1, "week").toDate(),
    initialEnd: dayjs().toDate(),
    initialIsUTC: false
  }
};

// Story showing the menu open
export const MenuOpen: Story = {
  args: {
    ...Default.args,
    showState: true
  },
  parameters: {
    docs: {
      description: {
        story: 'TimeRangeSelector with dropdown menu open, showing preset options with hover previews.'
      }
    }
  },
  play: async ({ canvasElement }) => {
    // This would normally open the menu, but we can't automate this in Storybook easily
    // In a real implementation, we would use testing-library to click the button
  }
};

// Story showing a custom range selected
export const CustomRangeSelected: Story = {
  args: {
    ...Default.args,
    initialStart: dayjs().subtract(2, "month").toDate(),
    initialEnd: dayjs().subtract(1, "month").toDate(),
  },
  parameters: {
    docs: {
      description: {
        story: 'TimeRangeSelector with a custom date range selected.'
      }
    }
  }
};

// Story showing UTC timezone selected
export const UTCTimezone: Story = {
  args: {
    ...Default.args,
    initialIsUTC: true
  },
  parameters: {
    docs: {
      description: {
        story: 'TimeRangeSelector with UTC timezone selected instead of local time.'
      }
    }
  }
};

// Story showing validation error
export const ValidationError: Story = {
  args: {
    ...Default.args,
    showValidationError: true
  },
  parameters: {
    docs: {
      description: {
        story: 'TimeRangeSelector showing a validation error when start date is after end date.'
      }
    }
  }
};

// Story focusing on accessibility features
export const AccessibilityFeatures: Story = {
  args: {
    ...Default.args,
    showState: true
  },
  parameters: {
    docs: {
      description: {
        story: `
## Accessibility Features

The TimeRangeSelector includes the following accessibility features:

1. **Keyboard Navigation**
   - Arrow keys to navigate menu items
   - Enter to select the focused item
   - Escape to close the menu
   - Keyboard shortcuts: T for Today, Y for Yesterday

2. **ARIA Attributes**
   - aria-expanded to indicate menu state
   - aria-haspopup to indicate dropdown functionality
   - aria-label with descriptive text
   - aria-live regions for dynamic content

3. **Focus Management**
   - Proper focus trapping within the menu
   - Visual focus indicators
   - Return focus to trigger button on close

4. **Screen Reader Support**
   - Announcements for menu open/close
   - Descriptive labels for all interactive elements
   - Error announcements
        `
      }
    }
  }
};

// Story showing different preset options
export const PresetOptions: Story = {
  args: {
    ...Default.args,
    showState: true
  },
  parameters: {
    docs: {
      description: {
        story: `
## Preset Options

The TimeRangeSelector provides several preset options:

- **Past Day**: Last 24 hours from now
- **Today**: From start of today to end of today
- **Yesterday**: From start of yesterday to end of yesterday
- **Past Week**: Last 7 days from now

Each preset option shows a hover preview with the actual date range that will be selected.
        `
      }
    }
  }
};

// Story demonstrating the custom range functionality
export const CustomRangeDemo: Story = {
  args: {
    ...Default.args,
    showState: true
  },
  parameters: {
    docs: {
      description: {
        story: `
## Custom Range Selection

The custom range section allows users to:

1. Select a start date and time
2. Select an end date and time
3. Toggle between UTC and Local timezone
4. See validation feedback for invalid ranges

The UI has been cleaned up to remove visual clutter while maintaining full functionality.
        `
      }
    }
  }
};

// Interactive playground story
export const InteractivePlayground: Story = {
  args: {
    showState: true,
    showValidationError: false,
    initialStart: dayjs().subtract(1, "week").toDate(),
    initialEnd: dayjs().toDate(),
    initialIsUTC: false
  },
  parameters: {
    docs: {
      description: {
        story: `
## Interactive Playground

Use the controls below to test different configurations of the TimeRangeSelector:

- Toggle state visibility
- Show/hide validation errors
- Change initial dates
- Switch between UTC and Local timezone

Try the keyboard shortcuts and accessibility features!
        `
      }
    }
  }
};
