import type { ReactNode } from "react";

type ToggleProps = {
  leftLabel?: string;
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  description?: ReactNode;
  disabled?: boolean;
};

const Toggle = ({
  leftLabel,
  label,
  checked,
  onChange,
  description,
  disabled = false,
}: ToggleProps) => {
  return (
    <div className="flex items-center gap-3 my-1">
      {leftLabel && (
        <span className="text-sm font-medium text-space60">{leftLabel}</span>
      )}
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        disabled={disabled}
        onClick={() => onChange(!checked)}
        className={`
          relative inline-flex h-6 w-11 min-w-[44px] items-center rounded-full
          transition-colors focus:outline-none focus:ring-2 focus:ring-blue50 focus:ring-offset-2
          ${checked ? "bg-blue50" : "bg-gray90"}
          ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
        `}
      >
        <span
          className={`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform
            ${checked ? "translate-x-6" : "translate-x-1"}
          `}
        />
      </button>
      <div>
        <span className="text-sm font-medium text-space60">{label}</span>
        {description && <p className="text-xs text-space70">{description}</p>}
      </div>
    </div>
  );
};

export { Toggle };
