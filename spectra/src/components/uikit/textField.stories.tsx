import type { Meta, StoryObj } from '@storybook/react';
import TextField from './textField';
import { useState } from 'react';

const meta = {
  title: 'UIKit/TextField',
  component: TextField,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof TextField>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic TextField
export const Basic: Story = {
  args: {
    label: 'Name',
    value: '',
    placeholder: 'Enter your name',
  },
};

// Required TextField
export const Required: Story = {
  args: {
    label: 'Email',
    value: '',
    placeholder: 'Enter your email',
    type: 'email',
    required: true,
  },
};

// With Error
export const WithError: Story = {
  args: {
    label: 'Password',
    value: '123',
    type: 'password',
    error: 'Password must be at least 8 characters',
  },
};

// Disabled TextField
export const Disabled: Story = {
  args: {
    label: 'Username',
    value: 'johndoe',
    disabled: true,
  },
};

// Interactive TextField Example
const InteractiveTextFieldExample = () => {
  const [value, setValue] = useState('');
  const [error, setError] = useState('');
  
  const handleChange = (newValue: string) => {
    setValue(newValue);
    
    if (newValue.length < 3 && newValue.length > 0) {
      setError('Input must be at least 3 characters');
    } else {
      setError('');
    }
  };
  
  return (
    <div className="w-80">
      <TextField
        label="Interactive Input"
        value={value}
        placeholder="Type something..."
        onChange={handleChange}
        error={error}
      />
      
      <div className="mt-4 text-sm text-space50">
        Current value: {value ? `"${value}"` : '(empty)'}
      </div>
    </div>
  );
};

export const Interactive: Story = {
  render: () => <InteractiveTextFieldExample />,
};
