export interface HighlightedTextProps {
  text: string;
  query: string;
  highlightClassName?: string;
}

/**
 * HighlightedText component highlights matching text in search results
 */
export const HighlightedText = ({
  text,
  query,
  highlightClassName = "bg-blue95 text-blue40",
}: HighlightedTextProps): JSX.Element => {
  if (!query.trim()) return <>{text}</>;

  try {
    const regex = new RegExp(
      `(${query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
      "gi",
    );
    const parts = text.split(regex);

    return (
      <>
        {parts.map((part, i) =>
          regex.test(part) ? (
            <span
              // biome-ignore lint/suspicious/noArrayIndexKey: no other unique identifier
              key={`${part}-${i}`}
              className={highlightClassName}
            >
              {part}
            </span>
          ) : (
            // biome-ignore lint/suspicious/noArrayIndexKey: no other unique identifier
            <span key={`${part}-${i}`}>{part}</span>
          ),
        )}
      </>
    );
  } catch (e) {
    // If regex fails, return the original text
    return <>{text}</>;
  }
};

export default HighlightedText;
