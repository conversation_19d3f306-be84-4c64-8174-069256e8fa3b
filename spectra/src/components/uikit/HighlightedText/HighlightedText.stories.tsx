import type { <PERSON>a, StoryObj } from "@storybook/react";
import { HighlightedText } from "./HighlightedText";

const meta: Meta<typeof HighlightedText> = {
  title: "UIKit/HighlightedText",
  component: HighlightedText,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    text: {
      control: "text",
      description: "The text to search within",
    },
    query: {
      control: "text",
      description: "The search query to highlight",
    },
    highlightClassName: {
      control: "text",
      description: "Optional CSS class for the highlighted text",
    },
  },
};

export default meta;
type Story = StoryObj<typeof HighlightedText>;

export const Default: Story = {
  args: {
    text: "This is a sample text to highlight",
    query: "sample",
  },
};

export const NoMatch: Story = {
  args: {
    text: "This is a sample text to highlight",
    query: "not found",
  },
};

export const MultipleMatches: Story = {
  args: {
    text: "This text has multiple text matches",
    query: "text",
  },
};

export const CaseInsensitive: Story = {
  args: {
    text: "This TEXT has mixed CASE text",
    query: "text",
  },
};

export const CustomHighlightStyle: Story = {
  args: {
    text: "This is a sample text with custom highlight style",
    query: "custom",
    highlightClassName: "bg-green-200 text-green-800 font-bold",
  },
};
