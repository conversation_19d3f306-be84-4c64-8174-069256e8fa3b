import { action } from "@storybook/addon-actions";
import type { <PERSON>a, StoryObj } from "@storybook/react";
import { useRef } from "react";
import { SearchResultSection } from "./SearchResultSection";

const meta: Meta<typeof SearchResultSection> = {
  title: "UIKit/SearchResultSection",
  component: SearchResultSection,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "The title of the section",
    },
    items: {
      control: "object",
      description: "The items to display in this section",
    },
    search: {
      control: "text",
      description: "The search query to highlight",
    },
    selectedIndex: {
      control: "number",
      description: "The index of the currently selected item",
    },
    baseIndex: {
      control: "number",
      description:
        "The base index for this section (sum of previous sections' items)",
    },
    onItemClick: {
      action: "item clicked",
      description: "Function to call when an item is clicked",
    },
    onItemKeyDown: {
      action: "key pressed",
      description: "Function to call when a key is pressed on an item",
    },
    showDeviceType: {
      control: "boolean",
      description: "Whether to show device type icons (for device items)",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "400px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof SearchResultSection>;

// Create a wrapper component to provide the ref
const SectionWithRef = (args) => {
  const selectedItemRef = useRef(null);
  return <SearchResultSection {...args} selectedItemRef={selectedItemRef} />;
};

export const SitesSection: Story = {
  render: (args) => <SectionWithRef {...args} />,
  args: {
    title: "Sites",
    items: [
      {
        id: "site-1",
        name: "Main Campus",
        originalItem: { id: "site-1", name: "Main Campus" },
      },
      {
        id: "site-2",
        name: "Downtown Office",
        originalItem: { id: "site-2", name: "Downtown Office" },
      },
      {
        id: "site-3",
        name: "Research Facility",
        originalItem: { id: "site-3", name: "Research Facility" },
      },
    ],
    search: "",
    selectedIndex: -1,
    baseIndex: 0,
    onItemClick: action("site clicked"),
    onItemKeyDown: action("key pressed on site"),
    showDeviceType: false,
  },
};

export const SitesSectionWithSelection: Story = {
  render: (args) => <SectionWithRef {...args} />,
  args: {
    title: "Sites",
    items: [
      {
        id: "site-1",
        name: "Main Campus",
        originalItem: { id: "site-1", name: "Main Campus" },
      },
      {
        id: "site-2",
        name: "Downtown Office",
        originalItem: { id: "site-2", name: "Downtown Office" },
      },
      {
        id: "site-3",
        name: "Research Facility",
        originalItem: { id: "site-3", name: "Research Facility" },
      },
    ],
    search: "",
    selectedIndex: 1,
    baseIndex: 0,
    onItemClick: action("site clicked"),
    onItemKeyDown: action("key pressed on site"),
    showDeviceType: false,
  },
};

export const DevicesSection: Story = {
  render: (args) => <SectionWithRef {...args} />,
  args: {
    title: "Devices",
    items: [
      {
        id: "device-1",
        name: "Charger 1",
        type: "Charger",
        originalItem: { id: "device-1", name: "Charger 1", type: "Charger" },
      },
      {
        id: "device-2",
        name: "Battery Pack A",
        type: "Battery",
        originalItem: {
          id: "device-2",
          name: "Battery Pack A",
          type: "Battery",
        },
      },
      {
        id: "device-3",
        name: "Energy Meter 2",
        type: "Meter",
        originalItem: { id: "device-3", name: "Energy Meter 2", type: "Meter" },
      },
    ],
    search: "",
    selectedIndex: -1,
    baseIndex: 5,
    onItemClick: action("device clicked"),
    onItemKeyDown: action("key pressed on device"),
    showDeviceType: true,
  },
};

export const MonitorsSection: Story = {
  render: (args) => <SectionWithRef {...args} />,
  args: {
    title: "Monitors",
    items: [
      {
        id: "monitor-1",
        name: "Test Monitor",
        resourceId: "resource-1",
        originalItem: {
          id: "monitor-1",
          name: "Test Monitor",
          resourceId: "resource-1",
        },
      },
      {
        id: "monitor-2",
        name: "Other Monitor",
        resourceId: "resource-2",
        originalItem: {
          id: "monitor-2",
          name: "Other Monitor",
          resourceId: "resource-2",
        },
      },
      {
        id: "monitor-3",
        name: "Battery Health Monitor",
        resourceId: "resource-3",
        originalItem: {
          id: "monitor-3",
          name: "Battery Health Monitor",
          resourceId: "resource-3",
        },
      },
    ],
    search: "",
    selectedIndex: -1,
    baseIndex: 8,
    onItemClick: action("monitor clicked"),
    onItemKeyDown: action("key pressed on monitor"),
    showDeviceType: false,
  },
};

export const IntegrationsSection: Story = {
  render: (args) => <SectionWithRef {...args} />,
  args: {
    title: "Integrations",
    items: [
      {
        id: "integration-1",
        name: "SMS",
        integrationType: "SMS",
        originalItem: {
          id: "integration-1",
          name: "SMS",
          integrationType: "SMS",
        },
      },
      {
        id: "integration-2",
        name: "Email",
        integrationType: "Email",
        originalItem: {
          id: "integration-2",
          name: "Email",
          integrationType: "Email",
        },
      },
      {
        id: "integration-3",
        name: "Webhook",
        integrationType: "Webhook",
        originalItem: {
          id: "integration-3",
          name: "Webhook",
          integrationType: "Webhook",
        },
      },
    ],
    search: "",
    selectedIndex: -1,
    baseIndex: 11,
    onItemClick: action("integration clicked"),
    onItemKeyDown: action("key pressed on integration"),
    showDeviceType: false,
  },
};

export const SectionWithSearch: Story = {
  render: (args) => <SectionWithRef {...args} />,
  args: {
    title: "Devices",
    items: [
      {
        id: "device-1",
        name: "Charger 1",
        type: "Charger",
        originalItem: { id: "device-1", name: "Charger 1", type: "Charger" },
      },
      {
        id: "device-2",
        name: "Battery Pack A",
        type: "Battery",
        originalItem: {
          id: "device-2",
          name: "Battery Pack A",
          type: "Battery",
        },
      },
      {
        id: "device-3",
        name: "Energy Meter 2",
        type: "Meter",
        originalItem: { id: "device-3", name: "Energy Meter 2", type: "Meter" },
      },
    ],
    search: "Battery",
    selectedIndex: -1,
    baseIndex: 5,
    onItemClick: action("device clicked"),
    onItemKeyDown: action("key pressed on device"),
    showDeviceType: true,
  },
};
