import type React from "react";
import { SearchResultItem as SearchResultItemComponent } from "../SearchResultItem/SearchResultItem";

export interface SearchResultItemData<T = unknown> {
  id: string;
  name: string;
  type?: string;
  originalItem?: T;
  [key: string]: unknown;
}

export interface SearchResultSectionProps<T = unknown> {
  title: string;
  items: Array<SearchResultItemData<T>>;
  search: string;
  selectedIndex: number;
  /**
   * The base index for this section (sum of previous sections' items)
   */
  baseIndex: number;
  selectedItemRef: React.RefObject<HTMLDivElement>;
  onItemClick: (item: SearchResultItemData<T>) => void;
  onItemKeyDown?: (e: React.KeyboardEvent, item: SearchResultItemData<T>) => void;
  /**
   * Whether to show device type icons (for device items)
   */
  showDeviceType?: boolean;
}

export const SearchResultSection = <T,>({
  title,
  items,
  search,
  selectedIndex,
  baseIndex,
  selectedItemRef,
  onItemClick,
  onItemKeyDown,
  showDeviceType = false,
}: SearchResultSectionProps<T>) => {
  if (items.length === 0) return null;

  return (
    <div className="mb-4">
      <h3 className="text-sm font-semibold text-space50 mb-2">{title}</h3>
      {items.map((item, idx) => {
        const itemIndex = idx + baseIndex;
        return (
          <SearchResultItemComponent
            key={item.id}
            item={item}
            search={search}
            isSelected={itemIndex === selectedIndex}
            selectedRef={itemIndex === selectedIndex ? selectedItemRef : undefined}
            onClick={() => onItemClick(item)}
            onKeyDown={onItemKeyDown ? (e) => onItemKeyDown(e, item) : undefined}
            showDeviceType={showDeviceType}
          />
        );
      })}
    </div>
  );
};

export default SearchResultSection;
