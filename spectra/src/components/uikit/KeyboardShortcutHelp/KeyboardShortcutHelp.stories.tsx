import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { KeyboardShortcutHelp } from "./KeyboardShortcutHelp";

const meta: Meta<typeof KeyboardShortcutHelp> = {
  title: "UIKit/KeyboardShortcutHelp",
  component: KeyboardShortcutHelp,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    shortcuts: {
      control: "object",
      description: "Array of keyboard shortcuts to display",
    },
    className: {
      control: "text",
      description: "Optional CSS class for the container",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "500px", padding: "20px", border: "1px solid #eee" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof KeyboardShortcutHelp>;

export const Default: Story = {
  args: {
    shortcuts: [
      { key: "↑↓", description: "to navigate" },
      { key: "enter", description: "to select" },
      { key: "esc", description: "to close" },
    ],
  },
};

export const SingleShortcut: Story = {
  args: {
    shortcuts: [
      { key: "ctrl+s", description: "to save" },
    ],
  },
};

export const ManyShortcuts: Story = {
  args: {
    shortcuts: [
      { key: "↑↓", description: "to navigate" },
      { key: "enter", description: "to select" },
      { key: "esc", description: "to close" },
      { key: "ctrl+f", description: "to search" },
      { key: "ctrl+s", description: "to save" },
    ],
  },
};

export const CustomStyle: Story = {
  args: {
    shortcuts: [
      { key: "↑↓", description: "to navigate" },
      { key: "enter", description: "to select" },
      { key: "esc", description: "to close" },
    ],
    className: "flex flex-col items-start gap-2 text-sm text-blue-600",
  },
};
