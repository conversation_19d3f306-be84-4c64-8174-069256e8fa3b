export interface ShortcutItem {
  key: string;
  description: string;
}

export interface KeyboardShortcutHelpProps {
  shortcuts: ShortcutItem[];
  className?: string;
}

/**
 * KeyboardShortcutHelp component displays keyboard shortcuts in a footer area
 */
export const KeyboardShortcutHelp = ({
  shortcuts,
  className = "flex justify-center gap-8 text-xs text-space50",
}: KeyboardShortcutHelpProps) => {
  return (
    <div className={className}>
      {shortcuts.map((shortcut, index) => (
        <div key={index} className="flex items-center gap-1.5">
          <kbd className="px-1.5 py-0.5 bg-space90 border rounded text-[10px]">
            {shortcut.key}
          </kbd>
          <span className="text-[10px]">{shortcut.description}</span>
        </div>
      ))}
    </div>
  );
};

export default KeyboardShortcutHelp;
