import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Toggle } from "./toggle";

const meta = {
  title: "UI Kit/Toggle",
  component: Toggle,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Toggle>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: "Toggle me",
    checked: false,
    onChange: (checked) => console.log("Toggle changed:", checked),
  },
};

export const Enabled: Story = {
  args: {
    label: "Enabled toggle",
    checked: true,
    onChange: (checked) => console.log("Toggle changed:", checked),
  },
};

export const WithDescription: Story = {
  args: {
    label: "Toggle with description",
    checked: false,
    description:
      "This is a helpful description that explains what this toggle does.",
    onChange: (checked) => console.log("Toggle changed:", checked),
  },
};

export const Disabled: Story = {
  args: {
    label: "Disabled toggle",
    checked: false,
    disabled: true,
    onChange: (checked) => console.log("Toggle changed:", checked),
  },
};

export const DisabledEnabled: Story = {
  args: {
    label: "Disabled enabled toggle",
    checked: true,
    disabled: true,
    onChange: (checked) => console.log("Toggle changed:", checked),
  },
};
