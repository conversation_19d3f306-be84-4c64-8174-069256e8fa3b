type ToggleProps = {
  checkedLabel: string;
  uncheckedLabel: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
};

export const InsetToggle = ({
  checkedLabel = "ON",
  uncheckedLabel = "OFF",
  checked,
  onChange,
  disabled = false,
}: ToggleProps) => {
  return (
    <div className="flex items-center gap-3 my-1">
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        disabled={disabled}
        onClick={() => onChange(!checked)}
        className={`
          relative inline-flex h-8 w-20 min-w-[80px] items-center rounded-full
          transition-colors focus:outline-none focus:ring-2 focus:ring-blue50 focus:ring-offset-2
          bg-gray95
          ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
        `}
      >
        {/* Left Label */}
        <span
          className={`
              absolute left-3 text-xs font-medium transition-colors z-10
              ${checked ? "text-space60" : "text-transparent"}
            `}
        >
          {checkedLabel}
        </span>

        {/* Right Label */}
        <span
          className={`
              absolute right-3 text-xs font-medium transition-colors z-10
              ${!checked ? "text-space60" : "text-transparent"}
            `}
        >
          {uncheckedLabel}
        </span>

        {/* Sliding Button */}
        <span
          className={`
            inline-block h-6 w-6 transform rounded-full bg-white transition-transform z-20 relative
            shadow-sm border border-gray70
            ${checked ? "translate-x-12" : "translate-x-1"}
          `}
        />
      </button>
    </div>
  );
};
