import { ReactComponent as BatteriesIcon } from "../../../images/icons/batteries.svg";
import { ReactComponent as ChargersIcon } from "../../../images/icons/chargers.svg";
import { ReactComponent as MeterIcon } from "../../../images/icons/meter.svg";

export interface DeviceIconProps {
  deviceType: string;
  className?: string;
}

/**
 * DeviceIcon component displays the appropriate icon based on device type
 */
export const DeviceIcon = ({ deviceType, className = "w-4 h-4 text-space50" }: DeviceIconProps) => {
  switch (deviceType) {
    case "Charger":
      return <ChargersIcon className={className} />;
    case "Battery":
      return <BatteriesIcon className={className} />;
    case "Meter":
      return <MeterIcon className={className} />;
    case "SwapStation":
      // Use Battery icon for SwapStation as a fallback
      return <BatteriesIcon className={className} />;
    default:
      return null;
  }
};

export default DeviceIcon;
