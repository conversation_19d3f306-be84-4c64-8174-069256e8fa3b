import type { <PERSON>a, StoryObj } from "@storybook/react";
import { DeviceIcon } from "./DeviceIcon";

const meta: Meta<typeof DeviceIcon> = {
  title: "UIKit/DeviceIcon",
  component: DeviceIcon,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    deviceType: {
      control: "select",
      options: ["Charger", "Battery", "Meter", "SwapStation"],
      description: "The type of device to display an icon for",
    },
    className: {
      control: "text",
      description: "Optional CSS class name to apply to the icon",
    },
  },
};

export default meta;
type Story = StoryObj<typeof DeviceIcon>;

export const Charger: Story = {
  args: {
    deviceType: "Charger",
  },
};

export const Battery: Story = {
  args: {
    deviceType: "Battery",
  },
};

export const Meter: Story = {
  args: {
    deviceType: "Meter",
  },
};

export const SwapStation: Story = {
  args: {
    deviceType: "SwapStation",
  },
};

export const CustomSize: Story = {
  args: {
    deviceType: "Battery",
    className: "w-8 h-8 text-blue40",
  },
};
