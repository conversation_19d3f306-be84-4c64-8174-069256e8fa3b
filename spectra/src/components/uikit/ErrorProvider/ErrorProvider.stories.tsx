/**
 * Storybook stories for ErrorProvider component
 */

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { MemoryRouter, useLocation, useNavigate } from "react-router-dom";
import { useErrorActions } from "../../../stores/errorStore";
import { ErrorProvider } from "./ErrorProvider";

const meta: Meta<typeof ErrorProvider> = {
  title: "UIKit/ErrorProvider",
  component: ErrorProvider,
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    toastPosition: {
      control: "select",
      options: ["top-right", "top-left", "bottom-right", "bottom-left"],
    },
    maxToasts: {
      control: { type: "number", min: 1, max: 10 },
    },
    enableErrorBoundary: {
      control: "boolean",
    },
  },
};

// Simple demo component that doesn't auto-trigger errors
const SimpleDemo: React.FC = () => {
  const { showError, showNetworkError, clearErrors } = useErrorActions();

  const handleShowError = () => {
    showError("Demo Error", "This is a demonstration error message.", "client");
  };

  const handleShowNetworkError = () => {
    const mockResponse = new Response(null, {
      status: 500,
      statusText: "Server Error",
    });
    showNetworkError(mockResponse, "/api/demo", "GET");
  };

  const handleClearErrors = () => {
    clearErrors();
  };

  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold mb-4">Error Provider Demo</h1>
      <p className="text-gray-600 mb-6">
        Click the buttons below to test different error types.
      </p>
      <div className="space-y-2">
        <button
          type="button"
          onClick={handleShowError}
          className="block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Show Demo Error
        </button>
        <button
          type="button"
          onClick={handleShowNetworkError}
          className="block px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Show Network Error
        </button>
        <button
          type="button"
          onClick={handleClearErrors}
          className="block px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          Clear All Errors
        </button>
      </div>
    </div>
  );
};

// Demo component for route change functionality
const RouteChangeDemo: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showError, showNoAccessError, clearErrors } = useErrorActions();

  const handleShowError = () => {
    showError(
      "Route-Specific Error",
      "This error should be cleared when you navigate to a different route.",
      "client",
    );
  };

  const handleShowNoAccessError = () => {
    showNoAccessError();
  };

  const handleClearErrors = () => {
    clearErrors();
  };

  const handleNavigateHome = () => {
    navigate("/");
  };

  const handleNavigateAbout = () => {
    navigate("/about");
  };

  const handleNavigateContact = () => {
    navigate("/contact");
  };

  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold mb-4">
        Route Change Error Clearing Demo
      </h1>
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <p className="text-blue-800 font-medium">
          Current Route: {location.pathname}
        </p>
        <p className="text-blue-600 text-sm mt-1">
          Errors will be automatically cleared when you navigate to a different
          route.
        </p>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Trigger Errors:</h3>
        <button
          type="button"
          onClick={handleShowError}
          className="block px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Show Route Error
        </button>
        <button
          type="button"
          onClick={handleShowNoAccessError}
          className="block px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
        >
          Show No Access Error
        </button>
        <button
          type="button"
          onClick={handleClearErrors}
          className="block px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          Manually Clear Errors
        </button>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Navigate (will clear errors):</h3>
        <button
          type="button"
          onClick={handleNavigateHome}
          className="block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Navigate to Home (/)
        </button>
        <button
          type="button"
          onClick={handleNavigateAbout}
          className="block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          Navigate to About (/about)
        </button>
        <button
          type="button"
          onClick={handleNavigateContact}
          className="block px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Navigate to Contact (/contact)
        </button>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
        <h4 className="text-yellow-800 font-medium">How to test:</h4>
        <ol className="text-yellow-700 text-sm mt-2 space-y-1">
          <li>1. Click "Show Route Error" to display an error toast</li>
          <li>2. Click any navigation button to change routes</li>
          <li>3. Notice that the error disappears automatically</li>
          <li>4. Try the same with "Show No Access Error"</li>
        </ol>
      </div>
    </div>
  );
};

export default meta;
type Story = StoryObj<typeof ErrorProvider>;

export const Default: Story = {
  args: {
    toastPosition: "top-right",
    maxToasts: 5,
    enableErrorBoundary: true,
  },
  render: (args) => (
    <MemoryRouter>
      <ErrorProvider {...args}>
        <SimpleDemo />
      </ErrorProvider>
    </MemoryRouter>
  ),
};

export const TopLeft: Story = {
  args: {
    toastPosition: "top-left",
    maxToasts: 3,
    enableErrorBoundary: true,
  },
  render: (args) => (
    <MemoryRouter>
      <ErrorProvider {...args}>
        <SimpleDemo />
      </ErrorProvider>
    </MemoryRouter>
  ),
};

export const BottomRight: Story = {
  args: {
    toastPosition: "bottom-right",
    maxToasts: 5,
    enableErrorBoundary: true,
  },
  render: (args) => (
    <MemoryRouter>
      <ErrorProvider {...args}>
        <SimpleDemo />
      </ErrorProvider>
    </MemoryRouter>
  ),
};

export const LimitedToasts: Story = {
  args: {
    toastPosition: "top-right",
    maxToasts: 2,
    enableErrorBoundary: true,
  },
  render: (args) => (
    <MemoryRouter>
      <ErrorProvider {...args}>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-4">Limited to 2 Toasts</h1>
          <p className="text-gray-600 mb-6">
            Only 2 toasts will be shown at a time. Try triggering multiple
            errors.
          </p>
          <SimpleDemo />
        </div>
      </ErrorProvider>
    </MemoryRouter>
  ),
};

export const WithoutErrorBoundary: Story = {
  args: {
    toastPosition: "top-right",
    maxToasts: 5,
    enableErrorBoundary: false,
  },
  render: (args) => (
    <MemoryRouter>
      <ErrorProvider {...args}>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-4">Without Error Boundary</h1>
          <p className="text-gray-600 mb-6">
            Error boundary is disabled. Component errors won't be caught
            automatically.
          </p>
          <SimpleDemo />
        </div>
      </ErrorProvider>
    </MemoryRouter>
  ),
};

export const RouteChangeClearing: Story = {
  args: {
    toastPosition: "top-right",
    maxToasts: 5,
    enableErrorBoundary: true,
  },
  render: (args) => (
    <MemoryRouter initialEntries={["/"]}>
      <ErrorProvider {...args}>
        <RouteChangeDemo />
      </ErrorProvider>
    </MemoryRouter>
  ),
};
