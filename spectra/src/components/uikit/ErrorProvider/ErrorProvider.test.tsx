/**
 * Tests for ErrorProvider component to ensure no infinite loops
 */

import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { MemoryRouter, useNavigate } from "react-router-dom";
import { useErrorActions, useErrorStore } from "../../../stores/errorStore";
import { ErrorProvider } from "./ErrorProvider";

// Mock component that uses error actions
const TestComponent: React.FC = () => {
  const { showError, clearErrors } = useErrorActions();

  const handleShowError = () => {
    showError("Test Error", "This is a test error message", "client");
  };

  const handleClearErrors = () => {
    clearErrors();
  };

  return (
    <div>
      <button type="button" onClick={handleShowError} data-testid="show-error">
        Show Error
      </button>
      <button
        type="button"
        onClick={handleClearErrors}
        data-testid="clear-errors"
      >
        Clear Errors
      </button>
      <div data-testid="content">Test Content</div>
    </div>
  );
};

// Mock component that can navigate between routes
const NavigationTestComponent: React.FC = () => {
  const navigate = useNavigate();
  const { showError, showNoAccessError } = useErrorActions();

  const handleShowError = () => {
    showError(
      "Route Error",
      "This error should be cleared on navigation",
      "client",
    );
  };

  const handleShowNoAccessError = () => {
    showNoAccessError();
  };

  const handleNavigateToHome = () => {
    navigate("/");
  };

  const handleNavigateToAbout = () => {
    navigate("/about");
  };

  return (
    <div>
      <button
        type="button"
        onClick={handleShowError}
        data-testid="show-route-error"
      >
        Show Route Error
      </button>
      <button
        type="button"
        onClick={handleShowNoAccessError}
        data-testid="show-no-access-error"
      >
        Show No Access Error
      </button>
      <button
        type="button"
        onClick={handleNavigateToHome}
        data-testid="navigate-home"
      >
        Navigate to Home
      </button>
      <button
        type="button"
        onClick={handleNavigateToAbout}
        data-testid="navigate-about"
      >
        Navigate to About
      </button>
      <div data-testid="navigation-content">Navigation Test Content</div>
    </div>
  );
};

describe("ErrorProvider", () => {
  let consoleSpy: jest.SpyInstance;

  beforeEach(() => {
    // Clear any existing errors before each test
    const { clearErrors } = useErrorStore.getState();
    clearErrors();

    // Mock console.error to suppress expected error logging from the error store
    consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.error after each test
    if (consoleSpy) {
      consoleSpy.mockRestore();
    }
  });

  it("should render without infinite loops", () => {
    render(
      <MemoryRouter>
        <ErrorProvider>
          <TestComponent />
        </ErrorProvider>
      </MemoryRouter>,
    );

    expect(screen.getByTestId("content")).toBeInTheDocument();

    // Should not have any console errors related to infinite loops
    const infiniteLoopErrors = consoleSpy.mock.calls.filter((call) =>
      call.some(
        (arg) =>
          typeof arg === "string" &&
          (arg.includes("Maximum update depth") || arg.includes("infinite")),
      ),
    );

    expect(infiniteLoopErrors).toHaveLength(0);
  });

  it("should display errors when triggered", async () => {
    render(
      <MemoryRouter>
        <ErrorProvider>
          <TestComponent />
        </ErrorProvider>
      </MemoryRouter>,
    );

    // Trigger an error
    fireEvent.click(screen.getByTestId("show-error"));

    // Wait for error to appear
    await waitFor(() => {
      expect(screen.getByText("Test Error")).toBeInTheDocument();
    });

    expect(
      screen.getByText("This is a test error message"),
    ).toBeInTheDocument();
  });

  it("should clear errors when requested", async () => {
    render(
      <MemoryRouter>
        <ErrorProvider>
          <TestComponent />
        </ErrorProvider>
      </MemoryRouter>,
    );

    // Trigger an error
    fireEvent.click(screen.getByTestId("show-error"));

    // Wait for error to appear
    await waitFor(() => {
      expect(screen.getByText("Test Error")).toBeInTheDocument();
    });

    // Clear errors
    fireEvent.click(screen.getByTestId("clear-errors"));

    // Wait for error to disappear
    await waitFor(() => {
      expect(screen.queryByText("Test Error")).not.toBeInTheDocument();
    });
  });

  it("should handle multiple errors without issues", async () => {
    render(
      <MemoryRouter>
        <ErrorProvider maxToasts={3}>
          <TestComponent />
        </ErrorProvider>
      </MemoryRouter>,
    );

    // Trigger multiple errors quickly
    fireEvent.click(screen.getByTestId("show-error"));
    fireEvent.click(screen.getByTestId("show-error"));
    fireEvent.click(screen.getByTestId("show-error"));

    // Should only show up to maxToasts errors
    await waitFor(() => {
      const errorElements = screen.getAllByText("Test Error");
      expect(errorElements.length).toBeLessThanOrEqual(3);
    });
  });

  it("should work with different toast positions", () => {
    const positions: Array<
      "top-right" | "top-left" | "bottom-right" | "bottom-left"
    > = ["top-right", "top-left", "bottom-right", "bottom-left"];

    for (const position of positions) {
      const { unmount } = render(
        <MemoryRouter>
          <ErrorProvider toastPosition={position}>
            <TestComponent />
          </ErrorProvider>
        </MemoryRouter>,
      );

      expect(screen.getByTestId("content")).toBeInTheDocument();
      unmount();
    }
  });

  it("should work with error boundary disabled", () => {
    render(
      <MemoryRouter>
        <ErrorProvider enableErrorBoundary={false}>
          <TestComponent />
        </ErrorProvider>
      </MemoryRouter>,
    );

    expect(screen.getByTestId("content")).toBeInTheDocument();
  });

  describe("Route change error clearing", () => {
    it("should clear errors when navigating between routes", async () => {
      render(
        <MemoryRouter initialEntries={["/"]}>
          <ErrorProvider>
            <NavigationTestComponent />
          </ErrorProvider>
        </MemoryRouter>,
      );

      // Show an error on the current route
      fireEvent.click(screen.getByTestId("show-route-error"));

      // Wait for error to appear
      await waitFor(() => {
        expect(screen.getByText("Route Error")).toBeInTheDocument();
      });

      // Navigate to a different route
      fireEvent.click(screen.getByTestId("navigate-about"));

      // Wait for error to be cleared
      await waitFor(() => {
        expect(screen.queryByText("Route Error")).not.toBeInTheDocument();
      });
    });

    it("should clear no-access error when navigating between routes", async () => {
      render(
        <MemoryRouter initialEntries={["/"]}>
          <ErrorProvider>
            <NavigationTestComponent />
          </ErrorProvider>
        </MemoryRouter>,
      );

      // Show a no-access error
      fireEvent.click(screen.getByTestId("show-no-access-error"));

      // Verify the no-access error state is set
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(true);
      });

      // Navigate to a different route
      fireEvent.click(screen.getByTestId("navigate-about"));

      // Wait for no-access error to be cleared
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(false);
      });
    });

    it("should not clear errors on initial mount", async () => {
      // Pre-populate the error store with an error
      const { showError } = useErrorStore.getState();
      showError(
        "Pre-existing Error",
        "This should not be cleared on mount",
        "client",
      );

      render(
        <MemoryRouter initialEntries={["/"]}>
          <ErrorProvider>
            <NavigationTestComponent />
          </ErrorProvider>
        </MemoryRouter>,
      );

      // The pre-existing error should still be visible after mount
      await waitFor(() => {
        expect(screen.getByText("Pre-existing Error")).toBeInTheDocument();
      });
    });

    it("should allow new errors to be shown after route change", async () => {
      render(
        <MemoryRouter initialEntries={["/"]}>
          <ErrorProvider>
            <NavigationTestComponent />
          </ErrorProvider>
        </MemoryRouter>,
      );

      // Show an error
      fireEvent.click(screen.getByTestId("show-route-error"));

      // Wait for error to appear
      await waitFor(() => {
        expect(screen.getByText("Route Error")).toBeInTheDocument();
      });

      // Navigate to clear errors
      fireEvent.click(screen.getByTestId("navigate-about"));

      // Wait for error to be cleared
      await waitFor(() => {
        expect(screen.queryByText("Route Error")).not.toBeInTheDocument();
      });

      // Show a new error on the new route
      fireEvent.click(screen.getByTestId("show-route-error"));

      // The new error should appear
      await waitFor(() => {
        expect(screen.getByText("Route Error")).toBeInTheDocument();
      });
    });

    it("should demonstrate overlay architecture concept", async () => {
      // This test demonstrates that the overlay architecture concept works
      // by verifying that error clearing still functions correctly
      render(
        <MemoryRouter initialEntries={["/organization"]}>
          <ErrorProvider>
            <NavigationTestComponent />
          </ErrorProvider>
        </MemoryRouter>,
      );

      // Show a no-access error
      fireEvent.click(screen.getByTestId("show-no-access-error"));

      // Verify the no-access error state is set
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(true);
      });

      // Navigate to clear the error
      fireEvent.click(screen.getByTestId("navigate-about"));

      // Wait for error to be cleared
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(false);
      });

      // Navigate back to organization route
      fireEvent.click(screen.getByTestId("navigate-home"));

      // Component should still be functional
      expect(screen.getByTestId("navigation-content")).toBeInTheDocument();
    });

    it("should clear hasNoAccessError specifically on route change", async () => {
      render(
        <MemoryRouter initialEntries={["/organization"]}>
          <ErrorProvider>
            <NavigationTestComponent />
          </ErrorProvider>
        </MemoryRouter>,
      );

      // Show a no-access error
      fireEvent.click(screen.getByTestId("show-no-access-error"));

      // Verify the no-access error state is set
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(true);
      });

      // Navigate to a different route
      fireEvent.click(screen.getByTestId("navigate-about"));

      // Verify that hasNoAccessError is specifically cleared
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(false);
      });

      // Navigate to another route to ensure it stays cleared
      fireEvent.click(screen.getByTestId("navigate-home"));

      // hasNoAccessError should still be false
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(false);
      });
    });

    it("should demonstrate global error recovery concept", async () => {
      // This test demonstrates that the global solution concept works
      // by verifying that error clearing and navigation work together
      render(
        <MemoryRouter initialEntries={["/organization"]}>
          <ErrorProvider>
            <NavigationTestComponent />
          </ErrorProvider>
        </MemoryRouter>,
      );

      // Show a no-access error (simulating 403 from organization route)
      fireEvent.click(screen.getByTestId("show-no-access-error"));

      // Verify the no-access error state is set
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(true);
      });

      // Navigate away to clear the error
      fireEvent.click(screen.getByTestId("navigate-about"));

      // Wait for error to be cleared
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(false);
      });

      // Navigate back to organization route
      fireEvent.click(screen.getByTestId("navigate-home"));

      // The error should remain cleared, allowing the route to load fresh
      await waitFor(() => {
        const hasNoAccessError = useErrorStore.getState().hasNoAccessError;
        expect(hasNoAccessError).toBe(false);
      });

      // Component should still be functional
      expect(screen.getByTestId("navigation-content")).toBeInTheDocument();
    });
  });
});
