import type React from "react";
import { HighlightedText } from "../HighlightedText/HighlightedText";
import { DeviceIcon } from "../DeviceIcon/DeviceIcon";

export interface SearchResultItemProps {
  item: {
    id: string;
    name: string;
    type?: string;
    [key: string]: any;
  };
  search: string;
  isSelected: boolean;
  onClick: () => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  selectedRef?: React.RefObject<HTMLDivElement>;
  /**
   * Whether to show the device type icon (for device items)
   */
  showDeviceType?: boolean;
}

export const SearchResultItem = ({
  item,
  search,
  isSelected,
  onClick,
  onKeyDown,
  selectedRef,
  showDeviceType = false,
}: SearchResultItemProps) => {
  return (
    <div
      ref={selectedRef}
      className={`p-2 cursor-pointer rounded ${
        isSelected ? "bg-blue95 text-blue40" : "hover:bg-space90"
      }`}
      onClick={onClick}
      onKeyDown={onKeyDown}
      tabIndex={0}
      role="button"
    >
      <div className="flex flex-col w-full">
        <div className="flex justify-between items-center">
          <span className="font-medium">
            <HighlightedText text={item.name} query={search} />
          </span>
          {item.id && (
            <code className="text-[10px] text-space70 font-mono">
              <HighlightedText text={item.id} query={search} />
            </code>
          )}
        </div>
        {showDeviceType && item.type && (
          <div className="flex items-center gap-1.5 text-[10px] text-space50 mt-1">
            <DeviceIcon deviceType={item.type} />
            <span>{item.type}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchResultItem;
