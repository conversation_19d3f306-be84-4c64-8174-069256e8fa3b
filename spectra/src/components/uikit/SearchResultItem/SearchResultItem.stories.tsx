import type { <PERSON>a, StoryObj } from "@storybook/react";
import { SearchResultItem } from "./SearchResultItem";
import { action } from "@storybook/addon-actions";

const meta: Meta<typeof SearchResultItem> = {
  title: "UIKit/SearchResultItem",
  component: SearchResultItem,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    item: {
      control: "object",
      description: "The item to display",
    },
    search: {
      control: "text",
      description: "The search query to highlight",
    },
    isSelected: {
      control: "boolean",
      description: "Whether this item is currently selected",
    },
    onClick: {
      action: "clicked",
      description: "Function to call when the item is clicked",
    },
    onKeyDown: {
      action: "key pressed",
      description: "Function to call when a key is pressed on the item",
    },
    showDeviceType: {
      control: "boolean",
      description: "Whether to show the device type icon (for device items)",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "400px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof SearchResultItem>;

export const Site: Story = {
  args: {
    item: {
      id: "site-123",
      name: "Main Campus",
    },
    search: "",
    isSelected: false,
    onClick: action("site clicked"),
    showDeviceType: false,
  },
};

export const SiteSelected: Story = {
  args: {
    item: {
      id: "site-123",
      name: "Main Campus",
    },
    search: "",
    isSelected: true,
    onClick: action("site clicked"),
    showDeviceType: false,
  },
};

export const SiteWithHighlight: Story = {
  args: {
    item: {
      id: "site-123",
      name: "Main Campus",
    },
    search: "Main",
    isSelected: false,
    onClick: action("site clicked"),
    showDeviceType: false,
  },
};

export const Device: Story = {
  args: {
    item: {
      id: "device-456",
      name: "Charger Station 1",
      type: "Charger",
    },
    search: "",
    isSelected: false,
    onClick: action("device clicked"),
    showDeviceType: true,
  },
};

export const DeviceSelected: Story = {
  args: {
    item: {
      id: "device-456",
      name: "Charger Station 1",
      type: "Charger",
    },
    search: "",
    isSelected: true,
    onClick: action("device clicked"),
    showDeviceType: true,
  },
};

export const DeviceWithHighlight: Story = {
  args: {
    item: {
      id: "device-456",
      name: "Charger Station 1",
      type: "Charger",
    },
    search: "Charger",
    isSelected: false,
    onClick: action("device clicked"),
    showDeviceType: true,
  },
};
