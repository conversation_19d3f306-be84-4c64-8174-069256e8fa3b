import type { Meta, StoryObj } from '@storybook/react';
import AlertRow from './AlertRow';

const meta = {
  title: 'UIKit/AlertRow',
  component: AlertRow,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof AlertRow>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample alert data
const createAlert = (state: string, severity: number, resourceType: string) => ({
  resourceId: 'device-123',
  resourceType,
  time: new Date().toISOString(),
  alertState: state,
  alertSeverity: severity,
  alertMessage: `${state} Alert`,
  condition: 'value > threshold',
});

// Healthy Alert
export const Healthy: Story = {
  args: {
    alert: createAlert('HEALTHY', 0, 'THING.SwapStation'),
    onSelect: (id) => console.log('Selected:', id),
  },
};

// Warning Alert
export const Warning: Story = {
  args: {
    alert: createAlert('WARNING', 1, 'THING.SwapStation'),
    onSelect: (id) => console.log('Selected:', id),
  },
};

// Unhealthy Alert
export const Unhealthy: Story = {
  args: {
    alert: createAlert('UNHEALTHY', 2, 'THING.SwapStation'),
    onSelect: (id) => console.log('Selected:', id),
  },
};

// Missing Data Alert
export const MissingData: Story = {
  args: {
    alert: createAlert('MISSING_DATA', 3, 'THING.SwapStation'),
    onSelect: (id) => console.log('Selected:', id),
  },
};

// No Data Alert
export const NoData: Story = {
  args: {
    alert: createAlert('NO_DATA', 4, 'THING.SwapStation'),
    onSelect: (id) => console.log('Selected:', id),
  },
};

// Station Alarm
export const StationAlarm: Story = {
  args: {
    alert: createAlert('WARNING', 2, 'stationAlarm'),
    onSelect: (id) => console.log('Selected:', id),
  },
};

// Without onSelect handler
export const NoInteraction: Story = {
  args: {
    alert: createAlert('WARNING', 1, 'THING.SwapStation'),
  },
};
