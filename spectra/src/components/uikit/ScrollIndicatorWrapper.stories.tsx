import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import {
  ScrollIndicatorWrapper,
  withScrollIndicators,
} from "./ScrollIndicatorWrapper";

const meta: Meta<typeof ScrollIndicatorWrapper> = {
  title: "UI Kit/ScrollIndicatorWrapper",
  component: ScrollIndicatorWrapper,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A higher-order component that adds clickable scroll indicators to show when content can be scrolled horizontally. " +
          "The indicators automatically appear/disappear based on the scroll position and content width. " +
          "Users can click the indicators to smoothly scroll left or right.",
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof ScrollIndicatorWrapper>;

// Sample wide content component for demonstration
const WideContent = ({ width = "150%" }: { width?: string }) => (
  <div
    style={{ width }}
    className="bg-gradient-to-r from-blue-100 to-purple-100 p-6 rounded-lg flex items-center justify-between border border-gray-200"
  >
    <div className="flex space-x-4">
      {Array.from({ length: 8 }, (_, i) => (
        <div
          // biome-ignore lint/suspicious/noArrayIndexKey: Using index as key is acceptable for static demo content
          key={i}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 min-w-[120px] text-center"
        >
          <div className="font-semibold text-gray-800">Item {i + 1}</div>
          <div className="text-sm text-gray-600">Content here</div>
        </div>
      ))}
    </div>
  </div>
);

// Table example
const WideTable = () => (
  <table className="min-w-full divide-y divide-gray-200">
    <thead className="bg-gray-50">
      <tr>
        {Array.from({ length: 10 }, (_, i) => (
          <th
            // biome-ignore lint/suspicious/noArrayIndexKey: Using index as key is acceptable for static demo content
            key={i}
            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]"
          >
            Column {i + 1}
          </th>
        ))}
      </tr>
    </thead>
    <tbody className="bg-white divide-y divide-gray-200">
      {Array.from({ length: 5 }, (_, rowIndex) => (
        // biome-ignore lint/suspicious/noArrayIndexKey: Using index as key is acceptable for static demo content
        <tr key={rowIndex}>
          {Array.from({ length: 10 }, (_, colIndex) => (
            <td
              // biome-ignore lint/suspicious/noArrayIndexKey: Using index as key is acceptable for static demo content
              key={colIndex}
              className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
            >
              Row {rowIndex + 1}, Col {colIndex + 1}
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  </table>
);

export const BasicUsage: Story = {
  args: {
    children: <WideContent />,
    className: "max-w-md border border-gray-300 rounded-lg",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Basic usage with wide content that overflows horizontally. Try scrolling manually or clicking the scroll indicators to navigate.",
      },
    },
  },
};

export const TableExample: Story = {
  args: {
    children: <WideTable />,
    className: "max-w-4xl border border-gray-300 rounded-lg",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example with a wide table. The scroll indicators help users understand when more content is available and can be clicked to scroll.",
      },
    },
  },
};

export const NarrowContainer: Story = {
  args: {
    children: <WideContent width="300%" />,
    className: "max-w-xs border border-gray-300 rounded-lg",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Very narrow container with extra wide content to demonstrate the scroll indicators prominently.",
      },
    },
  },
};

export const NoOverflow: Story = {
  args: {
    children: (
      <div className="bg-green-100 p-6 rounded-lg text-center">
        <p>
          This content fits within the container width, so no scroll indicators
          should appear.
        </p>
      </div>
    ),
    className: "max-w-md border border-gray-300 rounded-lg",
  },
  parameters: {
    docs: {
      description: {
        story: "When content doesn't overflow, no scroll indicators are shown.",
      },
    },
  },
};

// Example of using the HOC function
const SimpleList = () => (
  <div className="flex space-x-4 p-4">
    {Array.from({ length: 12 }, (_, i) => (
      <div
        // biome-ignore lint/suspicious/noArrayIndexKey: Using index as key is acceptable for static demo content
        key={i}
        className="bg-blue-500 text-white px-4 py-2 rounded-lg min-w-[100px] text-center"
      >
        Item {i + 1}
      </div>
    ))}
  </div>
);

const ListWithScrollIndicators = withScrollIndicators(SimpleList);

export const HigherOrderComponent: Story = {
  render: () => (
    <div className="max-w-md border border-gray-300 rounded-lg">
      <ListWithScrollIndicators />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Example of using the `withScrollIndicators` HOC function to wrap an existing component. " +
          "This is useful for applying scroll indicators to components without modifying their implementation.",
      },
    },
  },
};
