import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { SearchModal } from "./SearchModal";
import { useRef } from "react";

const meta: Meta<typeof SearchModal> = {
  title: "UIKit/SearchModal",
  component: SearchModal,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  argTypes: {
    isOpen: {
      control: "boolean",
      description: "Whether the modal is open",
    },
    searchInput: {
      control: { disable: true },
      description: "The search input component",
    },
    children: {
      control: { disable: true },
      description: "The content to display in the modal",
    },
    shortcuts: {
      control: "object",
      description: "Optional keyboard shortcuts to display in the footer",
    },
    isLoading: {
      control: "boolean",
      description: "Optional loading state",
    },
    search: {
      control: "text",
      description: "Optional search query",
    },
    noResultsMessage: {
      control: "text",
      description: "Optional message to display when no results are found",
    },
  },
};

export default meta;
type Story = StoryObj<typeof SearchModal>;

// Create a wrapper component to provide the ref
const ModalWithRef = (args) => {
  const resultsContainerRef = useRef(null);
  
  const searchInput = (
    <input
      type="text"
      className="w-full px-4 py-2 text-lg border rounded-lg"
      placeholder="Search..."
      value={args.search || ""}
      readOnly
    />
  );
  
  return (
    <SearchModal 
      {...args} 
      searchInput={searchInput}
      resultsContainerRef={resultsContainerRef}
    />
  );
};

export const Default: Story = {
  render: (args) => <ModalWithRef {...args} />,
  args: {
    isOpen: true,
    children: (
      <div className="p-4">
        <p>Sample content goes here</p>
        <p>This would typically be search results</p>
      </div>
    ),
    search: "",
    isLoading: false,
  },
};

export const Loading: Story = {
  render: (args) => <ModalWithRef {...args} />,
  args: {
    isOpen: true,
    children: null,
    search: "loading example",
    isLoading: true,
  },
};

export const NoResults: Story = {
  render: (args) => <ModalWithRef {...args} />,
  args: {
    isOpen: true,
    children: null,
    search: "no results example",
    isLoading: false,
    noResultsMessage: "Nothing found for",
  },
};

export const CustomShortcuts: Story = {
  render: (args) => <ModalWithRef {...args} />,
  args: {
    isOpen: true,
    children: (
      <div className="p-4">
        <p>Sample content with custom shortcuts</p>
      </div>
    ),
    search: "",
    isLoading: false,
    shortcuts: [
      { key: "ctrl+f", description: "to filter" },
      { key: "ctrl+s", description: "to save" },
    ],
  },
};
