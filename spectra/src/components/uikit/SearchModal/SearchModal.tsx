import React, { type ReactNode } from "react";
import { KeyboardShortcutHelp, type ShortcutItem } from "../KeyboardShortcutHelp/KeyboardShortcutHelp";

export interface SearchModalProps {
  isOpen: boolean;
  searchInput: ReactNode;
  children: ReactNode;
  shortcuts?: ShortcutItem[];
  resultsContainerRef?: React.RefObject<HTMLDivElement>;
  isLoading?: boolean;
  search?: string;
  noResultsMessage?: string;
}

/**
 * SearchModal component displays a modal dialog with search functionality
 */
export const SearchModal = ({
  isOpen,
  searchInput,
  children,
  shortcuts = [
    { key: "↑↓", description: "to navigate" },
    { key: "enter", description: "to select" },
    { key: "esc", description: "to close" },
  ],
  resultsContainerRef,
  isLoading = false,
  search = "",
  noResultsMessage = "No results found",
}: SearchModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl">
        <div className="p-4">
          {searchInput}
        </div>

        <div
          ref={resultsContainerRef}
          className="max-h-96 overflow-y-auto p-4 pt-0"
        >
          {isLoading ? (
            <div className="text-center py-4">Loading...</div>
          ) : React.Children.count(children) === 0 && search.trim() !== "" ? (
            <div className="text-center py-4 text-space50">
              {noResultsMessage} "{search}"
            </div>
          ) : (
            children
          )}
        </div>

        <div className="border-t border-space90 p-4">
          <KeyboardShortcutHelp shortcuts={shortcuts} />
        </div>
      </div>
    </div>
  );
};

export default SearchModal;
