import type React from "react";
import {
  type ComponentType,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

interface ScrollIndicatorWrapperProps {
  children: React.ReactNode;
  className?: string;
}

// Context for child components to trigger recalculation
const ScrollIndicatorContext = createContext<(() => void) | null>(null);

/**
 * Hook for child components to trigger scroll indicator recalculation
 * Returns a no-op function if used outside of ScrollIndicatorWrapper
 */
export const useScrollIndicatorRecalculate = () => {
  const recalculate = useContext(ScrollIndicatorContext);
  return recalculate || (() => {}); // Return no-op if outside provider
};

/**
 * Higher-order component that adds scroll indicators to show when content can be scrolled horizontally
 */
export const ScrollIndicatorWrapper: React.FC<ScrollIndicatorWrapperProps> = ({
  children,
  className = "",
}) => {
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Check scroll position and update indicators
  const checkScrollPosition = useCallback(() => {
    if (containerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1); // -1 for rounding
    }
  }, []);

  // Scroll functions
  const scrollLeft = () => {
    if (containerRef.current) {
      const scrollAmount = containerRef.current.clientWidth * 0.8; // Scroll 80% of visible width
      containerRef.current.scrollBy({
        left: -scrollAmount,
        behavior: "smooth",
      });
    }
  };

  const scrollRight = () => {
    if (containerRef.current) {
      const scrollAmount = containerRef.current.clientWidth * 0.8; // Scroll 80% of visible width
      containerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      checkScrollPosition(); // Initial check
      container.addEventListener("scroll", checkScrollPosition);

      // Check on resize as well
      const handleResize = () => {
        setTimeout(checkScrollPosition, 100); // Delay to ensure DOM has updated
      };
      window.addEventListener("resize", handleResize);

      return () => {
        container.removeEventListener("scroll", checkScrollPosition);
        window.removeEventListener("resize", handleResize);
      };
    }
  }, [checkScrollPosition]);

  return (
    <ScrollIndicatorContext.Provider value={checkScrollPosition}>
      <div className={`relative ${className}`}>
        {/* Left scroll indicator */}
        {canScrollLeft && (
          <div
            className="absolute left-0 top-0 bottom-0 w-10 bg-gradient-to-r from-white to-transparent z-10 flex items-center cursor-pointer hover:from-blue90 transition-colors"
            onClick={scrollLeft}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                scrollLeft();
              }
            }}
          >
            <button
              className="ml-1 w-6 h-6 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              aria-label="Scroll left"
              type="button"
            >
              <svg
                className="w-4 h-4 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <title>Scroll left</title>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
          </div>
        )}

        {/* Right scroll indicator */}
        {canScrollRight && (
          <div
            className="absolute right-0 top-0 bottom-0 w-10 bg-gradient-to-l from-white to-transparent z-10 flex items-center justify-end cursor-pointer hover:from-blue90 transition-colors"
            onClick={scrollRight}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                scrollRight();
              }
            }}
          >
            <button
              className="mr-1 w-6 h-6 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              aria-label="Scroll right"
              type="button"
            >
              <svg
                className="w-4 h-4 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <title>Scroll right</title>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        )}

        {/* Scrollable content */}
        <div ref={containerRef} className="overflow-x-auto">
          {children}
        </div>
      </div>
    </ScrollIndicatorContext.Provider>
  );
};

/**
 * Higher-order component function that wraps a component with scroll indicators
 */
export function withScrollIndicators<P extends object>(
  WrappedComponent: ComponentType<P>,
  wrapperClassName?: string,
) {
  const WithScrollIndicatorsComponent = (props: P) => {
    return (
      <ScrollIndicatorWrapper className={wrapperClassName}>
        <WrappedComponent {...props} />
      </ScrollIndicatorWrapper>
    );
  };

  WithScrollIndicatorsComponent.displayName = `withScrollIndicators(${
    WrappedComponent.displayName || WrappedComponent.name || "Component"
  })`;

  return WithScrollIndicatorsComponent;
}

export default ScrollIndicatorWrapper;
