import type { Meta, StoryObj } from '@storybook/react';
import Modal from './modal';
import { useState } from 'react';

const meta = {
  title: 'UIKit/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Modal>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Modal
export const Basic: Story = {
  args: {
    open: true,
    onClose: () => {},
    title: 'Modal Title',
    children: <div className="mt-4 p-4">Modal content goes here</div>,
  },
};

// Modal with Actions
export const WithActions: Story = {
  args: {
    open: true,
    onClose: () => {},
    title: 'Confirm Action',
    children: <div className="mt-4 p-4">Are you sure you want to proceed?</div>,
    actions: {
      cancel: {
        label: 'Cancel',
        onClick: () => {},
      },
      confirm: {
        label: 'Confirm',
        onClick: () => {},
        variant: 'primary',
      },
    },
  },
};

// Modal with Error
export const WithError: Story = {
  args: {
    open: true,
    onClose: () => {},
    title: 'Error Modal',
    error: {
      title: 'Error Occurred',
      message: 'Something went wrong. Please try again later.',
    },
  },
};

// Interactive Modal Example
const InteractiveModalExample = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div>
      <button 
        onClick={() => setIsOpen(true)}
        className="px-4 py-2 bg-blue50 text-white rounded-md"
      >
        Open Modal
      </button>
      
      <Modal
        open={isOpen}
        onClose={() => setIsOpen(false)}
        title="Interactive Modal"
        children={<div className="mt-4 p-4">This is an interactive modal example</div>}
        actions={{
          cancel: {
            label: 'Close',
            onClick: () => setIsOpen(false),
          },
          confirm: {
            label: 'Save',
            onClick: () => {
              alert('Saved!');
              setIsOpen(false);
            },
            variant: 'primary',
          },
        }}
      />
    </div>
  );
};

export const Interactive: Story = {
  render: () => <InteractiveModalExample />,
};
