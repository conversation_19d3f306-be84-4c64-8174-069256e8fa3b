import Prism from "prismjs";
import type React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import "../utils/prism.css";

const COMMON_FUNCTIONS = ["Average()", "Count()", "Max()", "Min()"];

interface SyntaxTextFieldProps {
  label: string;
  value: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
  required?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  autoComplete?: string;
  name?: string;
  id?: string;
  className?: string;
  language?: string;
  availableProperties?: string[];
  availableMetrics?: string[];
}

const SyntaxTextField = ({
  label,
  value,
  placeholder,
  onChange = () => {},
  disabled = false,
  error,
  required = false,
  maxLength,
  minLength,
  pattern,
  autoComplete,
  name,
  id,
  className = "",
  language = "clike",
  availableProperties = [],
  availableMetrics = [],
}: SyntaxTextFieldProps) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const codeRef = useRef<HTMLElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const adjustHeight = useCallback(() => {
    if (textareaRef.current && containerRef.current) {
      textareaRef.current.style.height = "0px";
      const newHeight = Math.max(textareaRef.current.scrollHeight, 20);
      textareaRef.current.style.height = `${newHeight}px`;
      containerRef.current.style.height = `${newHeight}px`;
    }
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: value is needed to re-run highlighting and adjust height
  useEffect(() => {
    Prism.highlightAll();
    requestAnimationFrame(adjustHeight);
  }, [value, adjustHeight]);

  // Memoize the suggestions to prevent unnecessary recalculations
  const getSuggestions = useCallback(
    (text: string, cursorPosition: number) => {
      const textBeforeCursor = text.slice(0, cursorPosition);
      const lastChar = textBeforeCursor.slice(-1);
      const words = textBeforeCursor.split(/[\s.()]/);
      const currentWord = words[words.length - 1];

      // If the last character is a dot, show functions
      if (lastChar === ".") {
        return COMMON_FUNCTIONS;
      }

      // If we have a current word, filter properties and metrics
      if (currentWord) {
        const allSuggestions = [...availableProperties, ...availableMetrics];
        const filteredSuggestions = allSuggestions.filter((suggestion) =>
          suggestion.toLowerCase().startsWith(currentWord.toLowerCase()),
        );
        return filteredSuggestions;
      }

      return [];
    },
    [availableProperties, availableMetrics],
  );

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      onChange(newValue);
      requestAnimationFrame(adjustHeight);

      const cursorPosition = e.target.selectionStart;
      const newSuggestions = getSuggestions(newValue, cursorPosition);

      if (newSuggestions.length > 0) {
        const rect = e.target.getBoundingClientRect();
        const lineHeight = Number.parseInt(
          getComputedStyle(e.target).lineHeight,
        );
        const lines = newValue.slice(0, cursorPosition).split("\n").length;
        const words = newValue.slice(0, cursorPosition).split(/[\s.()]/);
        const currentWord = words[words.length - 1];
        const currentWordStart = newValue
          .slice(0, cursorPosition)
          .lastIndexOf(currentWord);

        const top = rect.top + lines * lineHeight;
        const left = rect.left + currentWordStart * 8;

        setDropdownPosition({ top, left });
        setSuggestions(newSuggestions);
        setShowDropdown(true);
        setSelectedIndex(0);
      } else {
        setShowDropdown(false);
      }
    },
    [onChange, getSuggestions, adjustHeight],
  );

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (showDropdown) {
      if (e.key === "ArrowDown") {
        e.preventDefault();
        setSelectedIndex((prev) => (prev + 1) % suggestions.length);
      } else if (e.key === "ArrowUp") {
        e.preventDefault();
        setSelectedIndex(
          (prev) => (prev - 1 + suggestions.length) % suggestions.length,
        );
      } else if (e.key === "Enter") {
        e.preventDefault();
        insertSuggestion(suggestions[selectedIndex]);
      } else if (e.key === "Escape") {
        setShowDropdown(false);
      }
    }
  };

  const insertSuggestion = (suggestion: string) => {
    if (!textareaRef.current) return;

    const cursorPosition = textareaRef.current.selectionStart;
    const textBeforeCursor = value.slice(0, cursorPosition);
    const textAfterCursor = value.slice(cursorPosition);
    const words = textBeforeCursor.split(/[\s.()]/);
    const currentWord = words[words.length - 1];
    const currentWordStart = textBeforeCursor.lastIndexOf(currentWord);

    const newValue =
      textBeforeCursor.slice(0, currentWordStart) +
      suggestion +
      textAfterCursor;

    onChange(newValue);
    setShowDropdown(false);

    requestAnimationFrame(() => {
      if (textareaRef.current) {
        const newPosition = currentWordStart + suggestion.length;
        textareaRef.current.setSelectionRange(newPosition, newPosition);
      }
    });
  };

  const commonStyles: React.CSSProperties = {
    padding: 0,
    margin: 0,
    lineHeight: "1.5",
    fontFamily: "monospace",
    fontSize: "0.875rem",
    tabSize: 2,
    whiteSpace: "pre-wrap" as const,
    wordWrap: "break-word" as const,
    overflow: "hidden",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%",
    height: "100%",
  };

  return (
    <div
      className={`p-3 rounded-lg outline outline-2 outline-offset-[-2px] outline-space90 flex-col justify-start items-center gap-0 w-full transition-all ${
        disabled ? "opacity-50" : "hover:outline-space70"
      } ${error ? "outline-red-500" : ""} ${className}`}
    >
      <div className="justify-start text-space70 text-[10px] font-normal">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </div>
      <div
        ref={containerRef}
        className="relative w-full"
        style={{
          minHeight: "1rem",
          transition: "height 0.1s ease-out",
        }}
      >
        <pre
          className="code-output"
          style={{
            ...commonStyles,
            background: "transparent",
          }}
        >
          <code
            ref={codeRef}
            className={`language-${language}`}
            style={{
              whiteSpace: "pre-wrap",
              wordWrap: "break-word",
              display: "block",
              width: "100%",
              background: "transparent",
            }}
          >
            {value || placeholder}
          </code>
        </pre>
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          maxLength={maxLength}
          minLength={minLength}
          autoComplete={autoComplete}
          name={name}
          id={id}
          className={`w-full justify-start text-space50 text-sm font-medium leading-none border-none focus:outline-none disabled:opacity-50 ${
            error ? "text-red-500" : ""
          }`}
          style={{
            ...commonStyles,
            color: "transparent",
            caretColor: "black",
            background: "transparent",
            resize: "none",
            whiteSpace: "pre-wrap",
            wordWrap: "break-word",
          }}
        />
      </div>
      {error && <div className="text-red-500 text-xs mt-1">{error}</div>}
      {showDropdown &&
        createPortal(
          <div
            className="fixed z-[9999] bg-white border border-gray-200 rounded shadow-lg"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              minWidth: "50px",
              maxWidth: "250px",
              maxHeight: "200px",
              overflowY: "auto",
              overflowX: "hidden",
              textOverflow: "ellipsis",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            {suggestions.map((suggestion, index) => (
              <button
                key={suggestion}
                type="button"
                className={`w-full text-left px-3 py-1 cursor-pointer hover:bg-gray-100 ${
                  index === selectedIndex ? "bg-gray-100" : ""
                }`}
                style={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
                onClick={() => insertSuggestion(suggestion)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    insertSuggestion(suggestion);
                  }
                }}
              >
                {suggestion}
              </button>
            ))}
          </div>,
          document.body,
        )}
    </div>
  );
};

export default SyntaxTextField;
