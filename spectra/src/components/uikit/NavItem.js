import React from "react";
import { ReactComponent as AlertIcon } from "images/icons/alert.svg";

const NavItem = ({
  title,
  icon,
  selected,
  criticalAlerts,
  warningAlerts,
  enabled = true,
  beta = false,
}) => {
  return (
    <li
      className={`text-heading3 flex justify-between w-full py-2.5 px-4 mb-1 rounded-md ${
        enabled
          ? "cursor-pointer hover:bg-blue95 hover:text-blue60"
          : "opacity-50 cursor-not-allowed"
      } ${selected ? "bg-blue95 text-blue50" : "text-space50"}`}
    >
      <div className="flex gap-2 items-center">
        <p className="hover:no-underline">
          {icon} {title}
        </p>
        {beta && (
          <div className="text-[10px] bg-blue50 text-blue95 rounded-md px-2">
            BETA
          </div>
        )}
      </div>

      <p className="float-right flex">
        {criticalAlerts > 0 && (
          <span className="text-red-500 flex mr-2">
            <AlertIcon className="mr-1 mt-1.5" />
            {criticalAlerts}
          </span>
        )}
        {warningAlerts > 0 && (
          <span className="text-yellow-500 flex">
            <AlertIcon className="mr-1 mt-1.5" />
            {warningAlerts}
          </span>
        )}
      </p>
    </li>
  );
};

export default NavItem;