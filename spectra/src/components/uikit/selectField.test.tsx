import "@testing-library/jest-dom";
import { fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import SelectField from "./selectField";

const mockOptions = [
  { value: "option1", label: "Option 1" },
  { value: "option2", label: "Option 2" },
  { value: "option3", label: "Option 3" },
];

describe("SelectField", () => {
  describe("Basic SelectField", () => {
    const mockOnChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
    });

    it("renders with label and options", () => {
      render(
        <SelectField
          label="Test Label"
          value=""
          options={mockOptions}
          onChange={mockOnChange}
        />,
      );

      // Check if label is rendered
      expect(screen.getByText("Test Label")).toBeInTheDocument();

      // Check if options are rendered
      mockOptions.forEach((option) => {
        expect(screen.getByText(option.label)).toBeInTheDocument();
      });
    });

    it("handles selection changes", () => {
      render(
        <SelectField
          label="Test Label"
          value=""
          options={mockOptions}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      fireEvent.change(select, { target: { value: "option2" } });

      expect(mockOnChange).toHaveBeenCalledWith("option2");
    });

    it("disables select when disabled prop is true", () => {
      render(
        <SelectField
          label="Test Label"
          value=""
          options={mockOptions}
          onChange={mockOnChange}
          disabled={true}
        />,
      );

      const select = screen.getByRole("combobox");
      expect(select).toBeDisabled();
    });

    it("applies custom className when provided", () => {
      render(
        <SelectField
          label="Test Label"
          value=""
          options={mockOptions}
          onChange={mockOnChange}
          className="custom-class"
        />,
      );

      const container =
        screen.getByText("Test Label").parentElement?.parentElement;
      expect(container).toHaveClass("custom-class");
    });
  });

  describe("Required SelectField", () => {
    const mockOnChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
    });

    it("renders with required indicator", () => {
      render(
        <SelectField
          label="Required Field"
          value=""
          options={mockOptions}
          onChange={mockOnChange}
          required={true}
        />,
      );

      // Check if label and required indicator are rendered
      expect(screen.getByText("Required Field")).toBeInTheDocument();
      expect(screen.getByText("*")).toBeInTheDocument();

      // Verify required attribute is set
      const select = screen.getByRole("combobox");
      expect(select).toBeRequired();
    });

    it("shows error message when error prop is provided", () => {
      render(
        <SelectField
          label="Required Field"
          value=""
          options={mockOptions}
          onChange={mockOnChange}
          required={true}
          error="This field is required"
        />,
      );

      // Check if error message is rendered
      expect(screen.getByText("This field is required")).toBeInTheDocument();

      // Check if error styling is applied
      const container =
        screen.getByText("Required Field").parentElement?.parentElement;
      expect(container).toHaveClass("outline-red-500");
    });

    it("displays placeholder text", () => {
      const placeholder = "Select an option";
      render(
        <SelectField
          label="Required Field"
          value=""
          options={mockOptions}
          onChange={mockOnChange}
          required={true}
          placeholder={placeholder}
        />,
      );

      const select = screen.getByRole("combobox");
      expect(select).toHaveTextContent(placeholder);
    });
  });
});
