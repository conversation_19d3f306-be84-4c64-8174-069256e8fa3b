/**
 * Tests for ErrorToast component to ensure no infinite loops
 */

import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import React from "react";
import "@testing-library/jest-dom";
import { useErrorActions } from "../../../stores/errorStore";
import { createError } from "../../../utils/errorUtils";
import { ErrorToast } from "./ErrorToast";

// Mock the error store
jest.mock("../../../stores/errorStore", () => ({
  useErrorActions: jest.fn(),
}));

const mockRemoveError = jest.fn();

beforeEach(() => {
  jest.clearAllMocks();
  (useErrorActions as jest.Mock).mockReturnValue({
    removeError: mockRemoveError,
  });
});

describe("ErrorToast", () => {
  const defaultError = createError("Test Error", "Test message", "client");

  it("should render without infinite loops", () => {
    const consoleSpy = jest
      .spyOn(console, "error")
      .mockImplementation(() => {});

    render(<ErrorToast error={defaultError} />);

    expect(screen.getByText("Test Error")).toBeInTheDocument();
    expect(screen.getByText("Test message")).toBeInTheDocument();

    // Should not have any console errors related to infinite loops
    const infiniteLoopErrors = consoleSpy.mock.calls.filter((call) =>
      call.some(
        (arg) =>
          typeof arg === "string" &&
          (arg.includes("Maximum update depth") || arg.includes("infinite")),
      ),
    );

    expect(infiniteLoopErrors).toHaveLength(0);

    consoleSpy.mockRestore();
  });

  it("should animate in after mounting", async () => {
    render(<ErrorToast error={defaultError} />);

    // Find the outermost toast container (the one with positioning and animation classes)
    const toastContainer = screen
      .getByText("Test Error")
      .closest('[class*="fixed"]');

    // Initially should not be visible (opacity-0)
    expect(toastContainer).toHaveClass("opacity-0");

    // Should become visible after animation delay
    await waitFor(
      () => {
        expect(toastContainer).toHaveClass("opacity-100");
      },
      { timeout: 200 },
    );
  });

  it("should handle dismiss action", async () => {
    const dismissibleError = createError(
      "Dismissible Error",
      "Can be dismissed",
      "client",
      {
        dismissible: true,
      },
    );

    render(<ErrorToast error={dismissibleError} />);

    const dismissButton = screen.getByRole("button");
    fireEvent.click(dismissButton);

    // Should call removeError after animation delay
    await waitFor(
      () => {
        expect(mockRemoveError).toHaveBeenCalledWith(dismissibleError.id);
      },
      { timeout: 400 },
    );
  });

  it("should handle retry action", () => {
    const onRetry = jest.fn();
    const retryableError = createError(
      "Retryable Error",
      "Can be retried",
      "network",
      {
        retryable: true,
        onRetry,
      },
    );

    render(<ErrorToast error={retryableError} />);

    const retryButton = screen.getByText("Retry");
    fireEvent.click(retryButton);

    expect(onRetry).toHaveBeenCalled();
  });

  it("should handle custom actions", () => {
    const customAction = jest.fn();
    const actionError = createError(
      "Action Error",
      "Has custom actions",
      "client",
      {
        actions: [
          {
            label: "Custom Action",
            action: customAction,
            variant: "primary",
          },
        ],
      },
    );

    render(<ErrorToast error={actionError} />);

    const actionButton = screen.getByText("Custom Action");
    fireEvent.click(actionButton);

    expect(customAction).toHaveBeenCalled();
  });

  it("should work with different positions", () => {
    const positions: Array<
      "top-right" | "top-left" | "bottom-right" | "bottom-left"
    > = ["top-right", "top-left", "bottom-right", "bottom-left"];

    for (const position of positions) {
      const { unmount } = render(
        <ErrorToast error={defaultError} position={position} />,
      );

      expect(screen.getByText("Test Error")).toBeInTheDocument();

      unmount();
    }
  });

  it("should apply custom className and style", () => {
    const customStyle = { backgroundColor: "red" };
    const customClassName = "custom-toast";

    render(
      <ErrorToast
        error={defaultError}
        className={customClassName}
        style={customStyle}
      />,
    );

    // Find the outermost toast container (the one that receives className and style props)
    const toastContainer = screen
      .getByText("Test Error")
      .closest('[class*="fixed"]');
    expect(toastContainer).toHaveClass(customClassName);
    expect(toastContainer).toHaveStyle("background-color: red");
  });

  it("should not show dismiss button for non-dismissible errors", () => {
    const nonDismissibleError = createError(
      "Non-dismissible",
      "Cannot be dismissed",
      "runtime",
      {
        dismissible: false,
      },
    );

    render(<ErrorToast error={nonDismissibleError} />);

    expect(screen.queryByRole("button")).not.toBeInTheDocument();
  });

  it("should display severity-specific styling", () => {
    const severities: Array<"low" | "medium" | "high" | "critical"> = [
      "low",
      "medium",
      "high",
      "critical",
    ];

    for (const severity of severities) {
      const severityError = createError(
        "Severity Test",
        "Testing severity",
        "client",
        {
          severity,
        },
      );

      const { unmount } = render(<ErrorToast error={severityError} />);

      expect(screen.getByText("Severity Test")).toBeInTheDocument();

      unmount();
    }
  });

  it("should handle rapid prop changes without errors", () => {
    const { rerender } = render(<ErrorToast error={defaultError} />);

    // Rapidly change props to test for infinite loops
    for (let i = 0; i < 10; i++) {
      const newError = createError(`Error ${i}`, `Message ${i}`, "client");
      rerender(<ErrorToast error={newError} />);
    }

    // Should not cause any errors
    expect(screen.getByText("Error 9")).toBeInTheDocument();
  });
});
