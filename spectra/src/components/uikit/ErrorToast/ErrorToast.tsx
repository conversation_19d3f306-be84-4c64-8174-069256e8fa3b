/**
 * ErrorToast component for displaying non-blocking error notifications
 */

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useErrorActions } from "../../../stores/errorStore";
import type { AppError } from "../../../types/errors";

interface ErrorToastProps {
  error: AppError;
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left";
  className?: string;
  style?: React.CSSProperties;
}

const severityStyles = {
  low: "bg-yellow-50 border-yellow-200 text-yellow-800",
  medium: "bg-orange-50 border-orange-200 text-orange-800",
  high: "bg-red-50 border-red-200 text-red-800",
  critical: "bg-red-100 border-red-300 text-red-900",
};

const severityIcons = {
  low: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <title>Low severity warning</title>
      <path
        fillRule="evenodd"
        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
        clipRule="evenodd"
      />
    </svg>
  ),
  medium: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <title>Medium severity warning</title>
      <path
        fillRule="evenodd"
        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
        clipRule="evenodd"
      />
    </svg>
  ),
  high: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <title>High severity warning</title>
      <path
        fillRule="evenodd"
        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
        clipRule="evenodd"
      />
    </svg>
  ),
  critical: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <title>Critical severity warning</title>
      <path
        fillRule="evenodd"
        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
        clipRule="evenodd"
      />
    </svg>
  ),
};

export const ErrorToast: React.FC<ErrorToastProps> = ({
  error,
  position = "top-right",
  className = "",
  style = {},
}) => {
  const { removeError } = useErrorActions();
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const isMountedRef = useRef(true);

  // Memoize position classes to prevent recreation
  const positionClasses = useMemo(
    () => ({
      "top-right": "top-4 right-4",
      "top-left": "top-4 left-4",
      "bottom-right": "bottom-4 right-4",
      "bottom-left": "bottom-4 left-4",
    }),
    [],
  );

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => {
      if (isMountedRef.current) {
        setIsVisible(true);
      }
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // Track mount status
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleDismiss = useCallback(() => {
    if (!isMountedRef.current) return;

    setIsExiting(true);
    const timer = setTimeout(() => {
      if (isMountedRef.current) {
        removeError(error.id);
      }
    }, 300); // Match animation duration

    return timer;
  }, [error.id, removeError]);

  const handleRetry = useCallback(() => {
    if (!isMountedRef.current) return;

    if (error.onRetry) {
      error.onRetry();
      handleDismiss();
    }
  }, [error.onRetry, handleDismiss]);

  return (
    <div
      className={`
        fixed z-50 max-w-sm w-full transition-all duration-300 ease-in-out
        ${positionClasses[position]}
        ${
          isVisible && !isExiting
            ? "translate-x-0 opacity-100"
            : position.includes("right")
              ? "translate-x-full opacity-0"
              : "-translate-x-full opacity-0"
        }
        ${className}
      `}
      style={style}
    >
      <div
        className={`
          rounded-lg border shadow-lg p-4
          ${severityStyles[error.severity]}
        `}
      >
        <div className="flex items-start">
          <div className="flex-shrink-0">{severityIcons[error.severity]}</div>

          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium">{error.title}</h3>
            <p className="mt-1 text-sm opacity-90">{error.message}</p>

            {/* Actions */}
            {(error.actions || error.retryable) && (
              <div className="mt-3 flex space-x-2">
                {error.retryable && (
                  <button
                    type="button"
                    onClick={handleRetry}
                    className="text-xs font-medium underline hover:no-underline focus:outline-none"
                  >
                    Retry
                  </button>
                )}
                {error.actions?.map((action) => (
                  <button
                    type="button"
                    key={action.label}
                    onClick={action.action}
                    className={`
                      text-xs font-medium underline hover:no-underline focus:outline-none
                      ${action.variant === "primary" ? "font-semibold" : ""}
                    `}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Dismiss button */}
          {error.dismissible && (
            <div className="ml-4 flex-shrink-0">
              <button
                type="button"
                onClick={handleDismiss}
                className="inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-current focus:ring-white"
              >
                <span className="sr-only">Dismiss</span>
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <title>Dismiss</title>
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
