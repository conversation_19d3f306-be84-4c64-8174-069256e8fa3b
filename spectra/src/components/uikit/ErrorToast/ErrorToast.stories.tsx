/**
 * Storybook stories for ErrorToast component
 */

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import {
  createError,
  createNetworkError,
  createValidationError,
} from "../../../utils/errorUtils";
import { ErrorToast } from "./ErrorToast";

// Pre-create error objects to prevent recreation on each render
const lowSeverityError = createError(
  "Minor Issue",
  "This is a low severity warning that will auto-hide.",
  "client",
  { severity: "low" },
);

const mediumSeverityError = createError(
  "Validation Error",
  "Please check your input and try again.",
  "validation",
  { severity: "medium" },
);

const highSeverityError = createError(
  "Access Denied",
  "You do not have permission to perform this action.",
  "authorization",
  { severity: "high" },
);

const criticalSeverityError = createError(
  "System Error",
  "A critical error has occurred. Please contact support.",
  "runtime",
  { severity: "critical", autoHide: false },
);

const retryError = createError(
  "Network Error",
  "Failed to load data. Please try again.",
  "network",
  {
    severity: "medium",
    retryable: true,
    onRetry: () => console.log("Retrying..."),
  },
);

const actionsError = createError(
  "Session Expired",
  "Your session has expired. Please log in again.",
  "authentication",
  {
    severity: "high",
    actions: [
      {
        label: "Log In",
        action: () => console.log("Redirecting to login..."),
        variant: "primary" as const,
      },
      {
        label: "Dismiss",
        action: () => console.log("Dismissed"),
        variant: "secondary" as const,
      },
    ],
  },
);

const mockResponse = {
  status: 500,
  statusText: "Internal Server Error",
  ok: false,
} as Response;

const networkError = createNetworkError(mockResponse, "/api/data", "GET");

const validationError = createValidationError(
  "Please check your input and try again.",
  "email",
  {
    email: ["Email is required", "Email format is invalid"],
    password: ["Password must be at least 8 characters"],
  },
);

const nonDismissibleError = createError(
  "Critical Alert",
  "This error cannot be dismissed and requires action.",
  "authentication",
  {
    severity: "critical",
    dismissible: false,
    autoHide: false,
  },
);

const meta: Meta<typeof ErrorToast> = {
  title: "UIKit/ErrorToast",
  component: ErrorToast,
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    position: {
      control: "select",
      options: ["top-right", "top-left", "bottom-right", "bottom-left"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof ErrorToast>;

export const LowSeverity: Story = {
  args: {
    error: lowSeverityError,
    position: "top-right",
  },
};

export const MediumSeverity: Story = {
  args: {
    error: mediumSeverityError,
    position: "top-right",
  },
};

export const HighSeverity: Story = {
  args: {
    error: highSeverityError,
    position: "top-right",
  },
};

export const CriticalSeverity: Story = {
  args: {
    error: criticalSeverityError,
    position: "top-right",
  },
};

export const WithRetry: Story = {
  args: {
    error: retryError,
    position: "top-right",
  },
};

export const WithActions: Story = {
  args: {
    error: actionsError,
    position: "top-right",
  },
};

export const NetworkError: Story = {
  args: {
    error: networkError,
    position: "top-right",
  },
};

export const ValidationErrorWithFields: Story = {
  args: {
    error: validationError,
    position: "top-right",
  },
};

export const BottomLeft: Story = {
  args: {
    error: lowSeverityError,
    position: "bottom-left",
  },
};

export const TopLeft: Story = {
  args: {
    error: mediumSeverityError,
    position: "top-left",
  },
};

export const BottomRight: Story = {
  args: {
    error: highSeverityError,
    position: "bottom-right",
  },
};

export const NonDismissible: Story = {
  args: {
    error: nonDismissibleError,
    position: "top-right",
  },
};
