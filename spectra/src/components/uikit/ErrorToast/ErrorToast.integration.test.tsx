/**
 * Integration test to verify ErrorToast works without infinite loops
 * This simulates the Storybook environment more closely
 */

import { render, screen } from "@testing-library/react";
import React from "react";
import "@testing-library/jest-dom";
import { createError } from "../../../utils/errorUtils";
import { ErrorToast } from "./ErrorToast";

// Mock the error store with stable functions
const mockRemoveError = jest.fn();
jest.mock("../../../stores/errorStore", () => ({
  useErrorActions: () => ({
    removeError: mockRemoveError,
  }),
}));

describe("ErrorToast Integration", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear any console errors
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should render multiple ErrorToast components without infinite loops", () => {
    const errors = [
      createError("Error 1", "Message 1", "client"),
      createError("Error 2", "Message 2", "network"),
      createError("Error 3", "Message 3", "validation"),
    ];

    const { rerender } = render(
      <div>
        {errors.map((error) => (
          <ErrorToast key={error.id} error={error} />
        ))}
      </div>,
    );

    // Should render all errors
    expect(screen.getByText("Error 1")).toBeInTheDocument();
    expect(screen.getByText("Error 2")).toBeInTheDocument();
    expect(screen.getByText("Error 3")).toBeInTheDocument();

    // Re-render multiple times to simulate Storybook behavior
    for (let i = 0; i < 5; i++) {
      rerender(
        <div>
          {errors.map((error) => (
            <ErrorToast key={error.id} error={error} />
          ))}
        </div>,
      );
    }

    // Should still render without errors
    expect(screen.getByText("Error 1")).toBeInTheDocument();
    expect(screen.getByText("Error 2")).toBeInTheDocument();
    expect(screen.getByText("Error 3")).toBeInTheDocument();
  });

  it("should handle prop changes without infinite loops", () => {
    const error1 = createError("Initial Error", "Initial message", "client");
    const error2 = createError("Updated Error", "Updated message", "network");

    const { rerender } = render(<ErrorToast error={error1} />);

    expect(screen.getByText("Initial Error")).toBeInTheDocument();

    // Rapidly change props
    for (let i = 0; i < 10; i++) {
      const currentError = i % 2 === 0 ? error1 : error2;
      rerender(<ErrorToast error={currentError} />);
    }

    // Should handle rapid changes without issues
    expect(screen.getByText("Updated Error")).toBeInTheDocument();
  });

  it("should work with different positions without infinite loops", () => {
    const error = createError("Position Test", "Testing positions", "client");
    const positions: Array<
      "top-right" | "top-left" | "bottom-right" | "bottom-left"
    > = ["top-right", "top-left", "bottom-right", "bottom-left"];

    const { rerender } = render(
      <ErrorToast error={error} position="top-right" />,
    );

    // Rapidly change positions
    for (const position of positions) {
      rerender(<ErrorToast error={error} position={position} />);
      expect(screen.getByText("Position Test")).toBeInTheDocument();
    }
  });

  it("should handle error objects with functions without infinite loops", () => {
    const onRetry = jest.fn();
    const customAction = jest.fn();

    const error = createError(
      "Complex Error",
      "Error with functions",
      "network",
      {
        retryable: true,
        onRetry,
        actions: [
          {
            label: "Custom Action",
            action: customAction,
            variant: "primary" as const,
          },
        ],
      },
    );

    const { rerender } = render(<ErrorToast error={error} />);

    expect(screen.getByText("Complex Error")).toBeInTheDocument();

    // Re-render multiple times with the same error object
    for (let i = 0; i < 5; i++) {
      rerender(<ErrorToast error={error} />);
    }

    // Should still work
    expect(screen.getByText("Complex Error")).toBeInTheDocument();
    expect(screen.getByText("Retry")).toBeInTheDocument();
    expect(screen.getByText("Custom Action")).toBeInTheDocument();
  });

  it("should not cause infinite loops when error store functions change", () => {
    const error = createError("Store Test", "Testing store changes", "client");

    // Mock changing removeError function (simulating store updates)
    let removeErrorCallCount = 0;
    const changingRemoveError = jest.fn(() => {
      removeErrorCallCount++;
    });

    jest.doMock("../../../stores/errorStore", () => ({
      useErrorActions: () => ({
        removeError: changingRemoveError,
      }),
    }));

    const { rerender } = render(<ErrorToast error={error} />);

    expect(screen.getByText("Store Test")).toBeInTheDocument();

    // Re-render multiple times
    for (let i = 0; i < 3; i++) {
      rerender(<ErrorToast error={error} />);
    }

    // Should not cause excessive re-renders
    expect(screen.getByText("Store Test")).toBeInTheDocument();
  });
});
