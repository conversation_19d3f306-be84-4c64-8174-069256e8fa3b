import type React from "react";

interface ButtonComponentProps
  extends React.ComponentPropsWithoutRef<"button"> {
  // Custom props can be added here
  children: React.ReactNode; // Required children prop
  iconBefore?: React.ReactNode;
  iconAfter?: React.ReactNode;
}

const variantStyles = {
  outline: {
    default:
      "border border-space80 text-gray70 hover:bg-gray95 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent",
    primary:
      "border border-blue50 text-blue70 hover:bg-blue50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent",
    danger:
      "border border-red50 text-red70 hover:bg-red50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent",
  },
  filled: {
    default:
      "border border-transparent bg-gray60 text-white hover:bg-gray70 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray60",
    primary:
      "border border-transparent bg-blue50 text-white hover:bg-blue70 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-blue50",
    danger:
      "border border-transparent bg-red50 text-white hover:bg-red70 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-red50",
  },
};

// Base Button Component
const ButtonComponent = ({
  className = "",
  children,
  disabled,
  iconBefore,
  iconAfter,
  ...props
}: ButtonComponentProps) => {
  return (
    <button {...props} disabled={disabled} className={`${className}`}>
      {iconBefore && (
        <span className="inline-flex items-center">{iconBefore}</span>
      )}
      {children}
      {iconAfter && (
        <span className="inline-flex items-center">{iconAfter}</span>
      )}
    </button>
  );
};

const PillButton = ({
  className = "",
  children,
  variant = "outline",
  buttonStyle = "default",
  iconBefore,
  iconAfter,
  disabled,
  ...props
}: ButtonComponentProps & {
  variant?: ButtonVariant;
  buttonStyle?: ButtonStyle;
  iconBefore?: React.ReactNode;
  iconAfter?: React.ReactNode;
}) => {
  return (
    <ButtonComponent
      {...props}
      disabled={disabled}
      className={`px-3.5 py-2 rounded-full justify-end items-center gap-1 cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 ${
        buttonStyle === "danger"
          ? "focus:ring-red-500"
          : "focus:ring-offset-blue90 focus:ring-blue90"
      } ${variantStyles[variant][buttonStyle]} flex text-xs font-medium leading-[14px] ${className}`}
    >
      {iconBefore && <div className="text-xs">{iconBefore}</div>}
      <div className="text-xs font-medium leading-[14px]">{children}</div>
      {iconAfter && <div className="text-xs">{iconAfter}</div>}
    </ButtonComponent>
  );
};

const MenuButton = ({
  className = "",
  children,
  disabled,
  iconBefore,
  iconAfter,
  ...props
}: ButtonComponentProps) => {
  return (
    <ButtonComponent
      {...props}
      disabled={disabled}
      iconBefore={iconBefore}
      iconAfter={iconAfter}
      className={`px-4 w-full py-3 text-xs text-left flex items-center gap-2 ${className}`}
    >
      {children}
    </ButtonComponent>
  );
};

const OptionButton = ({
  className = "",
  children,
  disabled,
  iconBefore,
  iconAfter,
  ...props
}: ButtonComponentProps) => {
  return (
    <ButtonComponent
      {...props}
      disabled={disabled}
      iconBefore={iconBefore}
      iconAfter={iconAfter}
      className={`p-2 w-full text-xs flex items-center justify-center gap-2 ${className}`}
    >
      {children}
    </ButtonComponent>
  );
};

type ButtonVariant = "outline" | "filled";
type ButtonStyle = "default" | "primary" | "danger";

const DefaultButton = ({
  className = "",
  children,
  variant = "outline",
  buttonStyle = "default",
  iconBefore,
  iconAfter,
  disabled,
  ...props
}: ButtonComponentProps & {
  variant: ButtonVariant;
  buttonStyle: ButtonStyle;
  iconBefore?: React.ReactNode;
  iconAfter?: React.ReactNode;
}) => {
  return (
    <ButtonComponent
      {...props}
      disabled={disabled}
      className={`px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
        buttonStyle === "danger" ? "focus:ring-red-500" : "focus:ring-blue-500"
      } ${variantStyles[variant][buttonStyle]} flex items-center justify-center ${className}`}
    >
      {iconBefore && <div>{iconBefore}</div>}
      {children}
      {iconAfter && <div>{iconAfter}</div>}
    </ButtonComponent>
  );
};

// Attach variants to the main component
ButtonComponent.Pill = PillButton;
ButtonComponent.Menu = MenuButton;
ButtonComponent.Option = OptionButton;
ButtonComponent.Default = DefaultButton;

export default ButtonComponent;
