import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import React, { createContext, useContext, useState } from "react";
import type { ReactNode } from "react";

// Define the types for our mock context
interface JumpBarContextType {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  isLoading: boolean;
}

// Create a mock context for Storybook
const MockJumpBarContext = createContext<JumpBarContextType>({
  isOpen: false,
  setIsOpen: () => {},
  isLoading: false,
});

// Create a hook to use our mock context
const useMockJumpBar = () => useContext(MockJumpBarContext);

// Define the props for our mock component
interface SearchHintOverlayProps {
  className?: string;
  text?: string;
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  detectPlatform?: boolean;
  keyboardShortcut?: "⌘" | "Ctrl";
}

// Create a mock SearchHintOverlay component for Storybook
const SearchHintOverlay = ({
  className = "",
  text,
  position = "bottom-right",
  detectPlatform = true,
  keyboardShortcut,
}: SearchHintOverlayProps) => {
  const { setIsOpen } = useMockJumpBar();

  // Determine which key to display
  const isMac = navigator.userAgent.includes("Mac");
  const displayKey = keyboardShortcut || (detectPlatform && isMac ? "⌘" : "Ctrl");

  // Default text if not provided
  const displayText = text || `Press ${displayKey} + K to search`;

  // Position classes
  const positionClasses = {
    "bottom-right": "bottom-8 right-4",
    "bottom-left": "bottom-8 left-4",
    "top-right": "top-8 right-4",
    "top-left": "top-8 left-4",
  };

  // Handle click to open JumpBar
  const handleClick = () => {
    setIsOpen(true);
    console.log("Search overlay clicked - would open JumpBar");
  };

  // Handle keyboard interaction for accessibility
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      setIsOpen(true);
      console.log("Search overlay activated via keyboard - would open JumpBar");
    }
  };

  return (
    <button
      type="button"
      className={`fixed ${positionClasses[position]} text-space80 text-xs bg-white/80 px-3 py-1.5 rounded-full border border-space90 backdrop-blur-sm cursor-pointer ${className}`}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      aria-label="Open search"
    >
      {displayText}
    </button>
  );
};

// Mock JumpBarProvider for Storybook
const MockJumpBarProvider = ({ children }: { children: ReactNode }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <MockJumpBarContext.Provider value={{ isOpen, setIsOpen, isLoading: false }}>
      {children}
    </MockJumpBarContext.Provider>
  );
};

// Define the meta for the story
const meta: Meta<typeof SearchHintOverlay> = {
  title: "UIKit/SearchHintOverlay",
  component: SearchHintOverlay,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <MockJumpBarProvider>
        <div className="h-screen w-screen bg-gray-100">
          <Story />
        </div>
      </MockJumpBarProvider>
    ),
  ],
  argTypes: {
    className: {
      control: "text",
      description: "Custom CSS class to apply to the overlay",
    },
    text: {
      control: "text",
      description: "Custom text to display in the overlay",
    },
    position: {
      control: "select",
      options: ["bottom-right", "bottom-left", "top-right", "top-left"],
      description: "Position of the overlay",
    },
    detectPlatform: {
      control: "boolean",
      description: "Whether to auto-detect platform for keyboard shortcut",
    },
    keyboardShortcut: {
      control: "select",
      options: ["⌘", "Ctrl"],
      description: "Force specific key display instead of auto-detecting",
    },
  },
};

// Define the story type
type Story = StoryObj<typeof SearchHintOverlay>;

// Define story variants
export const Default: Story = {
  args: {
    position: "bottom-right",
    detectPlatform: true,
  },
};

export const CustomText: Story = {
  args: {
    text: "Click here to search",
    position: "bottom-right",
  },
};

export const TopLeftPosition: Story = {
  args: {
    position: "top-left",
  },
};

export const TopRightPosition: Story = {
  args: {
    position: "top-right",
  },
};

export const BottomLeftPosition: Story = {
  args: {
    position: "bottom-left",
  },
};

export const ForceCommandKey: Story = {
  args: {
    keyboardShortcut: "⌘",
    detectPlatform: false,
  },
};

export const ForceCtrlKey: Story = {
  args: {
    keyboardShortcut: "Ctrl",
    detectPlatform: false,
  },
};

export const CustomStyle: Story = {
  args: {
    className: "bg-blue-100 text-blue-800 border-blue-300 font-bold",
  },
};

// Export the meta as default
export default meta;
