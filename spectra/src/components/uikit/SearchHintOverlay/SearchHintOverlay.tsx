import type React from "react";
import { useJumpBar } from "../../../context/JumpBarContext";

export interface SearchHintOverlayProps {
  className?: string;
  text?: string;
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  detectPlatform?: boolean;
  /**
   * Force specific key display instead of auto-detecting
   */
  keyboardShortcut?: "⌘" | "Ctrl";
}

/**
 * SearchHintOverlay component displays a hint for keyboard shortcut to open search
 */
export const SearchHintOverlay: React.FC<SearchHintOverlayProps> = ({
  className = "",
  text,
  position = "bottom-right",
  detectPlatform = true,
  keyboardShortcut,
}) => {
  const { setIsOpen } = useJumpBar();

  // Determine which key to display
  const displayKey = keyboardShortcut || (detectPlatform && navigator.platform.includes("Mac") ? "⌘" : "Ctrl");

  // Default text if not provided
  const displayText = text || `Press ${displayKey} + K to search`;

  // Position classes
  const positionClasses = {
    "bottom-right": "bottom-8 right-4",
    "bottom-left": "bottom-8 left-4",
    "top-right": "top-8 right-4",
    "top-left": "top-8 left-4",
  };

  // Handle click to open JumpBar
  const handleClick = () => {
    setIsOpen(true);
  };

  // Handle keyboard interaction for accessibility
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      setIsOpen(true);
    }
  };

  return (
    <div
      className={`fixed ${positionClasses[position]} text-space80 text-xs bg-white/80 px-3 py-1.5 rounded-full border border-space90 backdrop-blur-sm cursor-pointer ${className}`}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={0}
      aria-label="Open search"
    >
      {displayText}
    </div>
  );
};

export default SearchHintOverlay;
