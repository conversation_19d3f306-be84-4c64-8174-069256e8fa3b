import type React from "react";

interface SpinnerProps {
  size?: "small" | "medium" | "large";
  className?: string;
  color?: string;
}

const sizeStyles = {
  small: "w-4 h-4",
  medium: "w-8 h-8",
  large: "w-12 h-12",
};

/**
 * Spinner component for loading states
 *
 * @param size - Size of the spinner: 'small', 'medium', or 'large'
 * @param className - Additional CSS classes
 * @param color - Color of the spinner (defaults to current text color)
 */
const Spinner: React.FC<SpinnerProps> = ({
  size = "medium",
  className = "",
  color = "currentColor",
}) => {
  const sizeClass = sizeStyles[size];

  return (
    <div
      className={`inline-block animate-spin ${sizeClass} ${className}`}
      aria-label="Loading"
    >
      <svg
        className="w-full h-full"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="32"
          strokeDashoffset="32"
          opacity="0.25"
        />
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="32"
          strokeDashoffset="24"
        />
      </svg>
    </div>
  );
};

export default Spinner;
