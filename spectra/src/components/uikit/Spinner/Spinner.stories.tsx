import type { <PERSON>a, StoryObj } from "@storybook/react";
import Spinner from "./Spinner";

const meta = {
  title: "UIKit/Spinner",
  component: Spinner,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "select" },
      options: ["small", "medium", "large"],
    },
    color: {
      control: { type: "color" },
    },
  },
} satisfies Meta<typeof Spinner>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const Small: Story = {
  args: {
    size: "small",
  },
};

export const Medium: Story = {
  args: {
    size: "medium",
  },
};

export const Large: Story = {
  args: {
    size: "large",
  },
};

export const CustomColor: Story = {
  args: {
    size: "medium",
    color: "#3B82F6",
  },
};

export const WithCustomStyling: Story = {
  args: {
    size: "large",
    className: "text-blue-500",
  },
};

export const AllSizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <div className="text-center">
        <Spinner size="small" />
        <p className="mt-2 text-sm">Small</p>
      </div>
      <div className="text-center">
        <Spinner size="medium" />
        <p className="mt-2 text-sm">Medium</p>
      </div>
      <div className="text-center">
        <Spinner size="large" />
        <p className="mt-2 text-sm">Large</p>
      </div>
    </div>
  ),
};
