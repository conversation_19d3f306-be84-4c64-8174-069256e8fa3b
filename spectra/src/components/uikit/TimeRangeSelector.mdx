import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@storybook/blocks';
import * as TimeRangeSelectorStories from './timeRangeSelector.stories';

<Meta of={TimeRangeSelectorStories} />

# TimeRangeSelector

The TimeRangeSelector is a comprehensive component for selecting date and time ranges with both preset options and custom range selection capabilities.

## Overview

<Canvas of={TimeRangeSelectorStories.Default} />

## Features

### 🎯 **Preset Options**
- **Past Day**: Last 24 hours from current time
- **Today**: From start of today to end of today  
- **Yesterday**: From start of yesterday to end of yesterday
- **Past Week**: Last 7 days from current time

### 🎨 **Enhanced UX**
- **Hover Previews**: See actual date ranges before selecting
- **Visual Feedback**: Smooth animations and loading states
- **Clean Design**: Removed visual clutter while maintaining functionality
- **Responsive**: Works well on different screen sizes

### ⌨️ **Keyboard Navigation**
- `↑/↓` Arrow keys to navigate menu items
- `Enter` to select focused item
- `Esc` to close menu
- `T` shortcut for Today
- `Y` shortcut for Yesterday

### ♿ **Accessibility**
- Full screen reader support
- ARIA labels and live regions
- Focus management
- Keyboard navigation
- High contrast support

## Usage Examples

### Basic Usage
```tsx
import { SelectedTimeRangeProvider, TimeRangeSelector } from 'context/SelectedTimeRangeContext';

function App() {
  return (
    <SelectedTimeRangeProvider>
      <TimeRangeSelector />
    </SelectedTimeRangeProvider>
  );
}
```

### With State Access
```tsx
import { useSelectedTimeRange } from 'context/SelectedTimeRangeContext';

function MyComponent() {
  const { start, end, isUTC } = useSelectedTimeRange();
  
  return (
    <div>
      <TimeRangeSelector />
      <p>Selected: {start.format('YYYY-MM-DD')} to {end.format('YYYY-MM-DD')}</p>
    </div>
  );
}
```

## Stories

### Default State
<Canvas of={TimeRangeSelectorStories.Default} />

### Custom Range Selected
<Canvas of={TimeRangeSelectorStories.CustomRangeSelected} />

### UTC Timezone
<Canvas of={TimeRangeSelectorStories.UTCTimezone} />

### Validation Error
<Canvas of={TimeRangeSelectorStories.ValidationError} />

### Accessibility Features
<Canvas of={TimeRangeSelectorStories.AccessibilityFeatures} />

## Performance Optimizations

- **Debounced Input**: Date changes are debounced to prevent excessive re-renders
- **Memoized Calculations**: Expensive date formatting is memoized
- **Optimized Re-renders**: Uses React.memo and useCallback for performance

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Related Components

- `ButtonComponent` - Used for menu items and options
- `DateTimePicker` - MUI component for date/time selection
- `LocalizationProvider` - MUI provider for date formatting
