import { type AlertEvent } from "api/enterprise";
import { IoMdBatteryCharging } from "react-icons/io";
import { MdOutlinePower } from "react-icons/md";

const getBackgroundColor = (state: string) => {
  switch (state) {
    case "HEALTHY":
      return "bg-green50";
    case "WARNING":
      return "bg-yellow50";
    case "UNHEALTHY":
      return "bg-red50";
    case "MISSING_DATA":
      return "bg-purple50";
    case "NO_DATA":
      return "bg-gray60";
    default:
      return "bg-yellow50";
  }
};

interface AlertRowProps {
  alert: AlertEvent;
  onSelect?: (thingId: string) => void;
}

const AlertRow = ({ alert, onSelect }: AlertRowProps) => {
  const {
    resourceId,
    resourceType,
    time,
    alertState,
    alertSeverity,
    alertMessage,
    condition,
  } = alert;

  const bgColor = getBackgroundColor(alertState);
  const timestamp = new Date(time).toLocaleString();
  const resourceName = resourceType.replace("THING.", "");

  const icon =
    resourceType === "THING.SwapStation" ? (
      <IoMdBatteryCharging fontSize={20} />
    ) : (
      // else "stationAlarm"
      <MdOutlinePower fontSize={20} />
    );

  return (
    <button
      className={`${bgColor} relative rounded-lg overflow-hidden shadow-sm w-full text-left ${
        onSelect ? "cursor-pointer hover:opacity-90" : ""
      }`}
      onClick={() => onSelect?.(resourceId)}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          onSelect?.(resourceId);
        }
      }}
      type="button"
    >
      <div className="flex flex-row text-white">
        <div className="flex flex-col w-24 items-center justify-center bg-white/25 p-1">
          <div className="text-xs font-medium">{alertState}</div>
          <span className="text-3xl font-bold">{alertSeverity}</span>
        </div>

        <div className="flex flex-col flex-1 p-2 mr-2 justify-center items-center">
          <div className="flex flex-col items-center text-sm space-between gap-2">
            <div className="font-medium text-white flex flex-row items-center gap-2">
              {icon}
              {alertMessage || resourceName}
            </div>
            <div className="text-xs">{timestamp}</div>
          </div>
        </div>
      </div>
      <div className="flex flex-col text-white border-t border-white/20 p-2">
        <div className="text-xs text-white/70">
          Condition: <span className="font-mono">{condition}</span>
        </div>
      </div>
    </button>
  );
};

export default AlertRow;
