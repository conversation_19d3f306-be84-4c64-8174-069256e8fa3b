/**
 * Storybook stories for ErrorBoundary component
 */

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import React from "react";
import { ErrorBoundary } from "./ErrorBoundary";

const meta: Meta<typeof ErrorBoundary> = {
  title: "UIKit/ErrorBoundary",
  component: ErrorBoundary,
  parameters: {
    layout: "padded",
  },
};

export default meta;
type Story = StoryObj<typeof ErrorBoundary>;

// Component that throws an error
const ThrowError: React.FC<{ shouldThrow?: boolean; message?: string }> = ({
  shouldThrow = true,
  message = "This is a test error",
}) => {
  if (shouldThrow) {
    throw new Error(message);
  }
  return (
    <div className="p-4 bg-green-100 text-green-800 rounded">
      Component rendered successfully!
    </div>
  );
};

// Component that works normally
const WorkingComponent: React.FC = () => {
  return (
    <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
      <h3 className="text-lg font-semibold text-blue-800 mb-2">
        Working Component
      </h3>
      <p className="text-blue-700">
        This component renders without any errors.
      </p>
      <button
        type="button"
        className="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Click me
      </button>
    </div>
  );
};

export const Default: Story = {
  render: () => (
    <ErrorBoundary>
      <WorkingComponent />
    </ErrorBoundary>
  ),
};

export const WithError: Story = {
  render: () => (
    <ErrorBoundary>
      <ThrowError />
    </ErrorBoundary>
  ),
};

export const WithCustomError: Story = {
  render: () => (
    <ErrorBoundary>
      <ThrowError message="Custom error message for testing" />
    </ErrorBoundary>
  ),
};

export const WithCustomFallback: Story = {
  render: () => (
    <ErrorBoundary
      fallback={(error, retry) => (
        <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">
            Custom Error Fallback
          </h3>
          <p className="text-yellow-700 mb-4">{error.message}</p>
          <button
            type="button"
            onClick={retry}
            className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
          >
            Custom Retry Button
          </button>
        </div>
      )}
    >
      <ThrowError message="Error with custom fallback" />
    </ErrorBoundary>
  ),
};

export const WithErrorHandler: Story = {
  render: () => (
    <ErrorBoundary
      onError={(error) => {
        console.log("Custom error handler called:", error);
        alert(`Error caught: ${error.message}`);
      }}
    >
      <ThrowError message="Error with custom handler" />
    </ErrorBoundary>
  ),
};

export const NestedComponents: Story = {
  render: () => (
    <ErrorBoundary>
      <div className="space-y-4">
        <WorkingComponent />
        <ErrorBoundary>
          <ThrowError message="Nested error boundary test" />
        </ErrorBoundary>
        <WorkingComponent />
      </div>
    </ErrorBoundary>
  ),
};

export const ConditionalError: Story = {
  render: () => {
    const [shouldThrow, setShouldThrow] = React.useState(false);

    return (
      <div className="space-y-4">
        <button
          type="button"
          onClick={() => setShouldThrow(!shouldThrow)}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          {shouldThrow ? "Fix Component" : "Break Component"}
        </button>

        <ErrorBoundary>
          <ThrowError
            shouldThrow={shouldThrow}
            message="Conditional error test"
          />
        </ErrorBoundary>
      </div>
    );
  },
};

export const MultipleErrors: Story = {
  render: () => (
    <div className="space-y-4">
      <ErrorBoundary>
        <ThrowError message="First error boundary" />
      </ErrorBoundary>

      <ErrorBoundary>
        <ThrowError message="Second error boundary" />
      </ErrorBoundary>

      <ErrorBoundary>
        <WorkingComponent />
      </ErrorBoundary>
    </div>
  ),
};
