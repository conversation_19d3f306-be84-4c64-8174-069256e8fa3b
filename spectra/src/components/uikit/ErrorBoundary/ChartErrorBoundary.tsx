/**
 * ChartErrorBoundary - Specialized error boundary for chart components
 * Shows user-friendly error messages instead of crashing the entire page
 */

import React, { Component, type ReactNode } from "react";
import { useErrorStore } from "../../../stores/errorStore";
import type { AppError } from "../../../types/errors";
import { createError } from "../../../utils/errorUtils";

interface ChartErrorBoundaryProps {
  children: ReactNode;
  chartName?: string;
  fallback?: (error: AppError, retry: () => void) => ReactNode;
  onError?: (error: AppError) => void;
}

interface ChartErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
}

/**
 * Error boundary specifically designed for chart components
 * Creates "high" severity errors instead of "critical" so they show as dismissible toasts
 */
export class ChartErrorBoundary extends Component<
  ChartErrorBoundaryProps,
  ChartErrorBoundaryState
> {
  constructor(props: ChartErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ChartErrorBoundaryState {
    return { hasError: true, error: null };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const chartName = this.props.chartName || "Chart";
    
    // Create a high-severity error (not critical) so it shows as a toast
    const appError = createError(
      `${chartName} Error`,
      `Unable to display ${chartName.toLowerCase()}. ${error.message}`,
      "runtime",
      {
        severity: "high", // Use "high" instead of "critical" for toasts
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        dismissible: true,
        autoHide: false, // Don't auto-hide chart errors
      }
    );

    // Update state with the error
    this.setState({ error: appError });

    // Add to error store for toast display
    useErrorStore.getState().addError(appError);

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(appError);
    }

    // Log to console for debugging
    console.error(`ChartErrorBoundary caught an error in ${chartName}:`, error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback && this.state.error) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default fallback UI for charts
      const chartName = this.props.chartName || "Chart";
      return (
        <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center">
            <div className="text-red-500 mb-2">
              <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900 mb-1">
              {chartName} Unavailable
            </h3>
            <p className="text-xs text-gray-600 mb-3">
              Unable to display chart data. Please try refreshing the page.
            </p>
            <button
              onClick={this.handleRetry}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook version for functional components
 */
export const useChartErrorBoundary = () => {
  const addError = useErrorStore((state) => state.addError);

  const reportChartError = (error: Error, chartName = "Chart") => {
    const appError = createError(
      `${chartName} Error`,
      `Unable to display ${chartName.toLowerCase()}. ${error.message}`,
      "runtime",
      {
        severity: "high",
        stack: error.stack,
        dismissible: true,
        autoHide: false,
      }
    );

    addError(appError);
    console.error(`Chart error in ${chartName}:`, error);
  };

  return { reportChartError };
};
