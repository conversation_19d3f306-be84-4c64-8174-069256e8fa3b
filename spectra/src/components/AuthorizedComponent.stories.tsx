import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { AuthorizedComponent, useAuthorization } from "./AuthorizedComponent";
import ButtonComponent from "./uikit/button";

// Mock the AuthContext for storybook
const mockAuthContext = {
  isLoading: false,
  token: "mock-token",
  user: {
    partnerId: "partner-123",
    name: "<EMAIL>",
    nickname: "Test User",
    org_id: "org-123",
    picture: "https://example.com/avatar.jpg",
    sub: "auth0|123",
    updated_at: "2024-01-01T00:00:00.000Z",
    userId: "user-123",
  },
  permissions: ["read:ingest_admin", "write:ingest_places", "read:data_things"],
  logout: () => console.log("Logout clicked"),
};

// Demo component using the hook
const HookDemo = ({ requiredPermission }: { requiredPermission: string }) => {
  const hasPermission = useAuthorization(requiredPermission);

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="font-semibold mb-2">useAuthorization Hook Demo</h3>
      <p className="text-sm text-gray-600 mb-3">
        Required permission:{" "}
        <code className="bg-gray-100 px-1 rounded">{requiredPermission}</code>
      </p>
      <div className="flex items-center gap-2">
        <span
          className={`px-2 py-1 rounded text-sm ${hasPermission ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
        >
          {hasPermission ? "✓ Authorized" : "✗ Not Authorized"}
        </span>
        {hasPermission && (
          <ButtonComponent.Pill>Admin Action</ButtonComponent.Pill>
        )}
      </div>
    </div>
  );
};

const meta: Meta<typeof AuthorizedComponent> = {
  title: "UIKit/AuthorizedComponent",
  component: AuthorizedComponent,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
# AuthorizedComponent - Permission-Based Rendering

A higher-order component that conditionally renders children based on user permissions from the AuthContext.

## Features
- **Permission-based rendering**: Show/hide components based on specific permissions
- **Fallback support**: Optional fallback component when not authorized
- **Hook version**: \`useAuthorization\` hook for conditional logic within components
- **TypeScript support**: Fully typed with proper interfaces

## Usage Examples

### Basic Usage
\`\`\`tsx
<AuthorizedComponent requiredPermission="write:ingest_admin">
  <AdminButton />
</AuthorizedComponent>
\`\`\`

### With Fallback
\`\`\`tsx
<AuthorizedComponent
  requiredPermission="read:ingest_admin"
  fallback={<div>Insufficient permissions</div>}
>
  <AdminPanel />
</AuthorizedComponent>
\`\`\`

### Hook Usage
\`\`\`tsx
const canEdit = useAuthorization("write:ingest_admin");
return (
  <div>
    <ViewButton />
    {canEdit && <EditButton />}
  </div>
);
\`\`\`

## Available Permissions
Common permissions in the system include:
- \`read:ingest_admin\` - Read admin resources
- \`write:ingest_admin\` - Write admin resources
- \`read:data_things\` - Read device data
- \`write:ingest_places\` - Write place data
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    requiredPermission: {
      control: "select",
      options: [
        "read:ingest_admin",
        "write:ingest_admin",
        "read:data_things",
        "write:ingest_places",
        "nonexistent:permission",
      ],
      description: "The permission required to show the component",
    },
    children: {
      control: { disable: true },
      description: "The component(s) to render if authorized",
    },
    fallback: {
      control: { disable: true },
      description: "Optional fallback component to render if not authorized",
    },
  },
  decorators: [
    (Story) => {
      // Mock the useAuth hook for storybook
      const originalModule = jest.requireActual("../context/AuthContext");
      jest.doMock("../context/AuthContext", () => ({
        ...originalModule,
        useAuth: () => mockAuthContext,
      }));

      return (
        <div style={{ minWidth: "400px", padding: "20px" }}>
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              Mock User Permissions:
            </h4>
            <ul className="text-xs text-blue-700 space-y-1">
              {mockAuthContext.permissions.map((permission) => (
                <li key={permission}>
                  <code className="bg-blue-100 px-1 rounded">{permission}</code>
                </li>
              ))}
            </ul>
          </div>
          <Story />
        </div>
      );
    },
  ],
};

export default meta;
type Story = StoryObj<typeof AuthorizedComponent>;

export const AuthorizedUser: Story = {
  args: {
    requiredPermission: "read:ingest_admin",
    children: (
      <div className="p-4 bg-green-50 border border-green-200 rounded">
        <h3 className="text-green-800 font-semibold">✓ Admin Panel</h3>
        <p className="text-green-700 text-sm mt-1">
          This content is visible because the user has "read:ingest_admin"
          permission.
        </p>
        <ButtonComponent.Pill className="mt-2">
          Admin Action
        </ButtonComponent.Pill>
      </div>
    ),
  },
};

export const UnauthorizedUser: Story = {
  args: {
    requiredPermission: "nonexistent:permission",
    children: (
      <div className="p-4 bg-green-50 border border-green-200 rounded">
        <h3 className="text-green-800 font-semibold">Secret Admin Panel</h3>
        <p className="text-green-700 text-sm mt-1">
          You should not see this content.
        </p>
      </div>
    ),
  },
};

export const WithFallback: Story = {
  args: {
    requiredPermission: "nonexistent:permission",
    children: (
      <div className="p-4 bg-green-50 border border-green-200 rounded">
        <h3 className="text-green-800 font-semibold">Admin Content</h3>
        <p className="text-green-700 text-sm">You shouldn't see this.</p>
      </div>
    ),
    fallback: (
      <div className="p-4 bg-red-50 border border-red-200 rounded">
        <h3 className="text-red-800 font-semibold">⚠️ Access Denied</h3>
        <p className="text-red-700 text-sm mt-1">
          You don't have permission to view this content.
        </p>
        <ButtonComponent.Pill className="mt-2">
          Request Access
        </ButtonComponent.Pill>
      </div>
    ),
  },
};

export const MultiplePermissions: Story = {
  render: () => (
    <div className="space-y-4">
      <AuthorizedComponent requiredPermission="read:ingest_admin">
        <div className="p-3 bg-blue-50 border border-blue-200 rounded">
          <span className="text-blue-800 font-medium">Read Admin Panel</span>
          <span className="ml-2 text-xs text-blue-600">
            (read:ingest_admin)
          </span>
        </div>
      </AuthorizedComponent>

      <AuthorizedComponent requiredPermission="write:ingest_admin">
        <div className="p-3 bg-purple-50 border border-purple-200 rounded">
          <span className="text-purple-800 font-medium">Write Admin Panel</span>
          <span className="ml-2 text-xs text-purple-600">
            (write:ingest_admin)
          </span>
        </div>
      </AuthorizedComponent>

      <AuthorizedComponent requiredPermission="read:data_things">
        <div className="p-3 bg-green-50 border border-green-200 rounded">
          <span className="text-green-800 font-medium">Device Data Panel</span>
          <span className="ml-2 text-xs text-green-600">
            (read:data_things)
          </span>
        </div>
      </AuthorizedComponent>

      <AuthorizedComponent
        requiredPermission="super:admin"
        fallback={
          <div className="p-3 bg-gray-50 border border-gray-200 rounded">
            <span className="text-gray-600">Super Admin Panel (Hidden)</span>
          </div>
        }
      >
        <div className="p-3 bg-red-50 border border-red-200 rounded">
          <span className="text-red-800 font-medium">Super Admin Panel</span>
          <span className="ml-2 text-xs text-red-600">(super:admin)</span>
        </div>
      </AuthorizedComponent>
    </div>
  ),
};

export const HookUsage: Story = {
  render: () => (
    <div className="space-y-4">
      <HookDemo requiredPermission="read:ingest_admin" />
      <HookDemo requiredPermission="write:ingest_admin" />
      <HookDemo requiredPermission="nonexistent:permission" />
    </div>
  ),
};
