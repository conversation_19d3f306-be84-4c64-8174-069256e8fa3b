import {
  type BootstrapStateResponse,
  useAdminPlacesAPI,
} from "api/ingestion/admin/places";
import type { PlaceType } from "api/ingestion/common";
import { useCallback, useEffect, useState } from "react";
import { AuthorizedComponent, useAuthorization } from "./AuthorizedComponent";
import ButtonComponent from "./uikit/button";

type BootstrapControlsProps = {
  placeType: PlaceType;
  placeId: string;
};

export const BootstrapControls = ({
  placeType,
  placeId,
}: BootstrapControlsProps) => {
  return (
    <AuthorizedComponent requiredPermission="read:ingest_admin">
      <ControlPanel placeType={placeType} placeId={placeId} />
    </AuthorizedComponent>
  );
};

const ControlPanel = ({ placeType, placeId }: BootstrapControlsProps) => {
  const [bootstrapState, setBootstrapState] =
    useState<BootstrapStateResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBootstrapping, setIsBootstrapping] = useState(false);

  const { getBootstrapState, bootstrapIot, bootstrapTimeseries } =
    useAdminPlacesAPI();

  const writeAuthorized = useAuthorization("write:ingest_admin");

  // biome-ignore lint/correctness/useExhaustiveDependencies: getBootstrapState is not a dependency of fetchBootstrapState
  const fetchBootstrapState = useCallback(async () => {
    try {
      const state = await getBootstrapState(placeType, placeId);
      setBootstrapState(state);
    } catch (error) {
      console.error("Failed to fetch bootstrap state:", error);
    } finally {
      setIsLoading(false);
    }
  }, [placeType, placeId]);

  useEffect(() => {
    fetchBootstrapState();
  }, [fetchBootstrapState]);

  const handleBootstrapIot = async () => {
    setIsBootstrapping(true);
    try {
      await bootstrapIot(placeType, placeId);
      await fetchBootstrapState();
    } catch (error) {
      console.error("Failed to bootstrap IoT:", error);
    } finally {
      setIsBootstrapping(false);
    }
  };

  const handleBootstrapTimeseries = async () => {
    setIsBootstrapping(true);
    try {
      await bootstrapTimeseries(placeType, placeId);
      await fetchBootstrapState();
    } catch (error) {
      console.error("Failed to bootstrap timeseries:", error);
    } finally {
      setIsBootstrapping(false);
    }
  };

  if (isLoading) {
    return <div>Loading bootstrap state...</div>;
  }

  return (
    <div className="py-4">
      <div className="opacity-50 flex flex-col justify-start items-start space-y-2">
        <div className="justify-start text-black text-[10px] font-normal font-mono">
          IoT Bootstrapped: {bootstrapState?.iotBootstrapped ? "Yes" : "No"}
        </div>
        <div className="justify-start text-black text-[10px] font-normal font-mono">
          Timeseries Bootstrapped:{" "}
          {bootstrapState?.timeseriesBootstrapped ? "Yes" : "No"}
        </div>
      </div>

      <div className="mt-4 flex gap-2">
        {!bootstrapState?.iotBootstrapped && writeAuthorized && (
          <ButtonComponent.Pill
            onClick={handleBootstrapIot}
            disabled={isBootstrapping}
          >
            {isBootstrapping ? "Bootstrapping..." : "Bootstrap IoT"}
          </ButtonComponent.Pill>
        )}
        {!bootstrapState?.timeseriesBootstrapped && writeAuthorized && (
          <ButtonComponent.Pill
            onClick={handleBootstrapTimeseries}
            disabled={isBootstrapping}
          >
            {isBootstrapping ? "Bootstrapping..." : "Bootstrap Timeseries"}
          </ButtonComponent.Pill>
        )}
      </div>
    </div>
  );
};
