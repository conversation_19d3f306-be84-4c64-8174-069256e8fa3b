import type { LogEntry } from "hooks/usePollingDeviceLogs";
import { useEffect, useState } from "react";
import { formatLocalTime, formatUtcTime } from "utils/dateFormatters";

interface DataTypeFiltersProps {
  allDataPointTypes: string[];
  enabledTypes: Set<string>;
  onToggleType: (type: string) => void;
}

const DataTypeFilters = ({
  allDataPointTypes,
  enabledTypes,
  onToggleType,
}: DataTypeFiltersProps) => {
  if (allDataPointTypes.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col gap-2">
      <h4 className="text-xs text-space50 font-medium">Filter Data Points</h4>
      <div className="flex flex-wrap gap-1">
        {allDataPointTypes.map((type) => {
          const isEnabled = enabledTypes.has(type);
          return (
            <button
              key={type}
              onClick={() => onToggleType(type)}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                isEnabled
                  ? "bg-blue50 text-white"
                  : "bg-gray95 text-space70 hover:bg-gray90"
              }`}
              type="button"
              aria-pressed={isEnabled}
              aria-checked={isEnabled}
              role="switch"
            >
              {type}
            </button>
          );
        })}
      </div>
    </div>
  );
};

interface LogsTableProps {
  logs: LogEntry[];
  showUtcTimestamps?: boolean;
}

const LogsTable = ({ logs, showUtcTimestamps = true }: LogsTableProps) => {
  const [enabledTypes, setEnabledTypes] = useState<Set<string>>(new Set());
  const [allDataPointTypes, setAllDataPointTypes] = useState<string[]>([]);

  useEffect(() => {
    const types = Array.from(
      new Set(logs.flatMap((log) => log.dataPoints.map((dp) => dp.type))),
    );
    setAllDataPointTypes(types);

    // Initialize with all types enabled if none are selected
    if (enabledTypes.size === 0 && types.length > 0) {
      setEnabledTypes(new Set(types));
    }
  }, [logs, enabledTypes.size]);

  const toggleType = (type: string) => {
    setEnabledTypes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(type)) {
        newSet.delete(type);
      } else {
        newSet.add(type);
      }
      return newSet;
    });
  };

  // Filter dataPoints based on enabled types
  const getFilteredDataPoints = (log: LogEntry) => {
    return log.dataPoints.filter((dp) => enabledTypes.has(dp.type));
  };

  if (logs.length === 0) {
    return (
      <div className="text-center py-8 text-space70">
        <p>No logs available</p>
        <p className="text-xs mt-1">Start polling to see live data</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      {/* Filter Controls */}
      <DataTypeFilters
        allDataPointTypes={allDataPointTypes}
        enabledTypes={enabledTypes}
        onToggleType={toggleType}
      />

      {/* Logs Table */}
      <div className="max-h-[600px] overflow-y-auto">
        <table className="w-full text-xs">
          <thead className="sticky top-0 text-space50 bg-white/80">
            <tr>
              <th className="text-left p-2 border-b text-space50 font-medium min-w-[80px]">
                Timestamp
              </th>
              <th className="text-left p-2 border-b text-space50 font-medium">
                Data Points
              </th>
            </tr>
          </thead>
          <tbody>
            {logs.map((log, index) => {
              const filteredDataPoints = getFilteredDataPoints(log);
              return (
                <tr
                  key={`${log.time}-${index}`}
                  className="border-b hover:bg-gray95 transition-colors"
                >
                  <td className="p-2 text-space70 font-mono text-xs align-top">
                    {showUtcTimestamps
                      ? `${formatUtcTime(log.dayjsTime)}`
                      : `${formatLocalTime(log.dayjsTime)}`}
                    {index === 0 && (
                      <span className="text-xs text-blue50 rounded-md ml-2">
                        LATEST
                      </span>
                    )}
                  </td>
                  <td className="p-2">
                    <div className="flex flex-wrap gap-x-2 gap-y-1">
                      {filteredDataPoints.map((dataPoint, dpIndex) => (
                        <span
                          key={`${dataPoint.type}-${dpIndex}`}
                          className="inline-block text-xs text-space70 font-mono"
                        >
                          {dataPoint.type}:{" "}
                          <span className="text-blue50">
                            {dataPoint.value}
                            {dataPoint.unit || ""}
                          </span>
                        </span>
                      ))}
                      {filteredDataPoints.length === 0 && (
                        <span className="text-gray-500 italic text-xs">
                          No data points match filters
                        </span>
                      )}
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default LogsTable;
