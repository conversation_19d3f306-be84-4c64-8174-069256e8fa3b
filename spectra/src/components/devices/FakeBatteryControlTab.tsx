// This is a fake battery control tab that is used to test the battery control tab for a demo.
import { useState } from "react";
import { FaArrowUp, Fa<PERSON>oon, Fa<PERSON>owerOff, FaShip } from "react-icons/fa";
import { IconContext } from "react-icons/lib";
import { OTAUpgradeModal } from "./OTAUpgradeModal";

export const FakeBatteryControlTab = () => {
  const [isOTAModalOpen, setIsOTAModalOpen] = useState(false);
  return (
    <IconContext.Provider value={{ color: "white", className: "w-4 h-4 mr-2" }}>
      <div className="flex flex-col gap-4">
        <div className="p-4 bg-white rounded-xl shadow-sm border flex flex-col gap-2">
          <h3 className="text-heading3 text-space50">Firmware</h3>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60 flex items-center justify-center gap-2"
            type="button"
            onClick={() => setIsOTAModalOpen(true)}
          >
            <FaArrowUp />
            Upgrade OTA
          </button>
        </div>

        <div className="p-4 bg-white rounded-xl shadow-sm border flex flex-col gap-2">
          <h3 className="text-heading3 text-space50">Battery State</h3>

          <button
            className="px-4 py-2 bg-red50 text-white rounded-lg hover:bg-red60 flex items-center justify-center gap-2"
            type="button"
          >
            Disable Battery
          </button>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60 disabled:opacity-50 flex items-center justify-center gap-2"
            type="button"
            disabled
          >
            Enable Battery
          </button>
        </div>

        <div className="p-4 bg-white rounded-xl shadow-sm border flex flex-col gap-2">
          <h3 className="text-heading3 text-space50">Mode Control</h3>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60 flex items-center justify-center gap-2"
            type="button"
          >
            <FaMoon />
            Enter Sleep Mode
          </button>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60 disabled:opacity-50 flex items-center justify-center gap-2"
            type="button"
          >
            <FaShip />
            Enter Ship Mode
          </button>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60 disabled:opacity-50 flex items-center justify-center gap-2"
            type="button"
          >
            <FaPowerOff />
            Enter Shutdown Mode
          </button>
        </div>

        <div className="p-4 bg-white rounded-xl shadow-sm border flex flex-col gap-2">
          <h3 className="text-heading3 text-space50">Auto Configure</h3>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60"
            type="button"
          >
            Auto Configure Battery CAN
          </button>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60"
            type="button"
          >
            Auto Configure Battery ID
          </button>
        </div>

        <div className="p-4 bg-white rounded-xl shadow-sm border flex flex-col gap-2">
          <h3 className="text-heading3 text-space50">Heater Control</h3>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60 disabled:opacity-50"
            type="button"
            disabled
          >
            Disable Heater
          </button>

          <button
            className="px-4 py-2 bg-blue50 text-white rounded-lg hover:bg-blue60"
            type="button"
          >
            Enable Heater
          </button>
        </div>
      </div>

      <OTAUpgradeModal
        open={isOTAModalOpen}
        onClose={() => setIsOTAModalOpen(false)}
      />
    </IconContext.Provider>
  );
};
