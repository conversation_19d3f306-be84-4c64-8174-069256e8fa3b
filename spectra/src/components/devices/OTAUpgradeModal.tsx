import type React from "react";
import { useEffect, useState } from "react";
import { FaCheckCircle } from "react-icons/fa";
import Modal from "../uikit/modal";

interface OTAUpgradeModalProps {
  open: boolean;
  onClose: () => void;
}

const firmwareVersions = [
  "bms_firmware_v1.2.0.bin",
  "bms_firmware_v1.1.3.bin",
  "bms_firmware_v1.1.2.bin",
  "bms_firmware_v1.1.1.bin",
  "bms_firmware_v1.1.0.bin",
  "bms_firmware_v1.0.0.bin",
  "bms_firmware_v0.4.2.bin",
  "bms_firmware_v0.4.1.bin",
  "bms_firmware_v0.4.0.bin",
  "bms_firmware_v0.3.0.bin",
  "bms_firmware_v0.2.0.bin",
  "bms_firmware_v0.1.0.bin",
  "bms_firmware_v0.0.0.bin",
];

export const OTAUpgradeModal: React.FC<OTAUpgradeModalProps> = ({
  open,
  onClose,
}) => {
  const [selectedFirmware, setSelectedFirmware] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  // Reset states when modal opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedFirmware("");
      setIsUploading(false);
      setUploadProgress(0);
      setIsComplete(false);
    }
  }, [open]);

  // Progress bar simulation
  useEffect(() => {
    if (isUploading && !isComplete) {
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 100) {
            setIsComplete(true);
            setIsUploading(false);
            clearInterval(interval);
            return 100;
          }
          return prev + 1;
        });
      }, 100); // 100ms intervals for 10 seconds (100 * 100ms = 10s)

      return () => clearInterval(interval);
    }
  }, [isUploading, isComplete]);

  const handleUpload = () => {
    if (selectedFirmware && !isUploading && !isComplete) {
      setIsUploading(true);
      setUploadProgress(0);
    }
  };

  const handleCancel = () => {
    if (!isUploading) {
      onClose();
    }
  };

  const handleComplete = () => {
    onClose();
  };

  if (isComplete) {
    return (
      <Modal
        open={open}
        onClose={onClose}
        title="Firmware Upload Complete"
        actions={{
          confirm: {
            label: "Close",
            onClick: handleComplete,
          },
        }}
      >
        <div className="mt-4 text-center">
          <FaCheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <p className="text-lg font-medium text-green-700 mb-2">
            Firmware Successfully Uploaded!
          </p>
          <p className="text-sm text-gray-600">
            {selectedFirmware} has been successfully uploaded to the device. The
            device will restart with the new firmware.
          </p>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      open={open}
      onClose={onClose}
      title="OTA Firmware Upgrade"
      actions={{
        cancel: {
          label: "Cancel",
          onClick: handleCancel,
        },
        confirm: {
          label: isUploading ? "Uploading..." : "Upload Firmware",
          onClick: handleUpload,
          disabled: !selectedFirmware || isUploading,
        },
      }}
    >
      <div className="mt-4">
        <p className="text-sm text-gray-600 mb-4">
          Select a firmware version to upload to the device. The upload process
          will take a few minutes to complete.
        </p>

        <div className="mb-4">
          <label
            htmlFor="firmware-select"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Firmware Version
          </label>
          <select
            id="firmware-select"
            value={selectedFirmware}
            onChange={(e) => setSelectedFirmware(e.target.value)}
            disabled={isUploading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            <option value="">Select a firmware version...</option>
            {firmwareVersions.map((version) => (
              <option key={version} value={version}>
                {version}
              </option>
            ))}
          </select>
        </div>

        {isUploading && (
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Uploading firmware...</span>
              <span>{uploadProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-100"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        )}

        {selectedFirmware && !isUploading && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-700">
              <strong>Warning:</strong> Device will be unavailable during the
              firmware update process. Make sure the device has sufficient power
              and stable connectivity.
            </p>
          </div>
        )}
      </div>
    </Modal>
  );
};
