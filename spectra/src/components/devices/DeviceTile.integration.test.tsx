/**
 * Integration test for DeviceTile with centralized error management
 */

import { render, screen, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { ErrorProvider } from "../uikit/ErrorProvider/ErrorProvider";
import { DeviceTile } from "./DeviceTile";

// Mock the API hooks
jest.mock("api/data", () => ({
  useDataApi: jest.fn(),
  datapointsToMap: jest.fn(),
}));

// Mock the context hooks
jest.mock("context/SelectedDeviceContext", () => ({
  useSelectedDevice: () => ({
    selectedDevice: null,
    setSelectedDevice: jest.fn(),
  }),
}));

jest.mock("context/SelectedSimulationContext", () => ({
  useSelectedSimulation: () => ({
    simulationId: "test-simulation",
  }),
}));

jest.mock("context/SelectedTimeRangeContext", () => ({
  useSelectedTimeRange: () => ({
    start: "2024-01-01",
    end: "2024-01-02",
  }),
}));

jest.mock("../../context/AuthContext", () => ({
  useAuth: () => ({
    user: { partnerId: "test-partner" },
  }),
}));

// Mock utility functions
jest.mock("utils/typeToLabel", () => ({
  typeToLabel: (type: string) => type,
}));

const mockDevice = {
  thingId: "test-device-123",
  thingName: "Test Device",
  thingType: "Charger",
  thingDescription: "A test charger device",
  model: "Model X",
  isSimulated: false,
  longitude: 0,
  latitude: 0,
  altitude: 0,
  siteId: "test-site",
  placeType: "site",
  properties: {
    reportingInterval: "60",
  },
  partnerId: "test-partner",
  parentId: undefined,
  parentType: undefined,
  children: [],
  status: "active",
  firmwareVersion: "1.0.0",
  lastSeen: "2024-01-01T12:00:00Z",
  // Add missing Thing properties
  integrationId: "test-integration",
  levelId: "test-level",
  attributes: {},
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  deletedAt: null,
  createdBy: "test-user",
  primaryGroup: "test-group",
};

describe("DeviceTile Error Management Integration", () => {
  let mockGetSummaryForThing: jest.Mock;
  let mockGetThingLastEventTime: jest.Mock;
  let mockDatapointsToMap: jest.Mock;
  let mockUseDataApi: jest.Mock;

  beforeEach(() => {
    // Create fresh mock functions
    mockGetSummaryForThing = jest.fn();
    mockGetThingLastEventTime = jest.fn();
    mockDatapointsToMap = jest.fn();

    // Set up the useDataApi mock to return our mock functions
    const { useDataApi, datapointsToMap } = require("api/data");
    mockUseDataApi = useDataApi as jest.Mock;
    mockUseDataApi.mockReturnValue({
      getSummaryForThing: mockGetSummaryForThing,
      getThingLastEventTime: mockGetThingLastEventTime,
    });

    // Mock datapointsToMap
    (datapointsToMap as jest.Mock).mockImplementation(mockDatapointsToMap);

    // Reset mocks
    jest.clearAllMocks();

    // Mock console.error to suppress expected error logging
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should handle successful API calls", async () => {
    // Mock successful API responses
    mockGetSummaryForThing.mockResolvedValue([
      { type: "power", value: 100, unit: "kW" },
    ]);
    mockGetThingLastEventTime.mockResolvedValue("2024-01-01T12:00:00Z");
    mockDatapointsToMap.mockReturnValue({
      power: { type: "power", value: 100, unit: "kW" },
    });

    render(
      <ErrorProvider>
        <DeviceTile device={mockDevice} />
      </ErrorProvider>,
    );

    // Should display device name
    expect(screen.getByText("Test Device")).toBeInTheDocument();
    expect(screen.getByText("Charger")).toBeInTheDocument();

    // Wait for API calls to complete and data to load
    await waitFor(() => {
      expect(mockGetSummaryForThing).toHaveBeenCalledWith(
        "site",
        "test-site",
        "test-device-123",
        "2024-01-01",
        "2024-01-02",
        "test-simulation",
      );
    });

    await waitFor(() => {
      expect(mockGetThingLastEventTime).toHaveBeenCalledWith(
        "site",
        "test-site",
        "test-device-123",
      );
    });

    // Should not show any error notifications
    expect(screen.queryByText(/Failed to load/)).not.toBeInTheDocument();
    expect(
      screen.queryByText(/Failed to check connection/),
    ).not.toBeInTheDocument();
  });

  it("should handle getSummaryForThing API failure gracefully", async () => {
    // Mock API failure
    mockGetSummaryForThing.mockRejectedValue(new Error("Network error"));
    mockGetThingLastEventTime.mockResolvedValue("2024-01-01T12:00:00Z");
    mockDatapointsToMap.mockReturnValue({});

    render(
      <ErrorProvider>
        <DeviceTile device={mockDevice} />
      </ErrorProvider>,
    );

    // Should still display device info
    expect(screen.getByText("Test Device")).toBeInTheDocument();

    // Wait for API call to fail
    await waitFor(() => {
      expect(mockGetSummaryForThing).toHaveBeenCalled();
    });

    // Should show error notification with user-friendly message
    await waitFor(() => {
      expect(
        screen.getByText(/Failed to load device statistics for Test Device/),
      ).toBeInTheDocument();
    });

    // Should show "No data" state instead of crashing
    await waitFor(() => {
      expect(screen.getByText("No data")).toBeInTheDocument();
    });
  });

  it("should handle getThingLastEventTime API failure gracefully", async () => {
    // Mock API failure for last event time
    mockGetSummaryForThing.mockResolvedValue([]);
    mockGetThingLastEventTime.mockRejectedValue(new Error("Connection error"));
    mockDatapointsToMap.mockReturnValue({});

    render(
      <ErrorProvider>
        <DeviceTile device={mockDevice} />
      </ErrorProvider>,
    );

    // Wait for API call to fail
    await waitFor(() => {
      expect(mockGetThingLastEventTime).toHaveBeenCalled();
    });

    // Should show error notification about connection status
    await waitFor(() => {
      expect(
        screen.getByText(/Failed to check connection status for Test Device/),
      ).toBeInTheDocument();
    });

    // Component should still function normally
    expect(screen.getByText("Test Device")).toBeInTheDocument();
  });

  it("should handle both API failures simultaneously", async () => {
    // Mock both API failures
    mockGetSummaryForThing.mockRejectedValue(new Error("Stats error"));
    mockGetThingLastEventTime.mockRejectedValue(new Error("Connection error"));
    mockDatapointsToMap.mockReturnValue({});

    render(
      <ErrorProvider>
        <DeviceTile device={mockDevice} />
      </ErrorProvider>,
    );

    // Wait for both API calls to fail
    await waitFor(() => {
      expect(mockGetSummaryForThing).toHaveBeenCalled();
      expect(mockGetThingLastEventTime).toHaveBeenCalled();
    });

    // Should show both error notifications
    await waitFor(() => {
      expect(
        screen.getByText(/Failed to load device statistics/),
      ).toBeInTheDocument();
      expect(
        screen.getByText(/Failed to check connection status/),
      ).toBeInTheDocument();
    });

    // Component should still render and show fallback state
    expect(screen.getByText("Test Device")).toBeInTheDocument();
    expect(screen.getByText("No data")).toBeInTheDocument();
  });

  it("should include device name in error messages", async () => {
    const deviceWithLongName = {
      ...mockDevice,
      thingName: "Very Long Device Name That Should Appear In Error",
    };

    mockGetSummaryForThing.mockRejectedValue(new Error("API Error"));
    mockDatapointsToMap.mockReturnValue({});

    render(
      <ErrorProvider>
        <DeviceTile device={deviceWithLongName} />
      </ErrorProvider>,
    );

    await waitFor(() => {
      expect(
        screen.getByText(
          /Failed to load device statistics for Very Long Device Name That Should Appear In Error/,
        ),
      ).toBeInTheDocument();
    });
  });

  it("should fallback to device ID if name is not available", async () => {
    const deviceWithoutName = {
      ...mockDevice,
      thingName: undefined,
    };

    mockGetSummaryForThing.mockRejectedValue(new Error("API Error"));
    mockDatapointsToMap.mockReturnValue({});

    render(
      <ErrorProvider>
        <DeviceTile device={deviceWithoutName} />
      </ErrorProvider>,
    );

    await waitFor(() => {
      expect(
        screen.getByText(
          /Failed to load device statistics for test-device-123/,
        ),
      ).toBeInTheDocument();
    });
  });
});
