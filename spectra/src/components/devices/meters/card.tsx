import Trend from "components/uikit/trend";
import { ReactComponent as MeterIcon } from "images/icons/meter.svg";

const MeterDetail = ({
    meter,
    isSelected = false,
    onClick = () => {}
}: {
    meter: any, // TODO: fix
    isSelected: boolean,
    onClick: () => void
}) => {
  return(
    <div className="flex flex-col gap-2 w-1/2 cursor-pointer" onClick={onClick}>
      <div
        className={`p-4 bg-white rounded-md shadow border border-gray90 flex-col justify-start items-start gap-2 inline-flex ${isSelected ? "outline outline-2 outline-offset-2 outline-blue50" : ""}`}
      >
        <div className="self-stretch justify-between items-center inline-flex">
          <div className="justify-start items-center gap-2 flex">
            <div className="text-space50 text-body">
              <MeterIcon />
            </div>
            <div className="justify-start items-center gap-1 flex">
              <div className="text-space50 text-body font-normal">{meter.name}</div>
              <div className="text-space70 text-body font-normal">&rarr;</div>
              <div className="text-space70 text-body font-normal">
                {meter.downstream} downstream
              </div>
            </div>
          </div>
        </div>
        <div className="self-stretch flex-col justify-start items-center gap-2 flex">
          <div className="self-stretch justify-center items-start gap-4 inline-flex">
            <div className="grow shrink basis-0 justify-center items-center gap-2.5 flex">
              <div className="grow shrink basis-0 flex-col justify-start items-start inline-flex">
                <div className="justify-start items-center gap-1 inline-flex">
                  <div className="text-space50 text-heading1 font-normal">
                    {meter.draw.toLocaleString()}
                  </div>
                  <div className="text-space70 text-caption font-normal">kWh</div>
                </div>
                <div className="text-space70 text-caption font-normal">
                  Energy Measured (last 24h)
                </div>
              </div>
              <Trend percent={meter.trend} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MeterDetail;
