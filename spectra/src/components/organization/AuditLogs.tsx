import { useState } from "react";

// Interface for audit log entries
export interface AuditLogEntry {
  id: string;
  timestamp: Date;
  user: string;
  action: string;
  resource: string;
  details: string;
  type: "created" | "edited" | "removed" | "added";
}

// Mock audit log data based on the reference image
const mockAuditLogs: AuditLogEntry[] = [
  {
    id: "1",
    timestamp: new Date("2025-07-03T12:34:00"),
    user: "Jordan",
    action: "Created device",
    resource: "Battery 5a07f509-9302-4dc9-978d-fc9abd456f38",
    details: "Created device: Battery 5a07f509-9302-4dc9-978d-fc9abd456f38",
    type: "created",
  },
  {
    id: "2",
    timestamp: new Date("2025-07-03T12:30:00"),
    user: "Jordan",
    action: "Created device",
    resource: "Battery 5d49e830-6f01-4331-b997-cbbf1863c764",
    details: "Created device: Battery 5d49e830-6f01-4331-b997-cbbf1863c764",
    type: "created",
  },
  {
    id: "3",
    timestamp: new Date("2025-07-03T12:28:00"),
    user: "Jordan",
    action: "Created device",
    resource: "Battery 7533811f-b6e0-4f30-8b8e-8d8ed61e37b3",
    details: "Created device: Battery 7533811f-b6e0-4f30-8b8e-8d8ed61e37b3",
    type: "created",
  },
  {
    id: "4",
    timestamp: new Date("2025-07-03T12:20:00"),
    user: "Jordan",
    action: "Created device",
    resource: "Battery e8c8a23a-3e18-4398-8e97-a73e04cfb513",
    details: "Created device: Battery e8c8a23a-3e18-4398-8e97-a73e04cfb513",
    type: "created",
  },
  {
    id: "5",
    timestamp: new Date("2025-07-03T12:15:00"),
    user: "Taylor",
    action: "Edited device",
    resource: "Battery 190dfcc8-7d5d-4fb0-9dd8-63af721c1738",
    details: "Edited device: Battery 190dfcc8-7d5d-4fb0-9dd8-63af721c1738",
    type: "edited",
  },
  {
    id: "6",
    timestamp: new Date("2025-07-03T12:10:00"),
    user: "Taylor",
    action: "Edited device",
    resource: "Battery 31b8c21e-1f61-4287-8bce-7e10ffbacd89",
    details: "Edited device: Battery 31b8c21e-1f61-4287-8bce-7e10ffbacd89",
    type: "edited",
  },
  {
    id: "7",
    timestamp: new Date("2025-07-03T12:05:00"),
    user: "Taylor",
    action: "Edited device",
    resource: "Battery 5fa3ae1f-b1b2-4924-952b-e88c3ac3fe17",
    details: "Edited device: Battery 5fa3ae1f-b1b2-4924-952b-e88c3ac3fe17",
    type: "edited",
  },
  {
    id: "8",
    timestamp: new Date("2025-07-03T12:00:00"),
    user: "Taylor",
    action: "Created device",
    resource: "Battery efa3da91-78ee-4dcf-8dc6-0fc195f4056b",
    details: "Created device: Battery efa3da91-78ee-4dcf-8dc6-0fc195f4056b",
    type: "created",
  },
  {
    id: "9",
    timestamp: new Date("2025-07-02T11:55:00"),
    user: "Morgan",
    action: "Edited device",
    resource: "Battery efa3da91-78ee-4dcf-8dc6-0fc195f4056b",
    details: "Edited device: Battery efa3da91-78ee-4dcf-8dc6-0fc195f4056b",
    type: "edited",
  },
  {
    id: "10",
    timestamp: new Date("2025-07-02T11:50:00"),
    user: "Morgan",
    action: "Removed device",
    resource: "Battery 5ee98bb6-c734-4b2f-811d-5150a5384ef8",
    details: "Removed device: Battery 5ee98bb6-c734-4b2f-811d-5150a5384ef8",
    type: "removed",
  },
  {
    id: "11",
    timestamp: new Date("2025-07-02T11:40:00"),
    user: "Morgan",
    action: "Removed device",
    resource: "Battery 6ae78b9c-72cc-8e45-8d2a-8c2e8d4e5f6a",
    details: "Removed device: Battery 6ae78b9c-72cc-8e45-8d2a-8c2e8d4e5f6a",
    type: "removed",
  },
  {
    id: "12",
    timestamp: new Date("2025-07-02T11:35:00"),
    user: "Casey",
    action: "Edited device",
    resource: "Battery a290266b-20fb-4bec-8c2e-23cc7eee1b73",
    details: "Edited device: Battery a290266b-20fb-4bec-8c2e-23cc7eee1b73",
    type: "edited",
  },
  {
    id: "13",
    timestamp: new Date("2025-07-02T11:30:00"),
    user: "Casey",
    action: "Removed device",
    resource: "Battery b5bb8694-2d71-495f-b2fa-8794if72f21",
    details: "Removed device: Battery b5bb8694-2d71-495f-b2fa-8794if72f21",
    type: "removed",
  },
  {
    id: "14",
    timestamp: new Date("2025-07-02T11:25:00"),
    user: "Casey",
    action: "Added integration",
    resource: "c592f03e-e9e7-4e4f-89f2-82ef2aa209a5",
    details: "Added integration: c592f03e-e9e7-4e4f-89f2-82ef2aa209a5",
    type: "added",
  },
  {
    id: "15",
    timestamp: new Date("2025-07-02T11:20:00"),
    user: "Casey",
    action: "Added integration",
    resource:
      '{"this": "is", "an": "example", "of": "a", "really": ["long", "json", "blob", "that", "will", "get", "truncated"], "or": "need expanding"}',
    details:
      '{"this": "is", "an": "example", "of": "a", "really": ["long", "json", "blob", "that", "will", "get", "truncated"], "or": "need expanding"}',
    type: "added",
  },
  {
    id: "16",
    timestamp: new Date("2025-07-02T11:15:00"),
    user: "Riley",
    action: "Added integration",
    resource:
      '{"long": "json", "blob", "that", "will", "get", "truncated"], "or": "need expanding"}',
    details:
      '{"long": "json", "blob", "that", "will", "get", "truncated"], "or": "need expanding"}',
    type: "added",
  },
  {
    id: "17",
    timestamp: new Date("2025-07-02T11:10:00"),
    user: "Riley",
    action: "Removed device",
    resource: "Battery 147f89d5-940a-4a62-becf-6367b899d51",
    details: "Removed device: Battery 147f89d5-940a-4a62-becf-6367b899d51",
    type: "removed",
  },
  {
    id: "18",
    timestamp: new Date("2025-07-02T11:05:00"),
    user: "Riley",
    action: "Added integration",
    resource: "885baefa-1a75-46bd-8686-5fa87a15f0e9",
    details: "Added integration: 885baefa-1a75-46bd-8686-5fa87a15f0e9",
    type: "added",
  },
  {
    id: "19",
    timestamp: new Date("2025-07-02T11:00:00"),
    user: "Riley",
    action: "Added integration",
    resource: "1dc12b85-81d0-41dd-b3f7-a84aee31e703",
    details: "Added integration: 1dc12b85-81d0-41dd-b3f7-a84aee31e703",
    type: "added",
  },
];

interface AuditLogItemProps {
  entry: AuditLogEntry;
  isExpanded: boolean;
  onToggleExpand: () => void;
}

const AuditLogItem: React.FC<AuditLogItemProps> = ({
  entry,
  isExpanded,
  onToggleExpand,
}) => {
  const formatTimestamp = (date: Date) => {
    return date
      .toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
      .replace(",", "");
  };

  const truncateText = (text: string, maxLength = 60) => {
    if (text.length <= maxLength) return text;
    return `${text.substring(0, maxLength)}...`;
  };

  const shouldShowExpand = entry.details.length > 60;

  return (
    <div className="relative grid grid-cols-12 gap-4 py-2 px-4 text-sm hover:bg-gray-50 font-mono">
      {shouldShowExpand && (
        <button
          type="button"
          onClick={onToggleExpand}
          className="absolute top-0.5 text-blue-900 text-xs flex items-center z-10"
          aria-label={isExpanded ? "Collapse details" : "Expand details"}
        >
          {isExpanded ? (
            <span className="text-xl text-blue-900">▲</span>
          ) : (
            <span className="text-xl text-blue-900">▼</span>
          )}
        </button>
      )}
      <div className="col-span-2">{formatTimestamp(entry.timestamp)}</div>
      <div className="col-span-1">{entry.user}</div>
      <div className="col-span-7">
        <div
          className={`${isExpanded ? "" : "truncate whitespace-nowrap overflow-hidden"}`}
        >
          <span>{entry.action}:</span>
          <span className="ml-1">
            {isExpanded
              ? entry.details.replace(`${entry.action}: `, "")
              : truncateText(entry.details.replace(`${entry.action}: `, ""))}
          </span>
        </div>
      </div>
    </div>
  );
};

interface AuditLogsProps {
  className?: string;
}

export const AuditLogs: React.FC<AuditLogsProps> = ({ className = "" }) => {
  const [displayedLogs, setDisplayedLogs] = useState<AuditLogEntry[]>(
    mockAuditLogs.slice(0, 15),
  );
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(mockAuditLogs.length > 15);
  const [filterText, setFilterText] = useState("");

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const loadMore = async () => {
    setIsLoading(true);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const currentLength = displayedLogs.length;
    const nextBatch = mockAuditLogs.slice(currentLength, currentLength + 15);

    setDisplayedLogs((prev) => [...prev, ...nextBatch]);
    setHasMore(currentLength + nextBatch.length < mockAuditLogs.length);
    setIsLoading(false);
  };

  const filteredLogs = displayedLogs.filter(
    (entry) =>
      filterText === "" ||
      entry.user.toLowerCase().includes(filterText.toLowerCase()) ||
      entry.action.toLowerCase().includes(filterText.toLowerCase()) ||
      entry.details.toLowerCase().includes(filterText.toLowerCase()),
  );

  return (
    <div className={`bg-white ${className}`}>
      {/* Header with filter and date range */}
      <div className="flex items-center gap-4 p-4">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Filter logs..."
            value={filterText}
            onChange={(e) => setFilterText(e.target.value)}
            className="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <svg
            className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        <div className="relative">
          <select className="appearance-none px-3 py-2 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
            <option>Jul 3, 12:34 PM - Jul 1, 12:34 PM</option>
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Custom range</option>
          </select>
          <svg
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </div>

      {/* Table header */}
      <div className="grid grid-cols-12 gap-4 py-3 px-4 text-xs font-medium text-gray-600 tracking-wider font-mono">
        <div className="col-span-2">Date</div>
        <div className="col-span-1">User</div>
        <div className="col-span-7">Action</div>
      </div>

      {/* Audit log entries */}
      <div>
        {filteredLogs.map((entry) => (
          <AuditLogItem
            key={entry.id}
            entry={entry}
            isExpanded={expandedItems.has(entry.id)}
            onToggleExpand={() => toggleExpand(entry.id)}
          />
        ))}
      </div>

      {hasMore && (
        <div className="p-4">
          {isLoading ? (
            <span className="text-gray-600 text-sm">Loading...</span>
          ) : (
            <button
              type="button"
              onClick={loadMore}
              className="text-sm font-medium"
              disabled={isLoading}
            >
              Load more...
            </button>
          )}
        </div>
      )}
    </div>
  );
};
