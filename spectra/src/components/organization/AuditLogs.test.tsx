import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { AuditLogs } from "./AuditLogs";

describe("AuditLogs Component", () => {
  test("renders audit log entries", () => {
    render(<AuditLogs />);

    // Check if log entries are displayed
    expect(screen.getAllByText("Jordan").length).toBeGreaterThan(0);
    expect(screen.getAllByText("Taylor").length).toBeGreaterThan(0);
    expect(screen.getAllByText("Morgan").length).toBeGreaterThan(0);

    // Check if actions are displayed (now with colons)
    expect(screen.getAllByText(/Created device/).length).toBeGreaterThan(0);
    expect(screen.getAllByText(/Edited device/).length).toBeGreaterThan(0);
    expect(screen.getAllByText(/Removed device/).length).toBeGreater<PERSON>han(0);
  });

  test("displays timestamps correctly", () => {
    render(<AuditLogs />);

    // Check if timestamps are formatted correctly
    expect(screen.getAllByText(/07\/03\/2025/).length).toBeGreaterThan(0);
    expect(screen.getAllByText(/12:34 PM/).length).toBeGreaterThan(0);
  });

  test("shows expand/collapse functionality for long text", () => {
    render(<AuditLogs />);

    // Find entries with long text that should have expand buttons
    const expandButtons = screen.queryAllByText("Show more");

    if (expandButtons.length > 0) {
      // Click on the first expand button
      fireEvent.click(expandButtons[0]);

      // Should now show "Show less" button
      expect(screen.getByText("Show less")).toBeInTheDocument();
    } else {
      // If no expand buttons, that's also valid (no long text entries)
      expect(expandButtons.length).toBe(0);
    }
  });

  test('loads more entries when "Load more..." is clicked', async () => {
    render(<AuditLogs />);

    // Initially should show first batch of entries
    const initialEntries = screen.getAllByText(
      /Created device|Edited device|Removed device|Added integration/,
    );
    const initialCount = initialEntries.length;

    // Click "Load more..." button if it exists
    const loadMoreButton = screen.queryByText("Load more...");
    if (loadMoreButton) {
      fireEvent.click(loadMoreButton);

      // Should show loading state
      expect(screen.getByText("Loading...")).toBeInTheDocument();

      // Wait for more entries to load
      await waitFor(
        () => {
          const updatedEntries = screen.getAllByText(
            /Created device|Edited device|Removed device|Added integration/,
          );
          expect(updatedEntries.length).toBeGreaterThan(initialCount);
        },
        { timeout: 2000 },
      );
    } else {
      // If no load more button, all entries are already shown
      expect(loadMoreButton).toBeNull();
    }
  });

  test("applies custom className", () => {
    const { container } = render(<AuditLogs className="custom-class" />);

    expect(container.firstChild).toHaveClass("bg-white");
    expect(container.firstChild).toHaveClass("custom-class");
  });

  test("displays different action types with correct colors", () => {
    render(<AuditLogs />);

    // Check if different action types are displayed
    const createdActions = screen.getAllByText(/Created device/);
    const editedActions = screen.getAllByText(/Edited device/);
    const removedActions = screen.getAllByText(/Removed device/);

    expect(createdActions.length).toBeGreaterThan(0);
    expect(editedActions.length).toBeGreaterThan(0);
    expect(removedActions.length).toBeGreaterThan(0);
  });

  test("displays load more button when there are more entries", () => {
    render(<AuditLogs />);

    // Should show load more button if there are more entries
    const loadMoreButton = screen.queryByText("Load more...");
    expect(loadMoreButton).toBeInTheDocument();
  });
});
