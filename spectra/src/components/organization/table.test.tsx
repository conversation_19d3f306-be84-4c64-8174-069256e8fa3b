import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { Table, type TableColumn } from "./table";

// Mock data for testing
interface TestData {
  id: string;
  name: string;
  value: number;
  status: "active" | "inactive";
}

const mockData: TestData[] = [
  { id: "1", name: "Item 1", value: 100, status: "active" },
  { id: "2", name: "Item 2", value: 200, status: "inactive" },
  { id: "3", name: "Item 3", value: 150, status: "active" },
];

const basicColumns: TableColumn<TestData>[] = [
  { id: "name", header: "Name", accessorKey: "name" },
  { id: "value", header: "Value", accessorKey: "value" },
  { id: "status", header: "Status", accessorKey: "status" },
];

describe("Table Component", () => {
  test("renders table with data", () => {
    render(<Table data={mockData} columns={basicColumns} />);

    // Check headers
    expect(screen.getByText("Name")).toBeInTheDocument();
    expect(screen.getByText("Value")).toBeInTheDocument();
    expect(screen.getByText("Status")).toBeInTheDocument();

    // Check data
    expect(screen.getByText("Item 1")).toBeInTheDocument();
    expect(screen.getByText("100")).toBeInTheDocument();
    expect(screen.getAllByText("active")).toHaveLength(2); // Two items have 'active' status
    expect(screen.getByText("inactive")).toBeInTheDocument();
  });

  test("renders empty state when no data", () => {
    render(
      <Table data={[]} columns={basicColumns} emptyMessage="No items found" />,
    );
    expect(screen.getByText("No items found")).toBeInTheDocument();
  });

  test("renders loading state", () => {
    render(<Table data={mockData} columns={basicColumns} loading={true} />);

    // Should show skeleton loading rows
    const skeletonElements = document.querySelectorAll(".animate-pulse");
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  test("handles row click", () => {
    const onRowClick = jest.fn();
    render(
      <Table data={mockData} columns={basicColumns} onRowClick={onRowClick} />,
    );

    // Click on first row
    const firstRow = screen.getByText("Item 1").closest("tr");
    expect(firstRow).not.toBeNull();
    fireEvent.click(firstRow as HTMLElement);

    expect(onRowClick).toHaveBeenCalledWith(mockData[0]);
  });

  test("renders custom cell content", () => {
    const columnsWithCustomCell: TableColumn<TestData>[] = [
      { id: "name", header: "Name", accessorKey: "name" },
      {
        id: "status",
        header: "Status",
        accessorKey: "status",
        cell: ({ getValue }) => {
          const status = getValue() as string;
          return (
            <span
              className={
                status === "active" ? "text-green-500" : "text-red-500"
              }
            >
              {status.toUpperCase()}
            </span>
          );
        },
      },
    ];

    render(<Table data={mockData} columns={columnsWithCustomCell} />);

    expect(screen.getAllByText("ACTIVE")).toHaveLength(2); // Two items have 'ACTIVE' status
    expect(screen.getByText("INACTIVE")).toBeInTheDocument();
  });

  test("handles sorting", () => {
    const onSortingChange = jest.fn();
    render(
      <Table
        data={mockData}
        columns={basicColumns}
        sorting={{ enableSorting: true }}
        onSortingChange={onSortingChange}
      />,
    );

    // Click on Name header to sort
    const nameHeader = screen.getByText("Name").closest("div");
    expect(nameHeader).not.toBeNull();
    fireEvent.click(nameHeader as HTMLElement);

    expect(onSortingChange).toHaveBeenCalled();
  });

  test("displays sorting arrows correctly", () => {
    const { rerender } = render(
      <Table
        data={mockData}
        columns={basicColumns}
        sorting={{
          enableSorting: true,
          initialSort: [{ id: "name", desc: false }], // Pre-sorted ascending
        }}
      />,
    );

    // Should show up arrow for ascending sort
    expect(screen.getByText("↑")).toBeInTheDocument();

    // Re-render with descending sort
    rerender(
      <Table
        data={mockData}
        columns={basicColumns}
        sorting={{
          enableSorting: true,
          initialSort: [{ id: "name", desc: true }], // Pre-sorted descending
        }}
      />,
    );

    // Should show down arrow for descending sort (there might be multiple due to default arrows)
    expect(screen.getAllByText("↓").length).toBeGreaterThan(0);
  });

  test("handles pagination", () => {
    const onPaginationChange = jest.fn();
    render(
      <Table
        data={mockData}
        columns={basicColumns}
        pagination={{ pageSize: 2, showPagination: true }}
        onPaginationChange={onPaginationChange}
      />,
    );

    // Should show pagination controls
    expect(screen.getByText("Page 1 of 2")).toBeInTheDocument();
    expect(screen.getByText("Showing 1 - 2 of 3 items")).toBeInTheDocument();
  });

  test("disables pagination when showPagination is false", () => {
    render(
      <Table
        data={mockData}
        columns={basicColumns}
        pagination={{ showPagination: false }}
      />,
    );

    // Should not show pagination controls
    expect(screen.queryByText("Page")).not.toBeInTheDocument();
  });

  test("applies custom className", () => {
    const { container } = render(
      <Table data={mockData} columns={basicColumns} className="custom-table" />,
    );

    expect(container.firstChild).toHaveClass("custom-table");
  });

  test("handles keyboard navigation for sortable headers", () => {
    const onSortingChange = jest.fn();
    render(
      <Table
        data={mockData}
        columns={basicColumns}
        sorting={{ enableSorting: true }}
        onSortingChange={onSortingChange}
      />,
    );

    const nameHeader = screen.getByText("Name").closest("div");
    expect(nameHeader).not.toBeNull();

    // Test Enter key
    fireEvent.keyDown(nameHeader as HTMLElement, { key: "Enter" });
    expect(onSortingChange).toHaveBeenCalled();

    // Test Space key
    fireEvent.keyDown(nameHeader as HTMLElement, { key: " " });
    expect(onSortingChange).toHaveBeenCalledTimes(2);
  });

  test("handles keyboard navigation for clickable rows", () => {
    const onRowClick = jest.fn();
    render(
      <Table data={mockData} columns={basicColumns} onRowClick={onRowClick} />,
    );

    const firstRow = screen.getByText("Item 1").closest("tr");
    expect(firstRow).not.toBeNull();

    // Test Enter key
    fireEvent.keyDown(firstRow as HTMLElement, { key: "Enter" });
    expect(onRowClick).toHaveBeenCalledWith(mockData[0]);

    // Test Space key
    fireEvent.keyDown(firstRow as HTMLElement, { key: " " });
    expect(onRowClick).toHaveBeenCalledTimes(2);
  });
});
