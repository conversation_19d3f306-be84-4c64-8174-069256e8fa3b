import {
  type ColumnDef,
  type PaginationState,
  type SortingState,
  type Table as TanStackTable,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";

// Icons removed - now using arrow characters for sorting

// Types
export interface TableColumn<T> {
  id: string;
  header: string;
  accessorKey?: keyof T;
  accessorFn?: (row: T) => unknown;
  cell?: (info: {
    getValue: () => unknown;
    row: { original: T };
    column: { id: string };
  }) => React.ReactNode;
  enableSorting?: boolean;
  sortingFn?: string;
  size?: number;
  minSize?: number;
  maxSize?: number;
}

export interface TablePaginationOptions {
  pageSize?: number;
  pageIndex?: number;
  showPagination?: boolean;
  pageSizeOptions?: number[];
}

export interface TableSortingOptions {
  enableSorting?: boolean;
  enableMultiSort?: boolean;
  initialSort?: Array<{ id: string; desc: boolean }>;
}

export interface TableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  // Pagination options
  pagination?: TablePaginationOptions;
  // Sorting options
  sorting?: TableSortingOptions;
  // Loading and empty states
  loading?: boolean;
  emptyMessage?: string;
  // Styling
  className?: string;
  // Callbacks
  onRowClick?: (row: T) => void;
  onPaginationChange?: (pageIndex: number, pageSize: number) => void;
  onSortingChange?: (sorting: Array<{ id: string; desc: boolean }>) => void;
}

// Loading skeleton component
const LoadingSkeleton: React.FC<{ columns: number; rows?: number }> = ({
  columns,
  rows = 5,
}) => (
  <>
    {Array.from({ length: rows }).map((_, rowIndex) => {
      const isLastRow = rowIndex === rows - 1;
      return (
        <tr key={rowIndex}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <td
              key={colIndex}
              className="p-3 relative"
            >
              <div className="h-4 bg-gray95 rounded animate-pulse" />
              {!isLastRow && (
                <div className="absolute bottom-0 left-3 right-3 border-b border-gray95" />
              )}
            </td>
          ))}
        </tr>
      );
    })}
  </>
);

// Empty state component
const EmptyState: React.FC<{ message: string; columns: number }> = ({
  message,
  columns,
}) => (
  <tr>
    <td colSpan={columns} className="p-8 text-center text-space70">
      {message}
    </td>
  </tr>
);

// Sort arrow component
const SortIndicator: React.FC<{
  isSorted: false | "asc" | "desc";
  canSort: boolean;
}> = ({ isSorted, canSort }) => {
  if (!canSort) return null;

  if (isSorted === "asc") {
    return <span className="text-blue50 text-sm ml-1">↑</span>;
  }

  if (isSorted === "desc") {
    return <span className="text-blue50 text-sm ml-1">↓</span>;
  }

  // Default - down arrow, with no color
  return <span className="text-sm ml-1">↓</span>;
};

// Pagination component
const TablePagination = <T,>({
  table,
  pageSizeOptions,
}: {
  table: TanStackTable<T>;
  pageSizeOptions: number[];
}) => {
  const pageIndex = table.getState().pagination.pageIndex;
  const pageSize = table.getState().pagination.pageSize;
  const pageCount = table.getPageCount();
  const canPreviousPage = table.getCanPreviousPage();
  const canNextPage = table.getCanNextPage();

  // Calculate showing range
  const totalItems = table.getFilteredRowModel().rows.length;
  const startItem = pageIndex * pageSize + 1;
  const endItem = Math.min((pageIndex + 1) * pageSize, totalItems);

  return (
    <div className="flex items-center justify-between px-4 py-3 bg-white">
      {/* Left side: Page size dropdown and items count */}
      <div className="flex items-center gap-4">
        {/* <select
          value={pageSize}
          onChange={(e) => table.setPageSize(Number(e.target.value))}
          className="text-xs border border-gray95 rounded px-2 py-1 text-space50"
        >
          {pageSizeOptions.map((size) => (
            <option key={size} value={size}>
              {size} per page
            </option>
          ))}
        </select> */}

        <span className="text-xs text-space70">
          Showing {totalItems > 0 ? startItem : 0} - {endItem} of {totalItems} items
        </span>
      </div>

      {/* Center: Empty for now, can be used for additional controls */}
      <div className="flex items-center gap-4">
        {/* Reserved space for future controls */}
      </div>

      {/* Right side: Navigation arrows with page info */}
      <div className="flex items-center">

        <div className="flex items-center gap-1">
          <button
            type="button"
            onClick={() => table.firstPage()}
            disabled={!canPreviousPage}
            className="w-8 h-8 flex items-center justify-center text-xs rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray95 text-space50"
          >
            {"<<"}
          </button>
          <button
            type="button"
            onClick={() => table.previousPage()}
            disabled={!canPreviousPage}
            className="w-8 h-8 flex items-center justify-center text-xs rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray95 text-space50"
          >
            {"<"}
          </button>
        </div>

        <span className="text-xs text-space70 whitespace-nowrap mx-2">
          Page {pageIndex + 1} of {pageCount}
        </span>

        <div className="flex items-center gap-1">
          <button
            type="button"
            onClick={() => table.nextPage()}
            disabled={!canNextPage}
            className="w-8 h-8 flex items-center justify-center text-xs rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray95 text-space50"
          >
            {">"}
          </button>
          <button
            type="button"
            onClick={() => table.lastPage()}
            disabled={!canNextPage}
            className="w-8 h-8 flex items-center justify-center text-xs rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray95 text-space50"
          >
            {">>"}
          </button>
        </div>
      </div>
    </div>
  );
};

// Main Table Component
export const Table = <T,>({
  data,
  columns,
  pagination = {},
  sorting = {},
  loading = false,
  emptyMessage = "No data available",
  className = "",
  onRowClick,
  onPaginationChange,
  onSortingChange,
}: TableProps<T>) => {
  // Default pagination options
  const {
    pageSize = 10,
    pageIndex = 0,
    showPagination = true,
    pageSizeOptions = [5, 10, 20, 50, 100],
  } = pagination;

  // Default sorting options
  const {
    enableSorting = true,
    enableMultiSort = false,
    initialSort = [],
  } = sorting;

  // Internal state for pagination and sorting
  const [paginationState, setPaginationState] = useState<PaginationState>({
    pageIndex,
    pageSize,
  });

  const [sortingState, setSortingState] = useState<SortingState>(initialSort);

  // Transform our simplified column definitions to TanStack format
  const tableColumns = useMemo<ColumnDef<T>[]>(() => {
    return columns.map((col): ColumnDef<T> => {
      const baseColumn: Partial<ColumnDef<T>> = {
        id: col.id,
        header: col.header,
        enableSorting: col.enableSorting ?? enableSorting,
        size: col.size,
        minSize: col.minSize,
        maxSize: col.maxSize,
      };

      // Add sorting function if provided
      if (col.sortingFn) {
        (baseColumn as Record<string, unknown>).sortingFn = col.sortingFn;
      }

      // Add cell renderer if provided
      if (col.cell) {
        baseColumn.cell = (info) => col.cell?.(info);
      }

      // Handle accessor - either key or function
      if (col.accessorKey) {
        return {
          ...baseColumn,
          accessorKey: col.accessorKey as string,
        } as ColumnDef<T>;
      }

      if (col.accessorFn) {
        return {
          ...baseColumn,
          accessorFn: col.accessorFn,
        } as ColumnDef<T>;
      }

      // Display column (no data accessor)
      return baseColumn as ColumnDef<T>;
    });
  }, [columns, enableSorting]);

  // Create table instance
  const table = useReactTable({
    data,
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableSorting,
    enableMultiSort,
    state: {
      pagination: paginationState,
      sorting: sortingState,
    },
    onPaginationChange: (updater) => {
      const newPagination =
        typeof updater === "function" ? updater(paginationState) : updater;
      setPaginationState(newPagination);
      onPaginationChange?.(newPagination.pageIndex, newPagination.pageSize);
    },
    onSortingChange: (updater) => {
      const newSorting =
        typeof updater === "function" ? updater(sortingState) : updater;
      setSortingState(newSorting);
      onSortingChange?.(newSorting);
    },
  });

  return (
    <div
      className={`bg-white overflow-hidden ${className}`}
    >
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Header */}
          <thead className="bg-white">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-4 py-3 text-left text-xs font-normal text-space70 uppercase tracking-wider relative"
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder ? null : (
                      <div
                        className={`flex items-center gap-2 ${
                          header.column.getCanSort()
                            ? "cursor-pointer select-none hover:text-space40"
                            : ""
                        }`}
                        onClick={header.column.getToggleSortingHandler()}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" || e.key === " ") {
                            e.preventDefault();
                            header.column.getToggleSortingHandler()?.(e);
                          }
                        }}
                        role={header.column.getCanSort() ? "button" : undefined}
                        tabIndex={header.column.getCanSort() ? 0 : undefined}
                        aria-label={
                          header.column.getCanSort()
                            ? `Sort by ${header.column.columnDef.header}`
                            : undefined
                        }
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                        <SortIndicator
                          isSorted={header.column.getIsSorted()}
                          canSort={header.column.getCanSort()}
                        />
                      </div>
                    )}
                    {/* Segmented border for header */}
                    <div className="absolute bottom-0 left-4 right-4 border-b border-gray95" />
                  </th>
                ))}
              </tr>
            ))}
          </thead>

          {/* Body */}
          <tbody className="bg-white">
            {loading ? (
              <LoadingSkeleton columns={columns.length} />
            ) : table.getRowModel().rows.length === 0 ? (
              <EmptyState message={emptyMessage} columns={columns.length} />
            ) : (
              table.getRowModel().rows.map((row, rowIndex) => {
                const isLastRow = rowIndex === table.getRowModel().rows.length - 1;
                return (
                  <tr
                    key={row.id}
                    className={`hover:bg-gray97 transition-colors ${
                      onRowClick ? "cursor-pointer" : ""
                    }`}
                    onClick={() => onRowClick?.(row.original)}
                    onKeyDown={(e) => {
                      if (onRowClick && (e.key === "Enter" || e.key === " ")) {
                        e.preventDefault();
                        onRowClick(row.original);
                      }
                    }}
                    role={onRowClick ? "button" : undefined}
                    tabIndex={onRowClick ? 0 : undefined}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className="px-4 py-4 text-sm text-space50 relative"
                        style={{ width: cell.column.getSize() }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                        {!isLastRow && (
                          <div className="absolute bottom-0 left-4 right-4 border-b border-gray95" />
                        )}
                      </td>
                    ))}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {showPagination && !loading && (
        <TablePagination table={table} pageSizeOptions={pageSizeOptions} />
      )}
    </div>
  );
};

// Export the component as default
export default Table;
