import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import type React from "react";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import type { SearchHintOverlayProps } from "../uikit/SearchHintOverlay/SearchHintOverlay";
import { SearchModal } from "../uikit/SearchModal/SearchModal";
import { SearchResultSection } from "../uikit/SearchResultSection/SearchResultSection";

// Create a mock JumpBar context for the story
interface JumpBarContextType {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  isLoading: boolean;
}

const MockJumpBarContext = createContext<JumpBarContextType>({
  isOpen: false,
  setIsOpen: () => {},
  isLoading: false,
});

// Create a hook to use our mock context
const useMockJumpBar = () => useContext(MockJumpBarContext);

// Create a custom SearchHintOverlay component that uses our mock context
const MockSearchHintOverlay = ({
  className = "",
  text,
  position = "bottom-right",
  detectPlatform = true,
  keyboardShortcut,
}: SearchHintOverlayProps) => {
  const { setIsOpen } = useMockJumpBar();

  // Determine which key to display
  const isMac = navigator.userAgent.includes("Mac");
  const displayKey =
    keyboardShortcut || (detectPlatform && isMac ? "⌘" : "Ctrl");

  // Default text if not provided
  const displayText = text || `Press ${displayKey} + K to search`;

  // Position classes
  const positionClasses = {
    "bottom-right": "bottom-8 right-4",
    "bottom-left": "bottom-8 left-4",
    "top-right": "top-8 right-4",
    "top-left": "top-8 left-4",
  };

  // Handle click to open JumpBar
  const handleClick = () => {
    setIsOpen(true);
    console.log("Search overlay clicked - opening JumpBar");
  };

  // Handle keyboard interaction for accessibility
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      setIsOpen(true);
      console.log("Search overlay activated via keyboard - opening JumpBar");
    }
  };

  return (
    <button
      type="button"
      className={`fixed ${positionClasses[position]} text-space80 text-xs bg-white/80 px-3 py-1.5 rounded-full border border-space90 backdrop-blur-sm cursor-pointer ${className}`}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      aria-label="Open search"
    >
      {displayText}
    </button>
  );
};

// Define types for our mock data
interface BaseItem {
  id: string;
  name: string;
  type?: string;
  originalItem?: BaseItem;
  [key: string]: unknown;
}

interface DeviceItem extends BaseItem {
  type: string;
}

interface MonitorItem extends BaseItem {
  resourceId: string;
}

interface IntegrationItem extends BaseItem {
  integrationType: string;
}

// Mock data
const mockSites: BaseItem[] = [
  { id: "site-1", name: "Main Campus" },
  { id: "site-2", name: "Downtown Office" },
  { id: "site-3", name: "Research Facility" },
];

const mockFleets: BaseItem[] = [
  { id: "fleet-1", name: "City Fleet" },
  { id: "fleet-2", name: "Campus Fleet" },
];

const mockDevices: DeviceItem[] = [
  {
    id: "device-1",
    name: "Charger 1",
    type: "Charger",
    originalItem: { id: "device-1", name: "Charger 1", type: "Charger" },
  },
  {
    id: "device-2",
    name: "Battery Pack A",
    type: "Battery",
    originalItem: { id: "device-2", name: "Battery Pack A", type: "Battery" },
  },
  {
    id: "device-3",
    name: "Energy Meter 2",
    type: "Meter",
    originalItem: { id: "device-3", name: "Energy Meter 2", type: "Meter" },
  },
  {
    id: "device-4",
    name: "Swap Station 1",
    type: "SwapStation",
    originalItem: {
      id: "device-4",
      name: "Swap Station 1",
      type: "SwapStation",
    },
  },
];

const mockMonitors: MonitorItem[] = [
  {
    id: "monitor-1",
    name: "Test Monitor",
    resourceId: "resource-1",
    originalItem: {
      id: "monitor-1",
      name: "Test Monitor",
      resourceId: "resource-1",
    },
  },
  {
    id: "monitor-2",
    name: "Other Monitor",
    resourceId: "resource-2",
    originalItem: {
      id: "monitor-2",
      name: "Other Monitor",
      resourceId: "resource-2",
    },
  },
  {
    id: "monitor-3",
    name: "Battery Health Monitor",
    resourceId: "resource-3",
    originalItem: {
      id: "monitor-3",
      name: "Battery Health Monitor",
      resourceId: "resource-3",
    },
  },
];

const mockIntegrations: IntegrationItem[] = [
  {
    id: "integration-1",
    name: "SMS",
    integrationType: "SMS",
    originalItem: {
      id: "integration-1",
      name: "SMS",
      integrationType: "SMS",
    },
  },
  {
    id: "integration-2",
    name: "Email",
    integrationType: "Email",
    originalItem: {
      id: "integration-2",
      name: "Email",
      integrationType: "Email",
    },
  },
  {
    id: "integration-3",
    name: "Webhook",
    integrationType: "Webhook",
    originalItem: {
      id: "integration-3",
      name: "Webhook",
      integrationType: "Webhook",
    },
  },
];

// Simple search function to filter items
const filterItems = <T extends BaseItem>(items: T[], query: string): T[] => {
  if (!query.trim()) return items;

  const lowerQuery = query.toLowerCase();
  return items.filter(
    (item) =>
      item.name.toLowerCase().includes(lowerQuery) ||
      item.id.toLowerCase().includes(lowerQuery) ||
      ("type" in item &&
        typeof item.type === "string" &&
        item.type.toLowerCase().includes(lowerQuery)),
  );
};

// Create a JumpBar story container component that includes both JumpBar and SearchHintOverlay
const JumpBarStoryContainer = ({ initialOpen = false, isLoading = false }) => {
  const [isOpen, setIsOpen] = useState(initialOpen);
  const [search, setSearch] = useState("");
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const resultsContainerRef = useRef<HTMLDivElement>(null);
  const selectedItemRef = useRef<HTMLDivElement>(null);

  // Filter the mock data based on search input
  const filteredSites = filterItems(mockSites, search);
  const filteredFleets = filterItems(mockFleets, search);
  const filteredDevices = filterItems(mockDevices, search);
  const filteredMonitors = filterItems(mockMonitors, search);
  const filteredIntegrations = filterItems(mockIntegrations, search);

  // Combine all filtered items for keyboard navigation
  const allFilteredItems = useMemo(
    () => [
      ...filteredSites,
      ...filteredFleets,
      ...filteredDevices,
      ...filteredMonitors,
      ...filteredIntegrations,
    ],
    [
      filteredSites,
      filteredFleets,
      filteredDevices,
      filteredMonitors,
      filteredIntegrations,
    ],
  );

  // Reset selection when search changes
  // biome-ignore lint/correctness/useExhaustiveDependencies: need to reset on search change
  useEffect(() => {
    setSelectedIndex(-1);
  }, [search]);

  // Scroll selected item into view
  useEffect(() => {
    if (
      selectedIndex >= 0 &&
      selectedItemRef.current &&
      resultsContainerRef.current
    ) {
      selectedItemRef.current.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });
    }
  }, [selectedIndex]);

  // Handle keyboard navigation
  const handleKeyNavigation = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setSelectedIndex((prev) =>
            prev < allFilteredItems.length - 1 ? prev + 1 : prev,
          );
          break;
        case "ArrowUp":
          e.preventDefault();
          setSelectedIndex((prev) => (prev > -1 ? prev - 1 : -1));
          break;
        case "Enter":
          e.preventDefault();
          if (selectedIndex >= 0 && selectedIndex < allFilteredItems.length) {
            const selectedItem = allFilteredItems[selectedIndex];
            alert(`Selected: ${selectedItem.name} (${selectedItem.id})`);
          }
          break;
      }
    },
    [selectedIndex, allFilteredItems],
  );

  // Handle global keyboard shortcuts
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault();
        setIsOpen(true);
      } else if (e.key === "Escape" && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleGlobalKeyDown);
    return () => document.removeEventListener("keydown", handleGlobalKeyDown);
  }, [isOpen]);

  // Handle item clicks
  const handleItemClick = (item: BaseItem) => {
    alert(`Clicked: ${item.name} (${item.id})`);
  };

  // Handle item key down
  const handleItemKeyDown = (e: React.KeyboardEvent, item: BaseItem) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      alert(`Selected via keypress: ${item.name} (${item.id})`);
    }
  };

  // Keyboard shortcuts
  const shortcuts = [
    { key: "↑↓", description: "to navigate" },
    { key: "enter", description: "to select" },
    { key: "esc", description: "to close" },
  ];

  // Search input component
  const searchInput = (
    <input
      type="text"
      className="w-full px-4 py-2 text-lg border rounded-lg"
      placeholder="Search sites, fleets and devices..."
      value={search}
      onChange={(e) => setSearch(e.target.value)}
      onKeyDown={handleKeyNavigation}
      // biome-ignore lint/a11y/noAutofocus: need autofocus
      autoFocus
    />
  );

  // Create context value
  const contextValue = {
    isOpen,
    setIsOpen,
    isLoading,
  };

  return (
    <MockJumpBarContext.Provider value={contextValue}>
      <div className="h-screen w-screen bg-gray-100">
        {/* SearchHintOverlay */}
        <MockSearchHintOverlay position="bottom-right" />

        {/* JumpBar */}
        {isOpen && (
          <SearchModal
            isOpen={isOpen}
            searchInput={searchInput}
            resultsContainerRef={resultsContainerRef}
            isLoading={isLoading}
            search={search}
            shortcuts={shortcuts}
            noResultsMessage="No results found for"
          >
            {/* Sites Section */}
            <SearchResultSection
              title="Sites"
              items={filteredSites}
              search={search}
              selectedIndex={selectedIndex}
              baseIndex={0}
              selectedItemRef={selectedItemRef}
              onItemClick={handleItemClick}
              onItemKeyDown={handleItemKeyDown}
            />

            {/* Fleets Section */}
            <SearchResultSection
              title="Fleets"
              items={filteredFleets}
              search={search}
              selectedIndex={selectedIndex}
              baseIndex={filteredSites.length}
              selectedItemRef={selectedItemRef}
              onItemClick={handleItemClick}
              onItemKeyDown={handleItemKeyDown}
            />

            {/* Devices Section */}
            <SearchResultSection
              title="Devices"
              items={filteredDevices}
              search={search}
              selectedIndex={selectedIndex}
              baseIndex={filteredSites.length + filteredFleets.length}
              selectedItemRef={selectedItemRef}
              onItemClick={handleItemClick}
              onItemKeyDown={handleItemKeyDown}
              showDeviceType={true}
            />

            {/* Monitors Section */}
            <SearchResultSection
              title="Monitors"
              items={filteredMonitors}
              search={search}
              selectedIndex={selectedIndex}
              baseIndex={
                filteredSites.length +
                filteredFleets.length +
                filteredDevices.length
              }
              selectedItemRef={selectedItemRef}
              onItemClick={handleItemClick}
              onItemKeyDown={handleItemKeyDown}
            />

            {/* Integrations Section */}
            <SearchResultSection
              title="Integrations"
              items={filteredIntegrations}
              search={search}
              selectedIndex={selectedIndex}
              baseIndex={
                filteredSites.length +
                filteredFleets.length +
                filteredDevices.length +
                filteredMonitors.length
              }
              selectedItemRef={selectedItemRef}
              onItemClick={handleItemClick}
              onItemKeyDown={handleItemKeyDown}
            />
          </SearchModal>
        )}
      </div>
    </MockJumpBarContext.Provider>
  );
};

// Simple wrapper for backward compatibility
const JumpBarStory = ({ isOpen = true, isLoading = false }) => {
  return <JumpBarStoryContainer initialOpen={isOpen} isLoading={isLoading} />;
};

// Define the meta for the story
const meta: Meta<typeof JumpBarStory> = {
  title: "Components/JumpBar",
  component: JumpBarStory,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  argTypes: {
    isOpen: {
      control: "boolean",
      description: "Whether the JumpBar is open",
    },
    isLoading: {
      control: "boolean",
      description: "Whether the JumpBar is loading data",
    },
  },
};

// Define the story type
type Story = StoryObj<typeof JumpBarStory>;

// Define story variants
export const Default: Story = {
  args: {
    isOpen: true,
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    isOpen: true,
    isLoading: true,
  },
};

export const Closed: Story = {
  args: {
    isOpen: false,
    isLoading: false,
  },
};

// Create a new story component for the complete search experience
const CompleteSearchExperienceStory = () => {
  return <JumpBarStoryContainer initialOpen={false} isLoading={false} />;
};

// Add the complete search experience story
export const CompleteSearchExperience: StoryObj<
  typeof CompleteSearchExperienceStory
> = {
  render: () => <CompleteSearchExperienceStory />,
  parameters: {
    docs: {
      description: {
        story: `
          This story demonstrates the complete search experience with both the JumpBar and SearchHintOverlay components.

          **Features:**
          - Click on the "Press Cmd/Ctrl+K to search" hint to open the JumpBar
          - Press Cmd/Ctrl+K to open the JumpBar with keyboard shortcut
          - Search for sites, fleets, and devices
          - Navigate results with arrow keys
          - Press Enter to select an item
          - Press Escape to close the JumpBar
        `,
      },
    },
  },
};

// Export the meta as default
export default meta;
