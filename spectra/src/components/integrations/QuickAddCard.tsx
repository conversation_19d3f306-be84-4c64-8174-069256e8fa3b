import {
  type EgressIntegrationType,
  type IngressIntegrationType,
  IntegrationDirection,
} from "api/data/integrations";
import { BsBoxArrowRight, BsCloudArrowDown } from "react-icons/bs";

interface QuickAddCardProps {
  title: string;
  description: string;
  direction: IntegrationDirection;
  type: IngressIntegrationType | EgressIntegrationType;
  icon?: string;
  onClick: (
    direction: IntegrationDirection,
    type: IngressIntegrationType | EgressIntegrationType,
  ) => void;
}

const QuickAddCard = ({
  title,
  description,
  direction,
  type,
  onClick,
}: QuickAddCardProps) => {
  const handleClick = () => {
    onClick(direction, type);
  };

  return (
    <div
      className="bg-blue60 rounded-lg py-3 px-4 hover:bg-blue70 hover:translate-y-[-2px] shadow-md hover:shadow-lg transition-all cursor-pointer"
      onClick={handleClick}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          handleClick();
        }
      }}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className="text-heading2 text-blue95">{title}</h4>
            {direction === IntegrationDirection.INGRESS ? (
              <BsCloudArrowDown className="text-blue95 text-2xl" />
            ) : (
              <BsBoxArrowRight className="text-blue95 text-2xl" />
            )}
          </div>
          <p className="text-[10px] font-mono text-blue90">{direction}</p>
          <p className="text-caption text-blue95">{description}</p>
        </div>
      </div>
    </div>
  );
};

export default QuickAddCard;
