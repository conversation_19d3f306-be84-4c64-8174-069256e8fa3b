import { useVerificationApi } from "api/ingestion/verification";
import { useState } from "react";
import Modal, { type ModalError } from "../uikit/modal";
import TextField from "../uikit/textField";

type PhoneVerificationModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onVerified: (phoneNumber: string) => void;
};

export const PhoneVerificationModal = ({
  isOpen,
  onClose,
  onVerified,
}: PhoneVerificationModalProps) => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [hasConsent, setHasConsent] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState<ModalError | null>(null);
  const [showCodeInput, setShowCodeInput] = useState(false);

  const { startPhoneNumberVerification, checkPhoneNumberVerification } =
    useVerificationApi();

  const handleStartVerification = async () => {
    if (!hasConsent) {
      setError({
        message: "Please provide consent to continue",
        action: { label: "Back", onClick: () => setError(null) },
      });
      return;
    }

    setIsVerifying(true);
    setError(null);

    try {
      const response = await startPhoneNumberVerification(phoneNumber);
      if (response.success) {
        setShowCodeInput(true);
      } else {
        setError({
          message:
            response.message ||
            "Failed to start verification. Please try again.",
          action: { label: "Back", onClick: () => setError(null) },
        });
      }
    } catch (err) {
      setError({
        message:
          "An error occurred while starting verification. Please try again.",
        action: { label: "Back", onClick: () => setError(null) },
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleVerifyCode = async () => {
    setIsVerifying(true);
    setError(null);

    try {
      const response = await checkPhoneNumberVerification(
        phoneNumber,
        verificationCode,
      );
      if (response.isValid) {
        onVerified(phoneNumber);
        handleClose();
      } else {
        setError({
          message: "Invalid verification code. Please try again.",
          action: { label: "Back", onClick: () => setError(null) },
        });
      }
    } catch (err) {
      setError({
        message:
          "An error occurred while verifying the code. Please try again.",
        action: { label: "Back", onClick: () => setError(null) },
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleClose = () => {
    setPhoneNumber("");
    setVerificationCode("");
    setHasConsent(false);
    setError(null);
    setShowCodeInput(false);
    onClose();
  };

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      title="Add Phone Number"
      error={error}
      actions={
        !showCodeInput
          ? {
              cancel: {
                label: "Cancel",
                onClick: handleClose,
              },
              confirm: {
                label: isVerifying ? "Sending..." : "Send Verification Code",
                onClick: handleStartVerification,
                disabled: !phoneNumber || !hasConsent || isVerifying,
                variant: "primary",
              },
            }
          : {
              cancel: {
                label: "Back",
                onClick: () => {
                  setShowCodeInput(false);
                  setVerificationCode("");
                },
              },
              confirm: {
                label: isVerifying ? "Verifying..." : "Verify Code",
                onClick: handleVerifyCode,
                disabled: verificationCode.length === 0 || isVerifying,
                variant: "primary",
              },
            }
      }
    >
      <div className="mt-4 space-y-4">
        {!showCodeInput ? (
          <>
            <TextField
              label="Phone Number"
              value={phoneNumber}
              onChange={setPhoneNumber}
              placeholder="Enter phone number"
              type="tel"
              required
            />

            <div className="flex items-start gap-2">
              <input
                type="checkbox"
                id="consent"
                checked={hasConsent}
                onChange={(e) => setHasConsent(e.target.checked)}
                className="mt-1"
              />
              <label htmlFor="consent" className="text-sm text-space70">
                By submitting this form and entering your phone number, you
                agree to receieve SMS and/or phone calls to this number. You
                will recieve a verification code to your phone number. Consent
                is not a condition of any of the features of the Spectra
                Platform. Message and data rates may apply. Reply STOP to
                cancel.
              </label>
            </div>
          </>
        ) : (
          <>
            <div className="text-body text-space70">
              A verification code has been sent to your phone number. Please
              enter it below. If you do not receive a code, please check your
              phone number and try again.
            </div>
            <TextField
              label="Verification Code"
              value={verificationCode}
              onChange={setVerificationCode}
              placeholder="Enter verification code"
              type="text"
              required
            />
          </>
        )}
      </div>
    </Modal>
  );
};
