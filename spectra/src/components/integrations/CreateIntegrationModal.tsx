import {
  EgressIntegrationType,
  IngressIntegrationType,
  IntegrationDirection,
  MULTI_VALUE_FIELD_TYPES,
  type MultiValueFieldType,
} from "api/data/integrations";
import { useAuth } from "context/AuthContext";
import { ReactComponent as DeleteIcon } from "images/icons/delete.svg";
import { useEffect, useState } from "react";
import { useIntegrationsApi } from "../../api/ingestion/integrations";
import type {
  IntegrationTypesResponse,
  Metadata,
  Property,
  Resource,
} from "../../pages/integrations/types";
import ButtonComponent from "../uikit/button";
import Modal from "../uikit/modal";
import SelectField from "../uikit/selectField";
import TextField from "../uikit/textField";
import {
  AuthType,
  IntegrationPropertiesForm,
} from "./IntegrationPropertiesForm";

type CreateIntegrationModalProps = {
  open: boolean;
  onClose: () => void;
  onCreated?: () => void;
  preSelectedDirection?: IntegrationDirection;
  preSelectedType?: IngressIntegrationType | EgressIntegrationType;
};

const CreateIntegrationModal = ({
  open,
  onClose,
  onCreated,
  preSelectedDirection,
  preSelectedType,
}: CreateIntegrationModalProps) => {
  const { user } = useAuth();
  const { createIntegration, getIntegrationTypes, validatePath } =
    useIntegrationsApi();

  const [integrationTypes, setIntegrationTypes] =
    useState<IntegrationTypesResponse>({});
  const [selectedDirection, setSelectedDirection] = useState<string>(
    preSelectedDirection || IntegrationDirection.INGRESS,
  );
  const [selectedType, setSelectedType] = useState<string>(
    preSelectedType || IngressIntegrationType.UtilityAPI,
  );
  const [integrationName, setIntegrationName] = useState("");
  const [resources, setResources] = useState<Resource[]>([]);
  const [properties, setProperties] = useState<Property[]>([]);
  const [metadata, setMetadata] = useState<Metadata[]>([]);
  const [createError, setCreateError] = useState<string | null>(null);
  const [hasInitializedDefaults, setHasInitializedDefaults] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [secretProperties, setSecretProperties] = useState<
    Record<string, string>
  >({});

  const getAuthType = () => {
    const authTypeProp = properties.find((p) => p.key === "authType");
    return (authTypeProp?.values[0] as AuthType) || AuthType.NONE;
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: getIntegrationTypes is not a dependency of this effect
  useEffect(() => {
    if (open && !hasInitializedDefaults) {
      getIntegrationTypes().then(setIntegrationTypes);
      setHasInitializedDefaults(true);
      if (preSelectedDirection) {
        setSelectedDirection(preSelectedDirection);
      }
      if (preSelectedType) {
        setSelectedType(preSelectedType);
      }
    }
  }, [open, hasInitializedDefaults, preSelectedDirection, preSelectedType]);

  useEffect(() => {
    if (!open) {
      setHasInitializedDefaults(false);
      setSecretProperties({});
    }
  }, [open]);

  const getTypeProperties = () => {
    if (
      !selectedDirection ||
      !selectedType ||
      !integrationTypes[selectedDirection]?.[selectedType]
    ) {
      return [];
    }
    return integrationTypes[selectedDirection][selectedType];
  };

  const handleAddResource = () => {
    setResources([
      ...resources,
      { id: `resource-${Date.now()}`, path: "", metadata: [] },
    ]);
  };

  const handleRemoveResource = (id: string) => {
    setResources(resources.filter((r) => r.id !== id));
  };

  const handleResourceChange = (id: string, field: string, value: string) => {
    const newResources = resources.map((resource) => {
      if (resource.id !== id) return resource;
      if (field === "path") {
        return { ...resource, path: value };
      }
      return {
        ...resource,
        metadata: resource.metadata.map((meta) =>
          meta.id === field ? { ...meta, value } : meta,
        ),
      };
    });
    setResources(newResources);
  };

  const handleAddResourceMetadata = (resourceId: string) => {
    setResources(
      resources.map((resource) =>
        resource.id === resourceId
          ? {
              ...resource,
              metadata: [
                ...resource.metadata,
                { id: `meta-${Date.now()}`, key: "", value: "" },
              ],
            }
          : resource,
      ),
    );
  };

  const handleRemoveResourceMetadata = (resourceId: string, metaId: string) => {
    setResources(
      resources.map((resource) =>
        resource.id === resourceId
          ? {
              ...resource,
              metadata: resource.metadata.filter((m) => m.id !== metaId),
            }
          : resource,
      ),
    );
  };

  const handleResourceMetadataChange = (
    resourceId: string,
    metaId: string,
    field: "key" | "value",
    value: string,
  ) => {
    setResources(
      resources.map((resource) =>
        resource.id === resourceId
          ? {
              ...resource,
              metadata: resource.metadata.map((meta) =>
                meta.id === metaId ? { ...meta, [field]: value } : meta,
              ),
            }
          : resource,
      ),
    );
  };

  const handleAddMetadata = () => {
    setMetadata([
      ...metadata,
      { id: `meta-${Date.now()}`, key: "", value: "" },
    ]);
  };

  const handleRemoveMetadata = (id: string) => {
    setMetadata(metadata.filter((m) => m.id !== id));
  };

  const handleMetadataChange = (
    id: string,
    field: "key" | "value",
    value: string,
  ) => {
    const newMetadata = metadata.map((meta) =>
      meta.id === id ? { ...meta, [field]: value } : meta,
    );
    setMetadata(newMetadata);
  };

  const handleCreate = async () => {
    setIsCreating(true);
    setCreateError(null);

    const pathValidationPromises = resources.map(async (resource) => {
      const isValid = await validatePath(resource.path);
      return { resource, isValid };
    });

    const pathValidationResults = await Promise.all(pathValidationPromises);
    const invalidPaths = pathValidationResults.filter(
      (result) => !result.isValid,
    );

    if (invalidPaths.length > 0) {
      setCreateError(
        "One or more resource paths are invalid. Please check your entries.",
      );
      setIsCreating(false);
      return;
    }

    const resourcesData = resources.map((r) => ({
      path: r.path,
      metadata: r.metadata.reduce(
        (acc, meta) => {
          if (meta.key) acc[meta.key] = meta.value;
          return acc;
        },
        {} as Record<string, string>,
      ),
    }));

    const propertiesData = properties.reduce(
      (acc, prop) => {
        if (prop.key && prop.values.length > 0) {
          acc[prop.key] = prop.values;
        }
        return acc;
      },
      {} as Record<string, string[]>,
    );

    const metadataData = metadata.reduce(
      (acc, meta) => {
        if (meta.key && meta.value) {
          acc[meta.key] = meta.value;
        }
        return acc;
      },
      {} as Record<string, string>,
    );

    const result = await createIntegration({
      integrationName,
      integrationDirection: selectedDirection,
      integrationType: selectedType,
      resources: resourcesData,
      properties: propertiesData,
      metaData: metadataData,
      ...(Object.keys(secretProperties).length > 0 && { secretProperties }),
    });

    if (result.success) {
      setIntegrationName("");
      setSelectedDirection(IntegrationDirection.INGRESS);
      setSelectedType(IngressIntegrationType.UtilityAPI);
      setResources([]);
      setProperties([]);
      setMetadata([]);
      setSecretProperties({});
      setHasInitializedDefaults(false);

      if (onCreated) onCreated();
      handleClose();
    } else {
      setCreateError(
        result.error?.errors?.[0]?.message ||
          result.error?.message ||
          "Failed to create integration",
      );
    }
    setIsCreating(false);
  };

  const isConfirmDisabled = () => {
    if (isCreating || !integrationName || !selectedType || !selectedDirection) {
      return true;
    }
    if (
      selectedType === EgressIntegrationType.WEBHOOK &&
      getAuthType() !== AuthType.NONE
    ) {
      const currentAuthType = getAuthType();
      switch (currentAuthType) {
        case AuthType.BASIC:
        case AuthType.DIGEST: {
          if (!secretProperties.username || !secretProperties.password)
            return true;
          break;
        }
      }
    }

    // Check for any multi-value fields that are required
    const hasMultiValueProperty = getTypeProperties().some((prop) =>
      MULTI_VALUE_FIELD_TYPES.includes(prop.name as MultiValueFieldType),
    );

    if (hasMultiValueProperty) {
      const multiValueTypes = getTypeProperties()
        .filter((prop) =>
          MULTI_VALUE_FIELD_TYPES.includes(prop.name as MultiValueFieldType),
        )
        .map((prop) => prop.name as MultiValueFieldType);

      for (const type of multiValueTypes) {
        const items = properties.find((p) => p.key === type)?.values || [];
        const hasValidValue = items.some((value) => value.trim() !== "");
        if (!hasValidValue) {
          return true;
        }
      }
    }

    return false;
  };

  const handleClose = () => {
    // reset all state
    setHasInitializedDefaults(false);
    setIntegrationName("");
    setResources([]);
    setProperties([]);
    setSecretProperties({});
    setMetadata([]);
    setSelectedDirection(IntegrationDirection.INGRESS);
    setSelectedType(IngressIntegrationType.UtilityAPI);
    setCreateError(null);
    setIsCreating(false);

    // call onClose
    onClose();
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      title="Create New Integration"
      actions={{
        cancel: {
          label: "Cancel",
          onClick: handleClose,
        },
        confirm: {
          label: isCreating ? "Creating..." : "Create Integration",
          onClick: handleCreate,
          disabled: isConfirmDisabled(),
        },
      }}
    >
      <div className="mt-4 space-y-4">
        <TextField
          name="name"
          label="Integration Name"
          required
          value={integrationName}
          onChange={setIntegrationName}
        />
        <SelectField
          name="direction"
          label="Direction"
          required
          value={selectedDirection}
          options={[
            { value: IntegrationDirection.INGRESS, label: "Ingress" },
            { value: IntegrationDirection.EGRESS, label: "Egress" },
          ]}
          onChange={(value) => {
            setSelectedDirection(value);
            if (value === IntegrationDirection.EGRESS) {
              setSelectedType(EgressIntegrationType.WEBHOOK);
              setProperties([
                {
                  key: "authType",
                  values: [AuthType.NONE],
                },
              ]);
            } else {
              setSelectedType("");
            }
            setProperties([]);
            setSecretProperties({});
          }}
        />
        {selectedDirection && integrationTypes[selectedDirection] && (
          <SelectField
            name="type"
            label="Integration Type"
            required
            value={selectedType}
            options={
              selectedDirection === IntegrationDirection.INGRESS
                ? Object.values(IngressIntegrationType).map((type) => ({
                    value: type,
                    label: type,
                  }))
                : Object.values(EgressIntegrationType).map((type) => ({
                    value: type,
                    label: type,
                  }))
            }
            onChange={(value) => {
              setSelectedType(value);
              setProperties([]);
              if (value !== EgressIntegrationType.WEBHOOK) {
                setSecretProperties({});
              }
            }}
          />
        )}

        {getTypeProperties().length > 0 && (
          <IntegrationPropertiesForm
            typeProperties={getTypeProperties().map((prop) => ({
              ...prop,
              isEditable: true,
            }))}
            properties={properties}
            setProperties={setProperties}
            selectedType={selectedType}
            selectedDirection={selectedDirection}
            secretProperties={secretProperties}
            setSecretProperties={setSecretProperties}
          />
        )}

        <div className="space-y-4 mt-4">
          <div className="flex justify-between items-center">
            <div className="text-heading2 text-space50">Resources</div>
            <ButtonComponent.Pill
              variant="outline"
              buttonStyle="default"
              onClick={handleAddResource}
            >
              + Add Resource
            </ButtonComponent.Pill>
          </div>

          {resources.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No resources added yet
            </div>
          ) : (
            <div className="space-y-4">
              {resources.map((resource) => (
                <div
                  key={resource.id}
                  className="p-3 rounded-lg outline outline-2 outline-offset-[-2px] outline-space90 transition-all hover:outline-space70"
                >
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <div className="text-xs font-medium text-gray-700 mb-1">
                        Resource Path
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          value={resource.path}
                          onChange={(e) =>
                            handleResourceChange(
                              resource.id,
                              "path",
                              e.target.value,
                            )
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                          placeholder="Enter resource path..."
                        />
                        <button
                          type="button"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleRemoveResource(resource.id)}
                        >
                          <DeleteIcon />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-xs font-medium text-gray-700">
                        Metadata
                      </div>
                      <ButtonComponent.Pill
                        variant="outline"
                        buttonStyle="default"
                        onClick={() => handleAddResourceMetadata(resource.id)}
                      >
                        + Add Metadata
                      </ButtonComponent.Pill>
                    </div>

                    {resource.metadata.length === 0 ? (
                      <div className="text-center py-4 text-gray-500 text-sm">
                        No metadata added yet
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {resource.metadata.map((meta) => (
                          <div key={meta.id} className="flex gap-2">
                            <input
                              type="text"
                              value={meta.key}
                              onChange={(e) =>
                                handleResourceMetadataChange(
                                  resource.id,
                                  meta.id,
                                  "key",
                                  e.target.value,
                                )
                              }
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                              placeholder="Key"
                            />
                            <input
                              type="text"
                              value={meta.value}
                              onChange={(e) =>
                                handleResourceMetadataChange(
                                  resource.id,
                                  meta.id,
                                  "value",
                                  e.target.value,
                                )
                              }
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                              placeholder="Value"
                            />
                            <button
                              type="button"
                              className="text-red-500 hover:text-red-700"
                              onClick={() =>
                                handleRemoveResourceMetadata(
                                  resource.id,
                                  meta.id,
                                )
                              }
                            >
                              <DeleteIcon />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="text-heading2 text-space50">Metadata</div>
            <ButtonComponent.Pill
              variant="outline"
              buttonStyle="default"
              onClick={handleAddMetadata}
            >
              + Add Metadata
            </ButtonComponent.Pill>
          </div>

          {metadata.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No metadata added yet
            </div>
          ) : (
            <div className="space-y-2">
              {metadata.map((meta) => (
                <div key={meta.id} className="flex gap-2">
                  <input
                    type="text"
                    value={meta.key}
                    onChange={(e) =>
                      handleMetadataChange(meta.id, "key", e.target.value)
                    }
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="Key"
                  />
                  <input
                    type="text"
                    value={meta.value}
                    onChange={(e) =>
                      handleMetadataChange(meta.id, "value", e.target.value)
                    }
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="Value"
                  />
                  <button
                    type="button"
                    className="text-red-500 hover:text-red-700"
                    onClick={() => handleRemoveMetadata(meta.id)}
                  >
                    <DeleteIcon />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {createError && (
          <div className="text-red-500 text-sm mt-4">{createError}</div>
        )}
      </div>
    </Modal>
  );
};

export default CreateIntegrationModal;
