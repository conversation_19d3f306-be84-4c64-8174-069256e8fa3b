import { Link } from "react-router-dom";
import type { IntegrationResponse } from "../../api/ingestion/integrations";

type IntegrationCardProps = {
  integration: IntegrationResponse;
};

const IntegrationCard = ({ integration }: IntegrationCardProps) => {
  return (
    <Link
      to={`/integrations/${integration.integrationId}`}
      className="hover:no-underline"
    >
      <div className="p-4 mb-4 rounded-md shadow border border-zinc-300 transition-transform hover:-translate-y-0.5 hover:shadow-md">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-heading3 text-space50">
              {integration.integrationName}
            </p>
            <p className="text-xs text-gray-500 font-mono">
              {integration.integrationId}
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-gray-500">Type</p>
            <p className="text-sm text-black">{integration.integrationType}</p>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default IntegrationCard;
