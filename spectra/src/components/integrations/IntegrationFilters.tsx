import { useEffect, useState } from "react";
import type { IntegrationResponse } from "../../api/ingestion/integrations";
import SelectField from "../uikit/selectField";
import TextField from "../uikit/textField";

type IntegrationFiltersProps = {
  integrations: IntegrationResponse[];
  onFilterChange: (filteredIntegrations: IntegrationResponse[]) => void;
};

const IntegrationFilters = ({
  integrations,
  onFilterChange,
}: IntegrationFiltersProps) => {
  const [nameFilter, setNameFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");

  useEffect(() => {
    const filteredIntegrations = integrations.filter((integration) => {
      const matchesName = integration.integrationName
        .toLowerCase()
        .includes(nameFilter.toLowerCase());
      const matchesType =
        !typeFilter || integration.integrationType === typeFilter;

      return matchesName && matchesType;
    });

    onFilterChange(filteredIntegrations);
  }, [integrations, nameFilter, typeFilter, onFilterChange]);

  const typeOptions = [
    { value: "", label: "All Types" },
    ...Array.from(new Set(integrations.map((i) => i.integrationType))).map(
      (type) => ({ value: type, label: type }),
    ),
  ];

  return (
    <div className="flex flex-col gap-4 mb-4">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-[200px]">
          <TextField
            label="Name"
            value={nameFilter}
            onChange={setNameFilter}
            placeholder="Filter by name..."
          />
        </div>

        <div className="w-[200px]">
          <SelectField
            label="Type"
            value={typeFilter}
            options={typeOptions}
            onChange={setTypeFilter}
            placeholder="Select type"
          />
        </div>
      </div>
    </div>
  );
};

export default IntegrationFilters;
