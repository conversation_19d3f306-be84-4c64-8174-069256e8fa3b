import type { IntegrationAction } from "api/data/integrations";
import { useIntegrationsApi } from "api/ingestion/integrations";
import { useState } from "react";
import Modal from "../uikit/modal";
import TextField from "../uikit/textField";

type IntegrationActionModalProps = {
  open: boolean;
  onClose: () => void;
  integrationId: string;
  action: IntegrationAction | null;
};

export const IntegrationActionModal = ({
  open,
  onClose,
  integrationId,
  action,
}: IntegrationActionModalProps) => {
  const [params, setParams] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { doIntegrationAction } = useIntegrationsApi();

  const handleParamChange = (param: string, value: string) => {
    setParams((prev) => ({ ...prev, [param]: value }));
  };

  const handleExecute = async () => {
    if (!action) return;

    setIsLoading(true);
    setError(null);

    try {
      await doIntegrationAction(integrationId, action.name, params);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to execute action");
    } finally {
      setIsLoading(false);
    }
  };

  if (!action) return null;

  return (
    <Modal
      open={open}
      onClose={onClose}
      title={`Execute ${action.name}`}
      error={error ? { message: error } : undefined}
      actions={{
        cancel: {
          label: "Cancel",
          onClick: onClose,
        },
        confirm: {
          label: "Execute",
          onClick: handleExecute,
          disabled:
            isLoading || action.actionParams.some((param) => !params[param]),
          variant: "primary",
        },
      }}
    >
      <div className="mt-4 space-y-4">
        <p className="text-sm text-gray-600">{action.description}</p>
        {action.actionParams?.map((param) => (
          <TextField
            key={param}
            label={param}
            value={params[param] || ""}
            onChange={(value) => handleParamChange(param, value)}
            required
          />
        ))}
      </div>
    </Modal>
  );
};
