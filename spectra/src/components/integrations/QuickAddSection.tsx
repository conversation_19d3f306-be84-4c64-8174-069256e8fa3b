import type { ReactNode } from "react";

interface QuickAddSectionProps {
  children: ReactNode;
}

const QuickAddSection = ({ children }: QuickAddSectionProps) => {
  return (
    <div className="my-4">
      <div className="mb-4">
        <h3 className="text-heading2 text-space50">Add New Integration</h3>
        <p className="text-caption text-space70">
          Select an integration type to get started
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {children}
      </div>
    </div>
  );
};

export default QuickAddSection;
