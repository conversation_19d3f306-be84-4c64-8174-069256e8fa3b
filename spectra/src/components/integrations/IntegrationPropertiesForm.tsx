import {
  EgressIntegrationType,
  IntegrationDirection,
  MULTI_VALUE_FIELD_LABELS,
  MULTI_VALUE_FIELD_TYPES,
  MultiValueFieldType,
} from "api/data/integrations";
import { ReactComponent as DeleteIcon } from "images/icons/delete.svg";
import { useState } from "react";
import type { Property } from "../../pages/integrations/types";
import ButtonComponent from "../uikit/button";
import SelectField from "../uikit/selectField";
import TextField from "../uikit/textField";
import { PhoneVerificationModal } from "./PhoneVerificationModal";

export enum AuthType {
  NONE = "NONE",
  BASIC = "BASIC",
  DIGEST = "DIGEST",
}

export const AUTH_TYPES = Object.values(AuthType);

export type IntegrationPropertiesFormProps = {
  typeProperties: Array<{
    name: string;
    description: string;
    isEditable?: boolean;
  }>;
  properties: Property[];
  setProperties: (properties: Property[]) => void;
  selectedType: string;
  selectedDirection: string;
  secretProperties?: Record<string, string>;
  setSecretProperties?: (properties: Record<string, string>) => void;
};

export const IntegrationPropertiesForm = ({
  typeProperties,
  properties,
  setProperties,
  selectedType,
  selectedDirection,
  secretProperties = {},
  setSecretProperties,
}: IntegrationPropertiesFormProps) => {
  const [showPhoneVerification, setShowPhoneVerification] = useState(false);
  const [pendingPhoneType, setPendingPhoneType] =
    useState<MultiValueFieldType | null>(null);

  const getAuthType = () => {
    const authTypeProp = properties.find((p) => p.key === "authType");
    return (authTypeProp?.values[0] as AuthType) || AuthType.NONE;
  };

  const validateMultiValue = (
    value: string,
    type: MultiValueFieldType,
  ): string | undefined => {
    if (!value.trim()) {
      return `${type} is required`;
    }

    switch (type) {
      case MultiValueFieldType.EMAIL: {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // TODO: fix this validation
        return emailRegex.test(value)
          ? undefined
          : "Please enter a valid email address";
      }
      case MultiValueFieldType.PHONE: {
        const phoneRegex = /^\+?[\d\s-()]+$/; // TODO: fix this validation
        return phoneRegex.test(value)
          ? undefined
          : "Please enter a valid phone number";
      }
      case MultiValueFieldType.URL: {
        try {
          new URL(value);
          return undefined;
        } catch {
          return "Please enter a valid URL";
        }
      }
      default:
        return undefined;
    }
  };

  const handleAddMultiValue = (type: MultiValueFieldType) => {
    if (type === MultiValueFieldType.PHONE) {
      setPendingPhoneType(type);
      setShowPhoneVerification(true);
      return;
    }

    const existingProp = properties.find((p) => p.key === type);
    if (existingProp) {
      setProperties(
        properties.map((p) =>
          p.key === type ? { ...p, values: [...p.values, ""] } : p,
        ),
      );
    } else {
      setProperties([...properties, { key: type, values: [""] }]);
    }
  };

  const handleRemoveMultiValue = (type: MultiValueFieldType, index: number) => {
    const existingProp = properties.find((p) => p.key === type);
    if (existingProp) {
      const newValues = [...existingProp.values];
      newValues.splice(index, 1);
      if (newValues.length === 0) {
        setProperties(properties.filter((p) => p.key !== type));
      } else {
        setProperties(
          properties.map((p) =>
            p.key === type ? { ...p, values: newValues } : p,
          ),
        );
      }
    }
  };

  const handleMultiValueChange = (
    type: MultiValueFieldType,
    index: number,
    value: string,
  ) => {
    const existingProp = properties.find((p) => p.key === type);
    if (existingProp) {
      const newValues = [...existingProp.values];
      newValues[index] = value;
      setProperties(
        properties.map((p) =>
          p.key === type ? { ...p, values: newValues } : p,
        ),
      );
    } else {
      setProperties([...properties, { key: type, values: [value] }]);
    }
  };

  const handlePhoneVerified = (phoneNumber: string) => {
    if (!pendingPhoneType) return;

    const existingProp = properties.find((p) => p.key === pendingPhoneType);
    if (existingProp) {
      setProperties(
        properties.map((p) =>
          p.key === pendingPhoneType
            ? { ...p, values: [...p.values, phoneNumber] }
            : p,
        ),
      );
    } else {
      setProperties([
        ...properties,
        { key: pendingPhoneType, values: [phoneNumber] },
      ]);
    }
  };

  const setAuthTypeValue = (value: AuthType) => {
    const existingProp = properties.find((p) => p.key === "authType");
    if (existingProp) {
      setProperties(
        properties.map((p) =>
          p.key === "authType" ? { ...p, values: [value] } : p,
        ),
      );
    } else {
      setProperties([...properties, { key: "authType", values: [value] }]);
    }
    // Reset auth credentials when auth type changes
    setSecretProperties({});
  };

  const currentAuthType = getAuthType();

  const renderAuthFields = () => {
    switch (currentAuthType) {
      case AuthType.BASIC:
      case AuthType.DIGEST:
        return (
          <div className="space-y-2">
            <TextField
              required
              label="Username"
              value={secretProperties.username || ""}
              onChange={(value) => {
                if (setSecretProperties) {
                  setSecretProperties({
                    ...secretProperties,
                    username: value,
                  });
                }
              }}
              placeholder="Enter username"
            />
            <TextField
              required
              label="Password"
              type="password"
              value={secretProperties.password || ""}
              onChange={(value) => {
                if (setSecretProperties) {
                  setSecretProperties({
                    ...secretProperties,
                    password: value,
                  });
                }
              }}
              placeholder="Enter password"
            />
          </div>
        );
      default:
        return null;
    }
  };

  const isAuthTypeEditable = typeProperties.find(
    (p) => p.name === "authType",
  )?.isEditable;

  return (
    <div className="space-y-4">
      {typeProperties.map((prop) => {
        const isMultiValue = MULTI_VALUE_FIELD_TYPES.includes(
          prop.name as MultiValueFieldType,
        );
        const isEditable = prop.isEditable !== false; // default to true if not specified

        if (isMultiValue) {
          const values =
            properties.find((p) => p.key === prop.name)?.values || [];

          return (
            <div key={prop.name}>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="text-heading2 text-space50">
                    {MULTI_VALUE_FIELD_LABELS[prop.name]}
                  </div>
                  {isEditable && (
                    <ButtonComponent.Pill
                      variant="outline"
                      buttonStyle="default"
                      onClick={() =>
                        handleAddMultiValue(prop.name as MultiValueFieldType)
                      }
                    >
                      + Add {MULTI_VALUE_FIELD_LABELS[prop.name]}
                    </ButtonComponent.Pill>
                  )}
                </div>

                <div className="space-y-2">
                  {values.map((value, index) => (
                    <div key={`${prop.name}-${index}`} className="space-y-1">
                      <div className="flex items-center gap-2">
                        <div className="flex-1">
                          <TextField
                            required
                            label={`${MULTI_VALUE_FIELD_LABELS[prop.name]} ${index + 1}`}
                            value={value}
                            onChange={(newValue) =>
                              handleMultiValueChange(
                                prop.name as MultiValueFieldType,
                                index,
                                newValue,
                              )
                            }
                            placeholder={prop.description}
                            error={validateMultiValue(
                              value,
                              prop.name as MultiValueFieldType,
                            )}
                            disabled={
                              !isEditable || prop.name === "recipientPhone"
                            }
                          />
                        </div>
                        {isEditable && (
                          <button
                            type="button"
                            className="text-red-500 hover:text-red-700 mt-6"
                            onClick={() =>
                              handleRemoveMultiValue(
                                prop.name as MultiValueFieldType,
                                index,
                              )
                            }
                          >
                            <DeleteIcon />
                          </button>
                        )}
                      </div>
                    </div>
                  ))}

                  {values.length === 0 && (
                    <div className="text-caption text-space70">
                      No{" "}
                      <span className="lowercase">
                        {MULTI_VALUE_FIELD_LABELS[prop.name]}
                      </span>{" "}
                      added yet
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        }

        return prop.name === "authType" ? (
          <SelectField
            key={prop.name}
            label="Auth Type"
            value={currentAuthType}
            options={AUTH_TYPES.map((type) => ({
              value: type,
              label: type.charAt(0) + type.slice(1).toLowerCase(),
            }))}
            onChange={setAuthTypeValue}
            placeholder="Select authentication type"
            disabled={!isEditable}
          />
        ) : (
          <TextField
            key={prop.name}
            required
            label={
              prop.name.charAt(0).toUpperCase() +
              prop.name.slice(1).replace(/([A-Z])/g, " $1")
            }
            value={properties.find((p) => p.key === prop.name)?.values[0] || ""}
            onChange={(value) => {
              const existingProp = properties.find((p) => p.key === prop.name);
              if (existingProp) {
                setProperties(
                  properties.map((p) =>
                    p.key === prop.name ? { ...p, values: [value] } : p,
                  ),
                );
              } else {
                setProperties([
                  ...properties,
                  { key: prop.name, values: [value] },
                ]);
              }
            }}
            placeholder={prop.description}
            disabled={!isEditable}
          />
        );
      })}

      {selectedDirection === IntegrationDirection.EGRESS &&
        selectedType === EgressIntegrationType.WEBHOOK &&
        currentAuthType !== AuthType.NONE &&
        isAuthTypeEditable &&
        renderAuthFields()}

      <PhoneVerificationModal
        isOpen={showPhoneVerification}
        onClose={() => {
          setShowPhoneVerification(false);
          setPendingPhoneType(null);
        }}
        onVerified={handlePhoneVerified}
      />
    </div>
  );
};
