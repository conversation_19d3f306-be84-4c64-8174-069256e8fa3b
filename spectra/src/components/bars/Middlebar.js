import { BsDot } from "react-icons/bs";

const MiddleBar = ({ pageTree, header, subheadings, displayProps }) => {
  return (
    <div className="flex flex-col">
      <div className="flex items-center text-center">
        <div className="md:text-md mt-2 flex flex-1 items-center">
          {pageTree !== undefined &&
            pageTree?.map((data, index) => (
              <div key={index} className="flex">
                <p className="mx-1">{data}</p>
                {index !== pageTree.length - 1 && <>{`>`}</>}
              </div>
            ))}
        </div>
        {/* <div className="flex w-fit text-gray-500 md:text-sm text-xs">
          <p className="px-3">{`Day`}</p>
          <p className="px-3">{`Month`}</p>
          <p className="px-3">{`Year`}</p>
          <p className="px-3">{`YTD`}</p>
          <p className="px-3">{`All-time`}</p>
        </div> */}
      </div>
      <div className="flex">
        <div className="mr-10 flex flex-col">
          <div className="text-heading1 text-space50 my-2 font-bold">
            {header !== undefined ? header : ""}
          </div>
          <div className="text-heading3 text-space50 flex items-center">
            {subheadings?.map((data, index) => (
              <div className="flex" key={index}>
                <div>{data}</div>
                {index !== subheadings.length - 1 && <BsDot fontSize={35} />}
              </div>
            ))}
          </div>
        </div>
        <div className="flex flex-1">
          {displayProps !== undefined &&
            Object.keys(displayProps).map((key) => (
              <div
                className="mr-3 h-min rounded-lg border-2 border-white p-1 shadow-inner"
                key={key}
              >
                {key}
                <div className="mx-2 my-1 flex w-fit rounded-md bg-white px-2 py-1 text-black shadow-md">
                  {displayProps[key]}
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default MiddleBar;
