import type { Timeseries } from "api/data";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

import { useEffect, useState } from "react";
import {
  formatLabelWithUnit,
  formatNumericValue,
  formatTimeAxis,
  CHART_FONTS,
  AXIS_CONFIG,
  CHART_MESSAGES,
  CHART_MARGINS,
  CHART_TYPE_CONFIG,
} from "./foundation";

export const RawChart = ({
  timeseries,
  colors,
}: {
  timeseries: Timeseries | null;
  colors: string[];
}) => {
  const [data, setData] = useState<any[]>([]);
  useEffect(() => {
    if (!timeseries) return;

    setData(
      timeseries.values.map((v) => ({
        time: +new Date(v.time),
        ...v,
      })),
    );
  }, [timeseries]);

  if (!timeseries) {
    return <p className="text-space70 text-caption">{CHART_MESSAGES.noData}</p>;
  }

  if (!data.length) {
    return (
      <p className="text-space70 text-caption">{CHART_MESSAGES.loading}</p>
    );
  }

  return (
    <>
      <ResponsiveContainer width="100%" height={250}>
        <LineChart data={data} margin={CHART_MARGINS.chargerPower}>
          <Tooltip
            formatter={(value) => {
              if (typeof value === "number") {
                return formatNumericValue(value);
              }
              return value;
            }}
          />
          <XAxis
            dataKey="time"
            stroke={AXIS_CONFIG.x.stroke}
            tick={{ fontSize: CHART_FONTS.tick }}
            interval={AXIS_CONFIG.x.interval}
            tickFormatter={(dateTime) =>
              formatTimeAxis(dateTime, "numeric", "numeric")
            }
          />
          <YAxis stroke={AXIS_CONFIG.y.stroke} />

          {timeseries.types.map((type, index) => (
            <Line
              key={index}
              type="monotone"
              dataKey={type}
              stroke={colors[index]}
              dot={CHART_TYPE_CONFIG.line.dot}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
      <div className="flex justify-center items-center gap-1">
        {timeseries.types.map((type, index) => (
          <div
            key={index}
            className="px-1 py-2 text-caption text-space50 border-t-4"
            style={{ borderColor: colors[index] }}
          >
            {formatLabelWithUnit(type, timeseries.units[index])}
          </div>
        ))}
      </div>
    </>
  );
};
