import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { DeviceStates } from "hooks/useDeviceState";
import { DeviceStateDonutChart } from "./DeviceStateDonutChart";

// Color mapping for device states (matching Dashboard component)
const deviceStateColors = {
  [DeviceStates.ONLINE]: "#6E9F66", // green
  [DeviceStates.OFFLINE]: "#C55B57", // red
  [DeviceStates.UNKNOWN]: "#E87D48", // orange
  [DeviceStates.UNINITIALIZED]: "#D4D5D6", // gray
};

const meta: Meta<typeof DeviceStateDonutChart> = {
  title: "Charts/DeviceStateDonutChart",
  component: DeviceStateDonutChart,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A donut chart showing the distribution of device states with customizable colors and sizing.",
      },
    },
  },
  argTypes: {
    stateCounts: {
      description: "Object containing count of devices for each state",
    },
    colorMap: {
      description: "Color mapping for each device state",
    },
    isLoading: {
      description: "Whether the chart is in loading state",
      control: "boolean",
    },
    totalDevices: {
      description: "Total number of devices",
      control: "number",
    },
    height: {
      description: "Chart height in pixels or CSS value",
      control: "number",
    },
    innerRadius: {
      description: "Inner radius of the donut",
      control: "number",
    },
    outerRadius: {
      description: "Outer radius of the donut",
      control: "number",
    },
    paddingAngle: {
      description: "Padding angle between segments",
      control: "number",
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    stateCounts: {
      [DeviceStates.ONLINE]: 45,
      [DeviceStates.OFFLINE]: 12,
      [DeviceStates.UNKNOWN]: 8,
      [DeviceStates.UNINITIALIZED]: 5,
    },
    colorMap: deviceStateColors,
    isLoading: false,
    totalDevices: 70,
  },
};

export const OnlyOnlineDevices: Story = {
  args: {
    stateCounts: {
      [DeviceStates.ONLINE]: 25,
      [DeviceStates.OFFLINE]: 0,
      [DeviceStates.UNKNOWN]: 0,
      [DeviceStates.UNINITIALIZED]: 0,
    },
    colorMap: deviceStateColors,
    isLoading: false,
    totalDevices: 25,
  },
};

export const MostlyOffline: Story = {
  args: {
    stateCounts: {
      [DeviceStates.ONLINE]: 3,
      [DeviceStates.OFFLINE]: 22,
      [DeviceStates.UNKNOWN]: 1,
      [DeviceStates.UNINITIALIZED]: 0,
    },
    colorMap: deviceStateColors,
    isLoading: false,
    totalDevices: 26,
  },
};

export const Loading: Story = {
  args: {
    stateCounts: {
      [DeviceStates.ONLINE]: 0,
      [DeviceStates.OFFLINE]: 0,
      [DeviceStates.UNKNOWN]: 0,
      [DeviceStates.UNINITIALIZED]: 0,
    },
    colorMap: deviceStateColors,
    isLoading: true,
    totalDevices: 0,
  },
};

export const NoDevices: Story = {
  args: {
    stateCounts: {
      [DeviceStates.ONLINE]: 0,
      [DeviceStates.OFFLINE]: 0,
      [DeviceStates.UNKNOWN]: 0,
      [DeviceStates.UNINITIALIZED]: 0,
    },
    colorMap: deviceStateColors,
    isLoading: false,
    totalDevices: 0,
  },
};

export const SmallChart: Story = {
  args: {
    stateCounts: {
      [DeviceStates.ONLINE]: 15,
      [DeviceStates.OFFLINE]: 8,
      [DeviceStates.UNKNOWN]: 2,
      [DeviceStates.UNINITIALIZED]: 1,
    },
    colorMap: deviceStateColors,
    isLoading: false,
    totalDevices: 26,
    height: 150,
    innerRadius: 30,
    outerRadius: 60,
  },
};

export const LargeChart: Story = {
  args: {
    stateCounts: {
      [DeviceStates.ONLINE]: 120,
      [DeviceStates.OFFLINE]: 35,
      [DeviceStates.UNKNOWN]: 18,
      [DeviceStates.UNINITIALIZED]: 7,
    },
    colorMap: deviceStateColors,
    isLoading: false,
    totalDevices: 180,
    height: 300,
    innerRadius: 60,
    outerRadius: 120,
    paddingAngle: 3,
  },
};
