import type { Timeseries } from "api/data";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON>ltip,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

import { useEffect, useState } from "react";
import { typeToLabel } from "utils/typeToLabel";
import {
  AXIS_CONFIG,
  CHART_COLORS,
  CHART_MARGINS,
  CHART_MESSAGES,
  CHART_TYPE_CONFIG,
  formatNumericValue,
  formatTimeAxis,
} from "./foundation";

type StackedChartDataType = {
  time: string;
  [key: string]: number | string;
};

export const StackedChart = ({
  timeseries,
  colors,
}: {
  timeseries: Timeseries | null;
  colors: string[];
}) => {
  const [data, setData] = useState<StackedChartDataType[]>([]);
  useEffect(() => {
    if (!timeseries) return;

    setData(
      timeseries.values.map((v) => ({
        time: +new Date(v.time),
        ...v,
      })),
    );
  }, [timeseries]);

  if (!timeseries) {
    return <p className="text-space70 text-caption">{CHART_MESSAGES.noData}</p>;
  }

  if (!data.length) {
    return (
      <p className="text-space70 text-caption">{CHART_MESSAGES.loading}</p>
    );
  }

  const tooltipFormatter = (
    value: number | string,
    name: string,
    props: { dataKey: string },
  ) => {
    const unit = timeseries.units[timeseries.types.indexOf(props.dataKey)];
    const formattedValue = formatNumericValue(value);
    return `${formattedValue} ${unit}`;
  };

  const formatDateString = (timestamp: number | string): string => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  return (
    <>
      {timeseries.types.map((type, index) => (
        <div key={type}>
          <div className="text-xs text-space50 font-bold mb-2">
            {typeToLabel(type)}
          </div>
          <ResponsiveContainer width="100%" height={80}>
            <LineChart
              data={data}
              syncId="stacked"
              margin={CHART_MARGINS.micro}
            >
              <Tooltip
                labelFormatter={formatDateString}
                formatter={tooltipFormatter}
                allowEscapeViewBox={{ x: false, y: true }}
              />
              <XAxis
                dataKey="time"
                stroke={AXIS_CONFIG.x.stroke}
                fontSize={AXIS_CONFIG.x.fontSize}
                interval={AXIS_CONFIG.x.interval}
                tickFormatter={(time: number) =>
                  formatTimeAxis(time, "numeric", "numeric")
                }
                axisLine={AXIS_CONFIG.x.axisLine}
              />
              <YAxis
                stroke={AXIS_CONFIG.y.stroke}
                fontSize={AXIS_CONFIG.y.fontSize}
              />
              <Line
                type="monotone"
                dataKey={type}
                stroke={colors[index] || CHART_COLORS.primary}
                dot={CHART_TYPE_CONFIG.line.dot}
                name={typeToLabel(type)}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      ))}
    </>
  );
};
