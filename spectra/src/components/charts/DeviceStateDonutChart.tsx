// TODO: TBD probably generalize this to be a generic donut chart

import { useMemo } from "react";
import { Cell, Pie, PieChart } from "recharts";

import { DeviceStates } from "hooks/useDeviceState";
import { ChartContainer } from "./foundation/ChartContainer";
import { StandardTooltip } from "./foundation/StandardTooltip";

// Define device state labels for better display
const deviceStateLabels = {
  [DeviceStates.ONLINE]: "Online",
  [DeviceStates.OFFLINE]: "Offline",
  [DeviceStates.UNKNOWN]: "Unknown",
  [DeviceStates.UNINITIALIZED]: "Uninitialized",
};

interface DeviceStateDonutChartProps {
  /** Device state counts */
  stateCounts: Record<DeviceStates, number>;
  /** Color mapping for device states */
  colorMap: Record<DeviceStates, string>;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Total number of devices */
  totalDevices: number;
  /** Chart height */
  height?: number | string;
  /** Inner radius of the donut */
  innerRadius?: number;
  /** Outer radius of the donut */
  outerRadius?: number;
  /** Padding angle between segments */
  paddingAngle?: number;
}

/**
 * Donut chart showing device state distribution
 */
export const DeviceStateDonutChart: React.FC<DeviceStateDonutChartProps> = ({
  stateCounts,
  colorMap,
  isLoading = false,
  totalDevices,
  height = 192, // h-48 equivalent (12rem = 192px)
  innerRadius = 40,
  outerRadius = 80,
  paddingAngle = 2,
}) => {
  // Prepare data for pie chart
  const pieChartData = useMemo(() => {
    return Object.entries(stateCounts)
      .filter(([, count]) => count > 0) // Only show states with devices
      .map(([state, count]) => ({
        name: deviceStateLabels[state as DeviceStates],
        value: count,
        fill: colorMap[state as DeviceStates],
        state: state as DeviceStates,
      }));
  }, [stateCounts, colorMap]);

  const hasData = totalDevices > 0 && pieChartData.length > 0;

  return (
    <ChartContainer
      height={height}
      isLoading={isLoading}
      hasData={hasData}
      loadingMessage="Loading device data..."
      noDataMessage="No devices found"
    >
      <PieChart>
        <Pie
          data={pieChartData}
          cx="50%"
          cy="50%"
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          paddingAngle={paddingAngle}
          dataKey="value"
        >
          {pieChartData.map((entry) => (
            <Cell key={`cell-${entry.state}`} fill={entry.fill} />
          ))}
        </Pie>
        <StandardTooltip
          valueFormatter={(value: number, name: string) =>
            `${value} ${name} Devices`
          }
        />
      </PieChart>
    </ChartContainer>
  );
};

export default DeviceStateDonutChart;
