import type { FleetResponse, SiteResponse } from "api/ingestion/places";
import type { Thing } from "api/ingestion/things";
import { Pin } from "components";

import { mapboxConfig } from "configs/mapbox";
import { useEffect, useMemo, useRef } from "react";
import { Map as MapboxMap, Marker } from "react-map-gl";
import {
  type Coordinate,
  US_CENTER_LOCATION,
  getCenterOfCoordinates,
} from "utils/geolocation";

const colorFor = (thing: string) => {
  // generate a color based on scale of hue
  const hue =
    thing.split("").reduce((acc, char) => {
      return acc + char.charCodeAt(0);
    }, 0) % 360;
  return `hsl(${hue}, 40%, 50%)`;
};

const MarkerForPlace = ({ place }: { place: SiteResponse | FleetResponse }) => {
  return (
    <Marker
      longitude={place.longitude}
      latitude={place.latitude}
      anchor="center"
      className="flex flex-row"
    >
      <Pin
        color={"#6db5d1"}
        id={1}
        size={28}
        locationId={place.siteId ?? place.fleetId}
        locationName={place.siteName ?? place.fleetName}
      />
    </Marker>
  );
};

const MarkerForDevice = ({ device }: { device: Thing }) => {
  const color = useMemo(() => colorFor(device.thingType), [device.thingType]);
  return (
    <Marker
      longitude={device.longitude}
      latitude={device.latitude}
      anchor="center"
      className="flex flex-row"
    >
      <Pin color={color} size={20} locationName={device.thingName} />
    </Marker>
  );
};

export const NetworkMap = ({
  place,
  devices,
}: {
  place: SiteResponse | FleetResponse;
  devices: Record<string, Thing[]>;
}) => {
  const mapRef = useRef<typeof MapboxMap>(null);

  const placeCoordinates =
    place.latitude && place.longitude
      ? {
          latitude: Number.parseFloat(place.latitude),
          longitude: Number.parseFloat(place.longitude),
        }
      : null;

  const center = useMemo<Coordinate | null>(() => {
    const coordinates = Object.entries(devices).flatMap(
      ([_, deviceList]) => deviceList,
    );
    if (coordinates.length === 0) return null;
    return getCenterOfCoordinates(coordinates);
  }, [devices]);

  const displayCoordinates = placeCoordinates ?? center ?? US_CENTER_LOCATION; // default to center of US

  // biome-ignore lint/correctness/useExhaustiveDependencies: add mapRef.current so it re-renders when the map is ready
  useEffect(() => {
    if (mapRef.current) {
      mapRef.current.flyTo({
        center: [displayCoordinates.longitude, displayCoordinates.latitude],
        zoom: 10,
      });
    }
  }, [displayCoordinates, mapRef.current]);

  return (
    <MapboxMap
      ref={mapRef}
      key={`${displayCoordinates.latitude}-${displayCoordinates.longitude}`}
      initialViewState={{
        longitude: displayCoordinates.longitude,
        latitude: displayCoordinates.latitude,
        zoom: 12,
      }}
      mapStyle="mapbox://styles/mapbox/light-v11"
      mapboxAccessToken={mapboxConfig.token}
      style={{
        flex: 1,
      }}
    >
      {placeCoordinates && <MarkerForPlace place={place} />}
      {Object.entries(devices).map(([deviceType, deviceList]) =>
        deviceList.map((device) => (
          <MarkerForDevice key={device.thingId} device={device} />
        )),
      )}
    </MapboxMap>
  );
};
