import type React from "react";
import { useMemo, useState } from "react";

interface GridChartProps<T> {
  keyField: string;
  elements: T[];
  tooltipFormatter?: (element: T) => string | React.ReactNode;
  colorMap?: Map<unknown, string> | Record<string, string>;
  defaultColor?: string;
  gap?: string;
  className?: string;
}

const GridChart = <T extends Record<string, unknown>>({
  keyField,
  elements,
  tooltipFormatter,
  colorMap,
  defaultColor = "#e5e7eb",
  gap = "2px",
  className = "",
}: GridChartProps<T>) => {
  const [hoveredElement, setHoveredElement] = useState<T | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Sort elements by group size (largest groups first)
  const sortedElements = useMemo(() => {
    if (elements.length === 0) return [];

    // Group elements by keyField value
    const groups = elements.reduce(
      (acc, element) => {
        const value = String(element[keyField]);
        if (!acc[value]) {
          acc[value] = [];
        }
        acc[value].push(element);
        return acc;
      },
      {} as Record<string, T[]>,
    );

    // Sort groups by size (descending) and flatten
    return Object.entries(groups)
      .sort(([, a], [, b]) => b.length - a.length)
      .flatMap(([, groupElements]) => groupElements);
  }, [elements, keyField]);

  // Calculate grid dimensions to make it as square as possible
  const gridDimensions = useMemo(() => {
    const count = sortedElements.length;
    if (count === 0) return { cols: 0, rows: 0 };

    const sqrt = Math.sqrt(count);
    const cols = Math.ceil(sqrt);
    const rows = Math.ceil(count / cols);

    return { cols, rows };
  }, [sortedElements.length]);

  // Get color for a given value
  const getColor = (value: unknown): string => {
    if (!colorMap) return defaultColor;

    if (colorMap instanceof Map) {
      return colorMap.get(value) || defaultColor;
    }

    // Handle object colorMap
    return (colorMap as Record<string, string>)[String(value)] || defaultColor;
  };

  // Handle mouse events
  const handleMouseEnter = (element: T, event: React.MouseEvent) => {
    setHoveredElement(element);
    setMousePosition({ x: event.clientX, y: event.clientY });
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    setMousePosition({ x: event.clientX, y: event.clientY });
  };

  const handleMouseLeave = () => {
    setHoveredElement(null);
  };

  return (
    <div className={`relative w-full ${className}`}>
      <div
        className="relative w-full"
        style={{
          display: "grid",
          gridTemplateColumns: `repeat(${gridDimensions.cols}, minmax(0, 1fr))`,
          gridTemplateRows: `repeat(${gridDimensions.rows}, minmax(0, 1fr))`,
          gap: gap,
          aspectRatio: `${gridDimensions.cols} / ${gridDimensions.rows}`,
        }}
      >
        {sortedElements.map((element, index) => {
          const value = element[keyField];
          const color = getColor(value);

          return (
            <div
              key={`${keyField}-${String(value)}-${index}`}
              className="cursor-pointer transition-all duration-200 hover:scale-110 hover:shadow-md w-full h-full"
              style={{
                backgroundColor: color,
                border: "1px solid rgba(0, 0, 0, 0.1)",
                borderRadius: "2px",
              }}
              onMouseEnter={(e) => handleMouseEnter(element, e)}
              onMouseMove={handleMouseMove}
              onMouseLeave={handleMouseLeave}
            />
          );
        })}
      </div>

      {/* Tooltip */}
      {hoveredElement && tooltipFormatter && (
        <div
          className="fixed z-50 bg-gray-800 text-white text-sm px-2 py-1 rounded shadow-lg pointer-events-none"
          style={{
            left: mousePosition.x + 10,
            top: mousePosition.y - 10,
            transform: "translate(-100%, -100%)",
          }}
        >
          {tooltipFormatter(hoveredElement)}
        </div>
      )}
    </div>
  );
};

export default GridChart;
