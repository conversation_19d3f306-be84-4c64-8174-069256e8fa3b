import { ResponsiveContainer } from "recharts";
import { CHART_DIMENSIONS, CHART_MESSAGES, RESPONSIVE_CONTAINER_PROPS } from "./constants";

import type { ReactElement } from "react";

interface ChartContainerProps {
  children: ReactElement;
  height?: number | string;
  width?: string;
  className?: string;
  isLoading?: boolean;
  hasData?: boolean;
  loadingMessage?: string;
  noDataMessage?: string;
  disabled?: boolean;
}

/**
 * Standardized container for all charts with consistent loading and empty states
 */
export const ChartContainer: React.FC<ChartContainerProps> = ({
  children,
  height = CHART_DIMENSIONS.medium,
  width = CHART_DIMENSIONS.fullWidth,
  className = "",
  isLoading = false,
  hasData = true,
  loadingMessage = CHART_MESSAGES.loading,
  noDataMessage = CHART_MESSAGES.noData,
  disabled = false,
}) => {
  // Loading state
  if (isLoading) {
    return (
      <div
        className={`flex items-center justify-center ${className} ${disabled ? "opacity-50" : ""}`}
        style={{ height, width }}
      >
        <p className="text-space70 text-caption">{loadingMessage}</p>
      </div>
    );
  }

  // No data state
  if (!hasData) {
    return (
      <div
        className={`flex items-center justify-center ${className} ${disabled ? "opacity-50" : ""}`}
        style={{ height, width }}
      >
        <p className="text-space70 text-caption">{noDataMessage}</p>
      </div>
    );
  }

  // Chart with data
  return (
    <div className={`${className} ${disabled ? "opacity-50" : ""}`}>
      <ResponsiveContainer
        width={width}
        height={height}
        {...RESPONSIVE_CONTAINER_PROPS}
      >
        {children}
      </ResponsiveContainer>
    </div>
  );
};

/**
 * Micro chart container for small inline charts
 */
export const MicroChartContainer: React.FC<Omit<ChartContainerProps, 'height'>> = (props) => {
  return (
    <ChartContainer
      {...props}
      height={CHART_DIMENSIONS.micro}
      noDataMessage="No data for the past 7 days"
    />
  );
};

/**
 * Standard chart container for most use cases
 */
export const StandardChartContainer: React.FC<ChartContainerProps> = (props) => {
  return <ChartContainer {...props} height={CHART_DIMENSIONS.medium} />;
};

/**
 * Large chart container for detailed views
 */
export const LargeChartContainer: React.FC<ChartContainerProps> = (props) => {
  return <ChartContainer {...props} height={CHART_DIMENSIONS.large} />;
};
