import { XAxis, YAxis } from "recharts";
import { formatTimeAxis, formatDateAxis, formatSmartDateTime, formatNumericValue } from "./formatters";
import { AXIS_CONFIG } from "./constants";

interface StandardXAxisProps {
  /**
   * Data key for the X axis
   */
  dataKey?: string;

  /**
   * Custom tick formatter
   */
  tickFormatter?: (value: any) => string;

  /**
   * Whether this is a time-based axis
   */
  isTimeAxis?: boolean;

  /**
   * Time range in milliseconds (for smart time formatting)
   */
  timeRangeMs?: number;

  /**
   * Additional props to pass to XAxis
   */
  axisProps?: any;
}

interface StandardYAxisProps {
  /**
   * Custom tick formatter
   */
  tickFormatter?: (value: any) => string;

  /**
   * Whether to format as numeric values
   */
  isNumeric?: boolean;

  /**
   * Domain for the Y axis
   */
  domain?: [string | number, string | number];

  /**
   * Additional props to pass to YAxis
   */
  axisProps?: any;
}

/**
 * Standardized X-axis component
 */
export const StandardXAxis: React.FC<StandardXAxisProps> = ({
  dataKey = "time",
  tickFormatter,
  isTimeAxis = true,
  timeRangeMs,
  axisProps = {},
}) => {
  const getTickFormatter = () => {
    if (tickFormatter) return tickFormatter;

    if (isTimeAxis) {
      if (timeRangeMs) {
        return (value: any) => formatSmartDateTime(value, timeRangeMs);
      }
      return formatTimeAxis;
    }

    return undefined;
  };

  return (
    <XAxis
      dataKey={dataKey}
      stroke={AXIS_CONFIG.x.stroke}
      fontSize={AXIS_CONFIG.x.fontSize}
      interval={AXIS_CONFIG.x.interval}
      axisLine={AXIS_CONFIG.x.axisLine}
      tickFormatter={getTickFormatter()}
      {...axisProps}
    />
  );
};

/**
 * Standardized Y-axis component
 */
export const StandardYAxis: React.FC<StandardYAxisProps> = ({
  tickFormatter,
  isNumeric = true,
  domain = ["auto", "auto"],
  axisProps = {},
}) => {
  const getTickFormatter = () => {
    if (tickFormatter) return tickFormatter;
    if (isNumeric) return formatNumericValue;
    return undefined;
  };

  return (
    <YAxis
      stroke={AXIS_CONFIG.y.stroke}
      fontSize={AXIS_CONFIG.y.fontSize}
      axisLine={AXIS_CONFIG.y.axisLine}
      domain={domain}
      tickFormatter={getTickFormatter()}
      {...axisProps}
    />
  );
};

/**
 * X-axis specifically for time-based data
 */
export const TimeXAxis: React.FC<Omit<StandardXAxisProps, 'isTimeAxis'>> = (props) => {
  return <StandardXAxis {...props} isTimeAxis={true} />;
};

/**
 * Y-axis specifically for numeric data
 */
export const NumericYAxis: React.FC<StandardYAxisProps> = (props) => {
  return <StandardYAxis {...props} isNumeric={true} />;
};

/**
 * Y-axis for boolean data (0/1 with True/False labels)
 */
export const BooleanYAxis: React.FC<Omit<StandardYAxisProps, 'domain' | 'tickFormatter'>> = (props) => {
  return (
    <StandardYAxis
      {...props}
      domain={[0, 1]}
      tickFormatter={(value) => value === 1 ? "True" : "False"}
      axisProps={{ tickCount: 2, ...props.axisProps }}
    />
  );
};
