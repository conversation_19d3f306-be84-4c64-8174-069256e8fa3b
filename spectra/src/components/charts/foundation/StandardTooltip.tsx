import { Tooltip } from "recharts";
import { formatTooltipValue, formatTooltipTime } from "./formatters";
import { TOOLTIP_CONFIG } from "./constants";

interface StandardTooltipProps {
  /**
   * Custom formatter for values
   */
  valueFormatter?: (value: any, name?: string, props?: any) => string;

  /**
   * Custom formatter for labels (usually timestamps)
   */
  labelFormatter?: (label: any) => string;

  /**
   * Whether to show the cursor
   */
  cursor?: boolean;

  /**
   * Additional props to pass to the Tooltip component
   */
  tooltipProps?: any;
}

/**
 * Standardized tooltip component with consistent formatting
 */
export const StandardTooltip: React.FC<StandardTooltipProps> = ({
  valueFormatter = formatTooltipValue,
  labelFormatter = formatTooltipTime,
  cursor = TOOLTIP_CONFIG.cursor,
  tooltipProps = {},
}) => {
  return (
    <Tooltip
      cursor={cursor}
      formatter={valueFormatter}
      labelFormatter={labelFormatter}
      contentStyle={TOOLTIP_CONFIG.contentStyle}
      {...tooltipProps}
    />
  );
};

/**
 * Tooltip specifically for numeric values (ensures 2 decimal places)
 */
export const NumericTooltip: React.FC<Omit<StandardTooltipProps, 'valueFormatter'>> = (props) => {
  return (
    <StandardTooltip
      {...props}
      valueFormatter={(value) => formatTooltipValue(value)}
    />
  );
};

/**
 * Tooltip for time-based charts with smart time formatting
 */
export const TimeTooltip: React.FC<StandardTooltipProps> = (props) => {
  return (
    <StandardTooltip
      {...props}
      labelFormatter={(timestamp) => formatTooltipTime(timestamp)}
    />
  );
};

/**
 * Simple tooltip without custom formatting (uses recharts defaults)
 */
export const SimpleTooltip: React.FC<{ cursor?: boolean }> = ({ cursor = false }) => {
  return <Tooltip cursor={cursor} />;
};
