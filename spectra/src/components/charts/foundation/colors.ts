/**
 * Standardized color palette for charts
 * Uses theme colors where possible for consistency
 */

// Base chart colors (using theme colors)
export const CHART_COLORS = {
  // Primary data colors
  primary: "#4A90E2",
  secondary: "#6db5d1",
  tertiary: "#5ea15f",

  // Energy-specific colors
  stored: "#89A7BF",
  forward: "#4E6D86",
  net: "#6B89A2",

  // Status colors
  online: "#1E3A8A",
  offline: "#DDDDDD",
  warning: "#E87D48",

  // Neutral colors
  axis: "#b4b4b4",
  grid: "#e5e5e5",
  text: "#7F8284",
  stroke:"#d1d5db",

  // Background colors
  background: "#ffffff",
  backgroundAlt: "#f8f9fa",
} as const;

// Device type colors (from existing NetworkMap)
export const DEVICE_COLORS = {
  Charger: "#5ea15f",
  Battery: "#6db5d1",
  Meter: "#6db5d1",
  SwapStation: "#f2aa3c",
} as const;

// Monitor state colors (from existing monitor charts)
export const MONITOR_STATE_COLORS = {
  HEALTHY: "#6E9F66", // green50
  WARNING: "#E87D48", // orange50
  UNHEALTHY: "#C55B57", // red50
} as const;

// Alert state colors
export const ALERT_STATE_COLORS = {
  HEALTHY: "#6E9F66", // green50
  WARNING: "#E87D48", // orange50
  UNHEALTHY: "#C55B57", // red50
} as const;

// Chart color palettes for multi-series charts
export const CHART_PALETTES = {
  // Default palette for general use
  default: [
    "#4A90E2", // blue
    "#6E9F66", // green
    "#E87D48", // orange
    "#C55B57", // red
    "#6db5d1", // light blue
    "#f2aa3c", // yellow
    "#9B59B6", // purple
    "#34495E", // dark blue-gray
  ],

  // Energy palette for power/energy charts
  energy: [
    "#4E6D86", // forward
    "#6B89A2", // net
    "#89A7BF", // stored
    "#5ea15f", // generation
  ],

  // Status palette for health/status indicators
  status: [
    "#6E9F66", // healthy/good
    "#E87D48", // warning/caution
    "#C55B57", // unhealthy/error
    "#b4b4b4", // neutral/unknown
  ],

  // Gradient colors for special effects
  gradients: {
    blinking: "url(#blinkingGradient)",
    highlight: "#6B89A2",
  },
} as const;

/**
 * Gets a color from the default palette by index
 */
export const getChartColor = (index: number): string => {
  return CHART_PALETTES.default[index % CHART_PALETTES.default.length];
};

/**
 * Gets colors for energy-related charts
 */
export const getEnergyColors = (): string[] => {
  return CHART_PALETTES.energy.slice();
};

/**
 * Gets colors for status-related charts
 */
export const getStatusColors = (): string[] => {
  return CHART_PALETTES.status.slice();
};

/**
 * Gets a specific number of colors from the default palette
 */
export const getChartColors = (count: number): string[] => {
  return Array.from({ length: count }, (_, i) => getChartColor(i));
};
