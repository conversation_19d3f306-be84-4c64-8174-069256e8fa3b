/**
 * Chart configuration constants for consistent styling across all charts
 */

import { CHART_COLORS } from "./colors";

// Standard chart dimensions
export const CHART_DIMENSIONS = {
  // Heights for different chart sizes
  micro: 52,
  small: 100,
  medium: 200,
  large: 250,
  xlarge: 400,

  // Standard widths (usually 100% with ResponsiveContainer)
  fullWidth: "100%",
} as const;

// Standard margins for different chart types
export const CHART_MARGINS = {
  // Default margin for most charts
  default: { top: 5, right: 10, bottom: 5, left: -20 },

  // Margin for charts with legends
  withLegend: { top: 5, right: 10, bottom: 30, left: -20 },

  // Margin for micro charts
  micro: { top: 0, right: 5, bottom: 0, left: -10 },

  // Margin for charts with Y-axis labels
  withYAxisLabels: { top: 5, right: 10, bottom: 5, left: 20 },

  // Margin for composed charts
  composed: { top: 10, right: 20, bottom: 5, left: -20 },

  chargerPower: { top: 0, right: 0, left: -25, bottom: 0 },

  monitorVariable: { top: 5, right: 5, left: -10, bottom: -10 },
} as const;

// Font sizes for chart elements
export const CHART_FONTS = {
  tick: 10,
  label: 12,
  title: 14,
  legend: 11,
} as const;

// Standard axis configurations
export const AXIS_CONFIG = {
  x: {
    stroke: CHART_COLORS.axis,
    fontSize: CHART_FONTS.tick,
    interval: "preserveStartEnd" as const,
    axisLine: false,
  },
  y: {
    stroke: CHART_COLORS.axis,
    fontSize: CHART_FONTS.tick,
    axisLine: false,
  },
} as const;

// Standard tooltip configuration
export const TOOLTIP_CONFIG = {
  cursor: false,
  contentStyle: {
    backgroundColor: CHART_COLORS.background,
    border: `1px solid ${CHART_COLORS.axis}`,
    borderRadius: "4px",
    fontSize: CHART_FONTS.label,
  },
} as const;

// Standard legend configuration
export const LEGEND_CONFIG = {
  verticalAlign: "bottom" as const,
  height: 36,
  fontSize: CHART_FONTS.legend,
} as const;

// Loading and empty state messages
export const CHART_MESSAGES = {
  loading: "Loading...",
  noData: "No data for the selected timeframe",
  noDataAvailable: "No data available",
  error: "Error loading chart data",
} as const;

// Animation configurations
export const ANIMATION_CONFIG = {
  // Standard animation duration
  duration: 300,

  // Disable animations for performance on large datasets
  disabled: false,
} as const;

// Grid configuration
export const GRID_CONFIG = {
  stroke: CHART_COLORS.grid,
  strokeDasharray: "3 3",
} as const;

// Standard responsive container props
export const RESPONSIVE_CONTAINER_PROPS = {
  width: CHART_DIMENSIONS.fullWidth,
  debounceMs: 50,
} as const;

// Chart type specific configurations
export const CHART_TYPE_CONFIG = {
  line: {
    strokeWidth: 2,
    dot: false,
    connectNulls: false,
  },

  bar: {
    barGap: 0,
    barCategoryGap: 0,
  },

  pie: {
    outerRadius: 80,
    innerRadius: 60, // for donut charts
    paddingAngle: 2,
  },

  scatter: {
    strokeWidth: 0,
  },
} as const;

// Time range constants for smart formatting
export const TIME_RANGES = {
  ONE_HOUR: 60 * 60 * 1000,
  ONE_DAY: 24 * 60 * 60 * 1000,
  ONE_WEEK: 7 * 24 * 60 * 60 * 1000,
  ONE_MONTH: 30 * 24 * 60 * 60 * 1000,
} as const;
