/**
 * Chart formatting utilities for consistent data presentation across all charts
 */

/**
 * Formats numeric values for chart tooltips and labels
 * - Integers: no decimal places
 * - Decimals: 2 decimal places maximum
 */
export const formatNumericValue = (value: number | string): string => {
  if (typeof value === "string") return value;
  if (typeof value !== "number" || Number.isNaN(value)) return "N/A";

  // If the value is an integer, return it without decimal places
  if (value % 1 === 0) {
    return value.toFixed(0);
  }

  // Otherwise, return it with 2 decimal places
  return value.toFixed(2);
};

/**
 * Formats percentage values consistently
 */
export const formatPercentage = (value: number, decimalPlaces = 1): string => {
  if (typeof value !== "number" || Number.isNaN(value)) return "N/A";
  return `${value.toFixed(decimalPlaces)}%`;
};

/**
 * Formats time values for chart axes with timezone support
 * For same-day data: shows time + timezone
 */
export const formatTimeAxis = (
  timestamp: number | string,
  hour: "2-digit" | "numeric" = "2-digit",
  minute: "2-digit" | "numeric" = "2-digit",
): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], {
    hour: hour,
    minute: minute,
  });
};

/**
 * Formats date values for chart axes
 * For multi-day data: shows full date using toLocaleDateString
 */
export const formatDateAxis = (timestamp: number | string): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString();
};

/**
 * Smart date/time formatter that chooses format based on time range
 * Same day => only show time + timezone
 * Multiple days => show full timestamp using toLocaleDateString
 */
export const formatSmartDateTime = (
  timestamp: number | string,
  timeRangeMs: number,
): string => {
  const ONE_DAY = 24 * 60 * 60 * 1000;

  if (timeRangeMs > ONE_DAY) {
    return formatDateAxis(timestamp);
  }
  return formatTimeAxis(timestamp);
};

/**
 * Formats tooltip labels for time-based charts
 * Always shows full timestamp with date and time
 */
export const formatTooltipTime = (timestamp: number | string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};

/**
 * Formats boolean values for charts
 */
export const formatBoolean = (value: boolean): string => {
  return value ? "True" : "False";
};

/**
 * Generic tooltip formatter that handles different data types
 */
export const formatTooltipValue = (
  value: boolean | number | string | null | undefined,
): string => {
  if (typeof value === "boolean") {
    return formatBoolean(value);
  }
  if (typeof value === "number") {
    return formatNumericValue(value);
  }
  if (typeof value === "string") {
    return value;
  }
  return "";
};

/**
 * Formats units for display (e.g., "kWh", "MW", etc.)
 */
export const formatUnit = (unit: string): string => {
  return unit ? ` (${unit})` : "";
};

/**
 * Creates a formatted label with unit
 */
export const formatLabelWithUnit = (label: string, unit?: string): string => {
  return `${label}${unit ? formatUnit(unit) : ""}`;
};

/**
 * Determines if a time range spans multiple days
 */
export const isMultiDayRange = (
  startTimestamp: number | string,
  endTimestamp: number | string,
): boolean => {
  const start = new Date(startTimestamp);
  const end = new Date(endTimestamp);

  // Check if dates are on different days
  return start.toDateString() !== end.toDateString();
};

/**
 * Smart axis formatter that automatically chooses between time and date based on data range
 */
export const formatSmartAxis = (
  timestamp: number | string,
  dataRange: { start: number | string; end: number | string },
): string => {
  if (isMultiDayRange(dataRange.start, dataRange.end)) {
    return formatDateAxis(timestamp);
  }
  return formatTimeAxis(timestamp);
};

/**
 * Enhanced tooltip formatter that shows appropriate detail based on context
 */
export const formatEnhancedTooltipTime = (
  timestamp: number | string,
  showFullTimestamp = true,
): string => {
  const date = new Date(timestamp);

  if (showFullTimestamp) {
    return date.toLocaleString();
  }

  // For same-day contexts, show time with timezone
  return date.toLocaleTimeString([], {
    hour: "numeric",
    minute: "2-digit",
    timeZoneName: "short",
  });
};
