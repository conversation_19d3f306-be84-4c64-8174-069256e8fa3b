import {
  CHART_DIMENSIONS,
  CHART_MARGINS,
  CHART_FONTS,
  AXIS_CONFIG,
  LEGEND_CONFIG,
  TOOLTIP_CONFIG,
  CHART_MESSAGES,
  ANIMATION_CONFIG,
  G<PERSON>D_CONFIG,
  RESPONSIVE_CONTAINER_PROPS,
  CHART_TYPE_CONFIG,
  TIME_RANGES
} from '../constants';

describe('Chart Constants', () => {
  describe('CHART_DIMENSIONS', () => {
    it('should have all required dimension constants', () => {
      expect(CHART_DIMENSIONS.micro).toBeDefined();
      expect(CHART_DIMENSIONS.small).toBeDefined();
      expect(CHART_DIMENSIONS.medium).toBeDefined();
      expect(CHART_DIMENSIONS.large).toBeDefined();
      expect(CHART_DIMENSIONS.xlarge).toBeDefined();
      expect(CHART_DIMENSIONS.fullWidth).toBeDefined();
    });

    it('should have numeric values for heights', () => {
      expect(typeof CHART_DIMENSIONS.micro).toBe('number');
      expect(typeof CHART_DIMENSIONS.small).toBe('number');
      expect(typeof CHART_DIMENSIONS.medium).toBe('number');
      expect(typeof CHART_DIMENSIONS.large).toBe('number');
      expect(typeof CHART_DIMENSIONS.xlarge).toBe('number');
    });

    it('should have logical height progression', () => {
      expect(CHART_DIMENSIONS.micro).toBeLessThan(CHART_DIMENSIONS.small);
      expect(CHART_DIMENSIONS.small).toBeLessThan(CHART_DIMENSIONS.medium);
      expect(CHART_DIMENSIONS.medium).toBeLessThan(CHART_DIMENSIONS.large);
      expect(CHART_DIMENSIONS.large).toBeLessThan(CHART_DIMENSIONS.xlarge);
    });

    it('should have reasonable height values', () => {
      expect(CHART_DIMENSIONS.micro).toBeGreaterThan(0);
      expect(CHART_DIMENSIONS.micro).toBeLessThan(100);
      expect(CHART_DIMENSIONS.xlarge).toBeLessThan(1000);
    });

    it('should have fullWidth as string', () => {
      expect(typeof CHART_DIMENSIONS.fullWidth).toBe('string');
      expect(CHART_DIMENSIONS.fullWidth).toBe('100%');
    });
  });

  describe('CHART_MARGINS', () => {
    it('should have all required margin configurations', () => {
      expect(CHART_MARGINS.default).toBeDefined();
      expect(CHART_MARGINS.withLegend).toBeDefined();
      expect(CHART_MARGINS.micro).toBeDefined();
      expect(CHART_MARGINS.withYAxisLabels).toBeDefined();
      expect(CHART_MARGINS.composed).toBeDefined();
    });

    it('should have proper margin object structure', () => {
      Object.values(CHART_MARGINS).forEach(margin => {
        expect(margin).toHaveProperty('top');
        expect(margin).toHaveProperty('right');
        expect(margin).toHaveProperty('bottom');
        expect(margin).toHaveProperty('left');

        expect(typeof margin.top).toBe('number');
        expect(typeof margin.right).toBe('number');
        expect(typeof margin.bottom).toBe('number');
        expect(typeof margin.left).toBe('number');
      });
    });

    it('should have reasonable margin values', () => {
      Object.values(CHART_MARGINS).forEach(margin => {
        expect(margin.top).toBeGreaterThanOrEqual(-50);
        expect(margin.right).toBeGreaterThanOrEqual(-50);
        expect(margin.bottom).toBeGreaterThanOrEqual(-50);
        expect(margin.left).toBeGreaterThanOrEqual(-50);

        expect(margin.top).toBeLessThan(100);
        expect(margin.right).toBeLessThan(100);
        expect(margin.bottom).toBeLessThan(100);
        expect(margin.left).toBeLessThan(100);
      });
    });
  });

  describe('CHART_FONTS', () => {
    it('should have all required font size constants', () => {
      expect(CHART_FONTS.tick).toBeDefined();
      expect(CHART_FONTS.label).toBeDefined();
      expect(CHART_FONTS.title).toBeDefined();
      expect(CHART_FONTS.legend).toBeDefined();
    });

    it('should have numeric font sizes', () => {
      expect(typeof CHART_FONTS.tick).toBe('number');
      expect(typeof CHART_FONTS.label).toBe('number');
      expect(typeof CHART_FONTS.title).toBe('number');
      expect(typeof CHART_FONTS.legend).toBe('number');
    });

    it('should have logical font size progression', () => {
      expect(CHART_FONTS.tick).toBeLessThanOrEqual(CHART_FONTS.label);
      expect(CHART_FONTS.label).toBeLessThanOrEqual(CHART_FONTS.title);
    });

    it('should have reasonable font sizes', () => {
      Object.values(CHART_FONTS).forEach(fontSize => {
        expect(fontSize).toBeGreaterThan(6);
        expect(fontSize).toBeLessThan(24);
      });
    });
  });

  describe('AXIS_CONFIG', () => {
    it('should have x and y axis configurations', () => {
      expect(AXIS_CONFIG.x).toBeDefined();
      expect(AXIS_CONFIG.y).toBeDefined();
    });

    it('should have proper axis configuration structure', () => {
      [AXIS_CONFIG.x, AXIS_CONFIG.y].forEach(axisConfig => {
        expect(axisConfig).toHaveProperty('stroke');
        expect(axisConfig).toHaveProperty('fontSize');
        expect(axisConfig).toHaveProperty('axisLine');

        expect(typeof axisConfig.stroke).toBe('string');
        expect(typeof axisConfig.fontSize).toBe('number');
        expect(typeof axisConfig.axisLine).toBe('boolean');
      });
    });

    it('should have valid color values for stroke', () => {
      expect(AXIS_CONFIG.x.stroke).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(AXIS_CONFIG.y.stroke).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });
  });

  describe('LEGEND_CONFIG', () => {
    it('should have required legend configuration properties', () => {
      expect(LEGEND_CONFIG.verticalAlign).toBeDefined();
      expect(LEGEND_CONFIG.height).toBeDefined();
    });

    it('should have proper types', () => {
      expect(typeof LEGEND_CONFIG.verticalAlign).toBe('string');
      expect(typeof LEGEND_CONFIG.height).toBe('number');
    });

    it('should have reasonable values', () => {
      expect(['top', 'middle', 'bottom']).toContain(LEGEND_CONFIG.verticalAlign);
      expect(LEGEND_CONFIG.height).toBeGreaterThan(0);
      expect(LEGEND_CONFIG.height).toBeLessThan(100);
    });
  });

  describe('TOOLTIP_CONFIG', () => {
    it('should have required tooltip configuration properties', () => {
      expect(TOOLTIP_CONFIG.cursor).toBeDefined();
      expect(TOOLTIP_CONFIG.contentStyle).toBeDefined();
    });

    it('should have proper types', () => {
      expect(typeof TOOLTIP_CONFIG.cursor).toBe('boolean');
      expect(typeof TOOLTIP_CONFIG.contentStyle).toBe('object');
    });

    it('should have valid contentStyle object', () => {
      expect(TOOLTIP_CONFIG.contentStyle).toHaveProperty('backgroundColor');
      expect(TOOLTIP_CONFIG.contentStyle).toHaveProperty('border');
      expect(TOOLTIP_CONFIG.contentStyle).toHaveProperty('borderRadius');
    });
  });

  describe('CHART_MESSAGES', () => {
    it('should have all required message constants', () => {
      expect(CHART_MESSAGES.loading).toBeDefined();
      expect(CHART_MESSAGES.noData).toBeDefined();
      expect(CHART_MESSAGES.noDataAvailable).toBeDefined();
      expect(CHART_MESSAGES.error).toBeDefined();
    });

    it('should have string values', () => {
      expect(typeof CHART_MESSAGES.loading).toBe('string');
      expect(typeof CHART_MESSAGES.noData).toBe('string');
      expect(typeof CHART_MESSAGES.noDataAvailable).toBe('string');
      expect(typeof CHART_MESSAGES.error).toBe('string');
    });

    it('should have non-empty messages', () => {
      Object.values(CHART_MESSAGES).forEach(message => {
        expect(message.length).toBeGreaterThan(0);
      });
    });
  });

  describe('ANIMATION_CONFIG', () => {
    it('should have required animation properties', () => {
      expect(ANIMATION_CONFIG.duration).toBeDefined();
      expect(ANIMATION_CONFIG.disabled).toBeDefined();
    });

    it('should have proper types', () => {
      expect(typeof ANIMATION_CONFIG.duration).toBe('number');
      expect(typeof ANIMATION_CONFIG.disabled).toBe('boolean');
    });

    it('should have reasonable duration', () => {
      expect(ANIMATION_CONFIG.duration).toBeGreaterThan(0);
      expect(ANIMATION_CONFIG.duration).toBeLessThan(5000);
    });
  });

  describe('GRID_CONFIG', () => {
    it('should have required grid properties', () => {
      expect(GRID_CONFIG.stroke).toBeDefined();
      expect(GRID_CONFIG.strokeDasharray).toBeDefined();
    });

    it('should have proper types', () => {
      expect(typeof GRID_CONFIG.stroke).toBe('string');
      expect(typeof GRID_CONFIG.strokeDasharray).toBe('string');
    });

    it('should have valid color for stroke', () => {
      expect(GRID_CONFIG.stroke).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });
  });

  describe('RESPONSIVE_CONTAINER_PROPS', () => {
    it('should have required responsive container properties', () => {
      expect(RESPONSIVE_CONTAINER_PROPS.width).toBeDefined();
      expect(RESPONSIVE_CONTAINER_PROPS.debounceMs).toBeDefined();
    });

    it('should have proper types', () => {
      expect(typeof RESPONSIVE_CONTAINER_PROPS.width).toBe('string');
      expect(typeof RESPONSIVE_CONTAINER_PROPS.debounceMs).toBe('number');
    });

    it('should have reasonable values', () => {
      expect(RESPONSIVE_CONTAINER_PROPS.width).toBe('100%');
      expect(RESPONSIVE_CONTAINER_PROPS.debounceMs).toBeGreaterThan(0);
      expect(RESPONSIVE_CONTAINER_PROPS.debounceMs).toBeLessThan(1000);
    });
  });

  describe('CHART_TYPE_CONFIG', () => {
    it('should have configurations for all chart types', () => {
      expect(CHART_TYPE_CONFIG.line).toBeDefined();
      expect(CHART_TYPE_CONFIG.bar).toBeDefined();
      expect(CHART_TYPE_CONFIG.pie).toBeDefined();
      expect(CHART_TYPE_CONFIG.scatter).toBeDefined();
    });

    it('should have proper line chart configuration', () => {
      expect(CHART_TYPE_CONFIG.line).toHaveProperty('strokeWidth');
      expect(CHART_TYPE_CONFIG.line).toHaveProperty('dot');
      expect(CHART_TYPE_CONFIG.line).toHaveProperty('connectNulls');

      expect(typeof CHART_TYPE_CONFIG.line.strokeWidth).toBe('number');
      expect(typeof CHART_TYPE_CONFIG.line.dot).toBe('boolean');
      expect(typeof CHART_TYPE_CONFIG.line.connectNulls).toBe('boolean');
    });

    it('should have proper bar chart configuration', () => {
      expect(CHART_TYPE_CONFIG.bar).toHaveProperty('barGap');
      expect(CHART_TYPE_CONFIG.bar).toHaveProperty('barCategoryGap');

      expect(typeof CHART_TYPE_CONFIG.bar.barGap).toBe('number');
      expect(typeof CHART_TYPE_CONFIG.bar.barCategoryGap).toBe('number');
    });

    it('should have proper pie chart configuration', () => {
      expect(CHART_TYPE_CONFIG.pie).toHaveProperty('outerRadius');
      expect(CHART_TYPE_CONFIG.pie).toHaveProperty('innerRadius');
      expect(CHART_TYPE_CONFIG.pie).toHaveProperty('paddingAngle');

      expect(typeof CHART_TYPE_CONFIG.pie.outerRadius).toBe('number');
      expect(typeof CHART_TYPE_CONFIG.pie.innerRadius).toBe('number');
      expect(typeof CHART_TYPE_CONFIG.pie.paddingAngle).toBe('number');
    });

    it('should have reasonable values', () => {
      expect(CHART_TYPE_CONFIG.line.strokeWidth).toBeGreaterThan(0);
      expect(CHART_TYPE_CONFIG.pie.outerRadius).toBeGreaterThan(CHART_TYPE_CONFIG.pie.innerRadius);
      expect(CHART_TYPE_CONFIG.pie.paddingAngle).toBeGreaterThanOrEqual(0);
    });
  });

  describe('TIME_RANGES', () => {
    it('should have all required time range constants', () => {
      expect(TIME_RANGES.ONE_HOUR).toBeDefined();
      expect(TIME_RANGES.ONE_DAY).toBeDefined();
      expect(TIME_RANGES.ONE_WEEK).toBeDefined();
      expect(TIME_RANGES.ONE_MONTH).toBeDefined();
    });

    it('should have numeric values in milliseconds', () => {
      expect(typeof TIME_RANGES.ONE_HOUR).toBe('number');
      expect(typeof TIME_RANGES.ONE_DAY).toBe('number');
      expect(typeof TIME_RANGES.ONE_WEEK).toBe('number');
      expect(typeof TIME_RANGES.ONE_MONTH).toBe('number');
    });

    it('should have logical time progression', () => {
      expect(TIME_RANGES.ONE_HOUR).toBeLessThan(TIME_RANGES.ONE_DAY);
      expect(TIME_RANGES.ONE_DAY).toBeLessThan(TIME_RANGES.ONE_WEEK);
      expect(TIME_RANGES.ONE_WEEK).toBeLessThan(TIME_RANGES.ONE_MONTH);
    });

    it('should have correct millisecond calculations', () => {
      expect(TIME_RANGES.ONE_HOUR).toBe(60 * 60 * 1000);
      expect(TIME_RANGES.ONE_DAY).toBe(24 * 60 * 60 * 1000);
      expect(TIME_RANGES.ONE_WEEK).toBe(7 * 24 * 60 * 60 * 1000);
      expect(TIME_RANGES.ONE_MONTH).toBe(30 * 24 * 60 * 60 * 1000);
    });
  });

  describe('Constants immutability', () => {
    it('should be readonly objects to prevent modification', () => {
      // TypeScript const assertions make objects readonly at compile time
      // Runtime freezing is not required for const assertions
      expect(typeof CHART_DIMENSIONS).toBe('object');
      expect(typeof CHART_MARGINS).toBe('object');
      expect(typeof CHART_FONTS).toBe('object');
      expect(typeof CHART_MESSAGES).toBe('object');
      expect(typeof TIME_RANGES).toBe('object');
    });
  });
});
