import {
  CHART_COLORS,
  CHART_PALETTES,
  getChartColors,
  getEnergyColors,
  getStatusColors
} from '../colors';

describe('Chart Colors', () => {
  describe('CHART_COLORS constants', () => {
    it('should have all required base colors defined', () => {
      expect(CHART_COLORS.primary).toBeDefined();
      expect(CHART_COLORS.secondary).toBeDefined();
      expect(CHART_COLORS.tertiary).toBeDefined();

      // Should be valid hex colors
      expect(CHART_COLORS.primary).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.secondary).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.tertiary).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });

    it('should have all required energy colors defined', () => {
      expect(CHART_COLORS.stored).toBeDefined();
      expect(CHART_COLORS.forward).toBeDefined();
      expect(CHART_COLORS.net).toBeDefined();

      // Should be valid hex colors
      expect(CHART_COLORS.stored).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.forward).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.net).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });

    it('should have all required status colors defined', () => {
      expect(CHART_COLORS.online).toBeDefined();
      expect(CHART_COLORS.offline).toBeDefined();
      expect(CHART_COLORS.warning).toBeDefined();

      // Should be valid hex colors
      expect(CHART_COLORS.online).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.offline).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.warning).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });

    it('should have all required neutral colors defined', () => {
      expect(CHART_COLORS.axis).toBeDefined();
      expect(CHART_COLORS.grid).toBeDefined();
      expect(CHART_COLORS.text).toBeDefined();
      expect(CHART_COLORS.background).toBeDefined();
      expect(CHART_COLORS.backgroundAlt).toBeDefined();

      // Should be valid hex colors
      expect(CHART_COLORS.axis).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.grid).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.text).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.background).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(CHART_COLORS.backgroundAlt).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });

    it('should have distinct colors for different purposes', () => {
      // Primary colors should be different
      expect(CHART_COLORS.primary).not.toBe(CHART_COLORS.secondary);
      expect(CHART_COLORS.secondary).not.toBe(CHART_COLORS.tertiary);

      // Status colors should be different
      expect(CHART_COLORS.online).not.toBe(CHART_COLORS.offline);
      expect(CHART_COLORS.offline).not.toBe(CHART_COLORS.warning);

      // Energy colors should be different
      expect(CHART_COLORS.stored).not.toBe(CHART_COLORS.forward);
      expect(CHART_COLORS.forward).not.toBe(CHART_COLORS.net);
    });
  });

  describe('CHART_PALETTES constants', () => {
    it('should have default palette with multiple colors', () => {
      expect(CHART_PALETTES.default).toBeDefined();
      expect(Array.isArray(CHART_PALETTES.default)).toBe(true);
      expect(CHART_PALETTES.default.length).toBeGreaterThan(3);

      // All colors should be valid hex colors
      CHART_PALETTES.default.forEach(color => {
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should have energy palette with appropriate colors', () => {
      expect(CHART_PALETTES.energy).toBeDefined();
      expect(Array.isArray(CHART_PALETTES.energy)).toBe(true);
      expect(CHART_PALETTES.energy.length).toBeGreaterThan(2);

      // All colors should be valid hex colors
      CHART_PALETTES.energy.forEach(color => {
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should have status palette with appropriate colors', () => {
      expect(CHART_PALETTES.status).toBeDefined();
      expect(Array.isArray(CHART_PALETTES.status)).toBe(true);
      expect(CHART_PALETTES.status.length).toBeGreaterThan(2);

      // All colors should be valid hex colors
      CHART_PALETTES.status.forEach(color => {
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should have gradients object defined', () => {
      expect(CHART_PALETTES.gradients).toBeDefined();
      expect(typeof CHART_PALETTES.gradients).toBe('object');
      expect(CHART_PALETTES.gradients.blinking).toBeDefined();
      expect(CHART_PALETTES.gradients.highlight).toBeDefined();
    });

    it('should have all colors in palettes be unique within each palette', () => {
      // Check default palette for uniqueness
      const defaultSet = new Set(CHART_PALETTES.default);
      expect(defaultSet.size).toBe(CHART_PALETTES.default.length);

      // Check energy palette for uniqueness
      const energySet = new Set(CHART_PALETTES.energy);
      expect(energySet.size).toBe(CHART_PALETTES.energy.length);

      // Check status palette for uniqueness
      const statusSet = new Set(CHART_PALETTES.status);
      expect(statusSet.size).toBe(CHART_PALETTES.status.length);
    });
  });

  describe('getChartColors function', () => {
    it('should return correct number of colors', () => {
      expect(getChartColors(1)).toHaveLength(1);
      expect(getChartColors(3)).toHaveLength(3);
      expect(getChartColors(5)).toHaveLength(5);
      expect(getChartColors(10)).toHaveLength(10);
    });

    it('should return valid hex colors', () => {
      const colors = getChartColors(5);
      colors.forEach(color => {
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should cycle through palette when requesting more colors than available', () => {
      const paletteLength = CHART_PALETTES.default.length;
      const colors = getChartColors(paletteLength + 2);

      expect(colors).toHaveLength(paletteLength + 2);
      // First color should match the first palette color
      expect(colors[0]).toBe(CHART_PALETTES.default[0]);
      // Color at palette length should cycle back to first color
      expect(colors[paletteLength]).toBe(CHART_PALETTES.default[0]);
    });

    it('should handle edge cases', () => {
      expect(getChartColors(0)).toHaveLength(0);
      expect(getChartColors(-1)).toHaveLength(0);
    });

    it('should return consistent colors for same count', () => {
      const colors1 = getChartColors(3);
      const colors2 = getChartColors(3);
      expect(colors1).toEqual(colors2);
    });
  });

  describe('getEnergyColors function', () => {
    it('should return all energy colors', () => {
      const colors = getEnergyColors();
      expect(colors).toHaveLength(CHART_PALETTES.energy.length);
    });

    it('should return valid hex colors', () => {
      const colors = getEnergyColors();
      colors.forEach(color => {
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should use energy palette colors', () => {
      const colors = getEnergyColors();
      colors.forEach((color, index) => {
        expect(color).toBe(CHART_PALETTES.energy[index]);
      });
    });

    it('should return a copy of the palette', () => {
      const colors = getEnergyColors();
      colors.push('#test');
      const colors2 = getEnergyColors();
      expect(colors2).not.toContain('#test');
    });
  });

  describe('getStatusColors function', () => {
    it('should return all status colors', () => {
      const colors = getStatusColors();
      expect(colors).toHaveLength(CHART_PALETTES.status.length);
    });

    it('should return valid hex colors', () => {
      const colors = getStatusColors();
      colors.forEach(color => {
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should use status palette colors', () => {
      const colors = getStatusColors();
      colors.forEach((color, index) => {
        expect(color).toBe(CHART_PALETTES.status[index]);
      });
    });

    it('should return a copy of the palette', () => {
      const colors = getStatusColors();
      colors.push('#test');
      const colors2 = getStatusColors();
      expect(colors2).not.toContain('#test');
    });
  });

  describe('Color accessibility and contrast', () => {
    it('should have sufficient contrast between adjacent colors in default palette', () => {
      // This is a basic check - in a real app you might use a color contrast library
      const colors = CHART_PALETTES.default;
      for (let i = 0; i < colors.length - 1; i++) {
        expect(colors[i]).not.toBe(colors[i + 1]);
      }
    });

    it('should have distinct status colors for accessibility', () => {
      const statusColors = CHART_PALETTES.status;
      const uniqueColors = new Set(statusColors);
      expect(uniqueColors.size).toBe(statusColors.length);
    });
  });

  describe('Integration with chart components', () => {
    it('should provide colors that work with chart libraries', () => {
      // Test that colors are in the format expected by chart libraries (hex strings)
      const colors = getChartColors(5);
      colors.forEach(color => {
        expect(typeof color).toBe('string');
        expect(color.startsWith('#')).toBe(true);
        expect(color.length).toBe(7); // #RRGGBB format
      });
    });

    it('should maintain color consistency across different function calls', () => {
      const defaultColors = getChartColors(3);
      const energyColors = getEnergyColors(3);
      const statusColors = getStatusColors(3);

      // Each function should return consistent results
      expect(getChartColors(3)).toEqual(defaultColors);
      expect(getEnergyColors(3)).toEqual(energyColors);
      expect(getStatusColors(3)).toEqual(statusColors);
    });
  });
});
