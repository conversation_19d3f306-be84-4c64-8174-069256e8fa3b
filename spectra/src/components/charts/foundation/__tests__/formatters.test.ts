import {
  formatNumericValue,
  formatPercentage,
  formatTimeAxis,
  formatDateAxis,
  formatSmartDateTime,
  formatTooltipTime,
  formatBoolean,
  formatTooltipValue,
  formatUnit,
  formatLabelWithUnit,
  isMultiDayRange,
  formatSmartAxis,
  formatEnhancedTooltipTime
} from '../formatters';

describe('Chart Formatters', () => {
  describe('formatNumericValue - Critical 2-Decimal Rule', () => {
    it('should format integers without decimal places', () => {
      expect(formatNumericValue(123)).toBe('123');
      expect(formatNumericValue(0)).toBe('0');
      expect(formatNumericValue(1000)).toBe('1000');
      expect(formatNumericValue(-50)).toBe('-50');
    });

    it('should format decimals with maximum 2 decimal places', () => {
      expect(formatNumericValue(123.4)).toBe('123.40');
      expect(formatNumericValue(123.45)).toBe('123.45');
      expect(formatNumericValue(0.1)).toBe('0.10');
      expect(formatNumericValue(0.12)).toBe('0.12');
    });

    it('should truncate to 2 decimal places (critical requirement)', () => {
      expect(formatNumericValue(123.456)).toBe('123.46');
      expect(formatNumericValue(123.456789)).toBe('123.46');
      expect(formatNumericValue(0.123)).toBe('0.12');
      expect(formatNumericValue(0.999)).toBe('1.00');
      expect(formatNumericValue(99.999)).toBe('100.00');
    });

    it('should handle edge cases', () => {
      expect(formatNumericValue(NaN)).toBe('N/A');
      expect(formatNumericValue(Infinity)).toBe('Infinity');
      expect(formatNumericValue(-Infinity)).toBe('-Infinity');
    });

    it('should handle string inputs', () => {
      expect(formatNumericValue('123')).toBe('123');
      expect(formatNumericValue('invalid')).toBe('invalid');
      expect(formatNumericValue('')).toBe('');
    });

    it('should handle null and undefined', () => {
      expect(formatNumericValue(null as any)).toBe('N/A');
      expect(formatNumericValue(undefined as any)).toBe('N/A');
    });

    it('should handle very small numbers', () => {
      expect(formatNumericValue(0.001)).toBe('0.00');
      expect(formatNumericValue(0.009)).toBe('0.01');
      expect(formatNumericValue(0.0001)).toBe('0.00');
    });

    it('should handle very large numbers', () => {
      expect(formatNumericValue(1234567.89)).toBe('1234567.89');
      expect(formatNumericValue(1234567.123)).toBe('1234567.12');
    });
  });

  describe('formatPercentage', () => {
    it('should format percentages with default 1 decimal place', () => {
      expect(formatPercentage(85)).toBe('85.0%');
      expect(formatPercentage(85.5)).toBe('85.5%');
      expect(formatPercentage(85.55)).toBe('85.5%');
    });

    it('should format percentages with custom decimal places', () => {
      expect(formatPercentage(85.555, 2)).toBe('85.56%');
      expect(formatPercentage(85.555, 0)).toBe('86%');
      expect(formatPercentage(85.555, 3)).toBe('85.555%');
    });

    it('should handle edge cases', () => {
      expect(formatPercentage(0)).toBe('0.0%');
      expect(formatPercentage(100)).toBe('100.0%');
      expect(formatPercentage(NaN)).toBe('N/A');
      expect(formatPercentage(null as any)).toBe('N/A');
    });
  });

  describe('formatTimeAxis', () => {
    it('should format timestamps as time', () => {
      const timestamp = new Date('2024-01-01T14:30:00Z').getTime();
      const result = formatTimeAxis(timestamp);
      // Result will vary by locale, but should contain hour and minute
      expect(result).toMatch(/\d{1,2}:\d{2}/);
    });

    it('should handle string timestamps', () => {
      const result = formatTimeAxis('2024-01-01T14:30:00Z');
      expect(result).toMatch(/\d{1,2}:\d{2}/);
    });

    it('should handle invalid dates gracefully', () => {
      const result = formatTimeAxis('invalid');
      expect(result).toBe('Invalid Date');
    });
  });

  describe('formatDateAxis', () => {
    it('should format timestamps as dates', () => {
      const timestamp = new Date('2024-01-01T14:30:00Z').getTime();
      const result = formatDateAxis(timestamp);
      // Result will vary by locale, but should contain date components
      expect(result).toMatch(/\d/);
    });

    it('should handle string timestamps', () => {
      const result = formatDateAxis('2024-01-01T14:30:00Z');
      expect(result).toMatch(/\d/);
    });
  });

  describe('formatSmartDateTime', () => {
    const timestamp = new Date('2024-01-01T14:30:00Z').getTime();
    const oneDay = 24 * 60 * 60 * 1000;

    it('should use time format for short ranges', () => {
      const result = formatSmartDateTime(timestamp, oneDay / 2);
      expect(result).toMatch(/\d{1,2}:\d{2}/);
    });

    it('should use date format for long ranges', () => {
      const result = formatSmartDateTime(timestamp, oneDay * 2);
      expect(result).toMatch(/\d/);
    });
  });

  describe('formatTooltipTime', () => {
    it('should format full timestamp for tooltips', () => {
      const timestamp = new Date('2024-01-01T14:30:00Z').getTime();
      const result = formatTooltipTime(timestamp);
      // Should contain both date and time components
      expect(result).toMatch(/\d/);
    });

    it('should handle string timestamps', () => {
      const result = formatTooltipTime('2024-01-01T14:30:00Z');
      expect(result).toMatch(/\d/);
    });
  });

  describe('formatBoolean', () => {
    it('should format true as "True"', () => {
      expect(formatBoolean(true)).toBe('True');
    });

    it('should format false as "False"', () => {
      expect(formatBoolean(false)).toBe('False');
    });
  });

  describe('formatTooltipValue - Generic Formatter', () => {
    it('should format numbers using formatNumericValue', () => {
      expect(formatTooltipValue(123.456)).toBe('123.46');
      expect(formatTooltipValue(123)).toBe('123');
    });

    it('should format booleans using formatBoolean', () => {
      expect(formatTooltipValue(true)).toBe('True');
      expect(formatTooltipValue(false)).toBe('False');
    });

    it('should pass through strings unchanged', () => {
      expect(formatTooltipValue('Custom Text')).toBe('Custom Text');
      expect(formatTooltipValue('')).toBe('');
    });

    it('should handle null and undefined', () => {
      expect(formatTooltipValue(null)).toBe('');
      expect(formatTooltipValue(undefined)).toBe('');
    });
  });

  describe('formatUnit', () => {
    it('should format units with parentheses', () => {
      expect(formatUnit('kW')).toBe(' (kW)');
      expect(formatUnit('kWh')).toBe(' (kWh)');
      expect(formatUnit('°C')).toBe(' (°C)');
    });

    it('should handle empty units', () => {
      expect(formatUnit('')).toBe('');
      expect(formatUnit(null as any)).toBe('');
      expect(formatUnit(undefined as any)).toBe('');
    });
  });

  describe('formatLabelWithUnit', () => {
    it('should combine label with unit', () => {
      expect(formatLabelWithUnit('Power Output', 'kW')).toBe('Power Output (kW)');
      expect(formatLabelWithUnit('Temperature', '°C')).toBe('Temperature (°C)');
    });

    it('should handle missing units', () => {
      expect(formatLabelWithUnit('Status', '')).toBe('Status');
      expect(formatLabelWithUnit('Status', null as any)).toBe('Status');
      expect(formatLabelWithUnit('Status', undefined)).toBe('Status');
    });

    it('should handle empty labels', () => {
      expect(formatLabelWithUnit('', 'kW')).toBe(' (kW)');
    });
  });

  // Integration tests for critical 2-decimal rule across all formatters
  describe('2-Decimal Rule Integration', () => {
    const testValues = [
      123.456789,
      0.123456,
      999.999999,
      0.001234,
      1234567.123456
    ];

    it('should enforce 2-decimal rule in formatNumericValue', () => {
      testValues.forEach(value => {
        const result = formatNumericValue(value);
        if (result !== 'N/A' && !result.includes('.')) {
          // Integer result is acceptable
          expect(Number.isInteger(Number(result))).toBe(true);
        } else if (result.includes('.')) {
          // Decimal result should have exactly 2 decimal places
          const decimalPart = result.split('.')[1];
          expect(decimalPart.length).toBe(2);
        }
      });
    });

    it('should enforce 2-decimal rule in formatTooltipValue for numbers', () => {
      testValues.forEach(value => {
        const result = formatTooltipValue(value);
        if (result.includes('.')) {
          const decimalPart = result.split('.')[1];
          expect(decimalPart.length).toBe(2);
        }
      });
    });
  });

  // Tests for new enhanced date/time formatters
  describe('Enhanced Date/Time Formatters', () => {
    // Use local dates to avoid timezone issues in tests
    const today = new Date();
    const sameDay = {
      start: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 8, 0).getTime(),
      end: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 18, 0).getTime()
    };

    const multiDay = {
      start: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 8, 0).getTime(),
      end: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 2, 18, 0).getTime()
    };

    describe('isMultiDayRange', () => {
      it('should detect same day range', () => {
        expect(isMultiDayRange(sameDay.start, sameDay.end)).toBe(false);
      });

      it('should detect multi-day range', () => {
        expect(isMultiDayRange(multiDay.start, multiDay.end)).toBe(true);
      });

      it('should handle string timestamps for different days', () => {
        const day1 = new Date(2024, 0, 15, 8, 0).toISOString();
        const day2 = new Date(2024, 0, 17, 18, 0).toISOString();
        expect(isMultiDayRange(day1, day2)).toBe(true);
      });
    });

    describe('formatSmartAxis', () => {
      const timestamp = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 14, 30).getTime();

      it('should use time format for same-day range', () => {
        const result = formatSmartAxis(timestamp, sameDay);
        // Should contain time elements (flexible for different locales)
        expect(result).toMatch(/\d{1,2}:\d{2}/); // Should include time format
      });

      it('should use date format for multi-day range', () => {
        const result = formatSmartAxis(timestamp, multiDay);
        // Should be date format (flexible for different locales)
        expect(result).toMatch(/\d/); // Should contain numbers (date)
        expect(result.length).toBeGreaterThan(5); // Date strings are typically longer than time
      });
    });

    describe('formatEnhancedTooltipTime', () => {
      const timestamp = new Date(2024, 0, 15, 14, 30).getTime();

      it('should show full timestamp by default', () => {
        const result = formatEnhancedTooltipTime(timestamp);
        expect(result).toMatch(/\d/); // Should contain date/time numbers
        expect(result.length).toBeGreaterThan(10); // Full timestamp should be longer
      });

      it('should show time only when requested', () => {
        const result = formatEnhancedTooltipTime(timestamp, false);
        expect(result).toMatch(/\d{1,2}:\d{2}/); // Should include time format
        expect(result.length).toBeLessThan(20); // Time-only should be shorter
      });
    });
  });
});
