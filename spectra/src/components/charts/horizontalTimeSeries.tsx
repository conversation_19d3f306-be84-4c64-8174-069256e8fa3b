import type { Timeseries } from "api/data.ts";
import { useSelectedTimeRange } from "context/SelectedTimeRangeContext";
import {
  Bar,
  BarChart,
  Cell,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { useState } from "react";
import {
  formatNumericValue,
  formatDateAxis,
  CHART_COLORS,
  CHART_DIMENSIONS,
  CHART_MESSAGES,
  TOOLTIP_CONFIG,
} from "./foundation";
import { ChartErrorBoundary } from "../uikit/ErrorBoundary/ChartErrorBoundary";

const tooltipFormatter = (value: number | string) => {
  return formatNumericValue(value);
};

const HorizontalTimeseriesChartInner = ({
  timeseries,
}: {
  timeseries: Timeseries | null;
}) => {
  const [focusedBar, setFocusedBar] = useState<number | null>(null);
  const { start, end, isUTC } = useSelectedTimeRange();
  if (timeseries === undefined) {
    return (
      <div className="p-3">
        <p className="text-space50 text-heading3 mb-4">Energy Utilization</p>
        <p className="text-space70 text-caption">{CHART_MESSAGES.loading}</p>
      </div>
    );
  }

  if (!timeseries) {
    return (
      <div className="p-3">
        <p className="text-space50 text-heading3 mb-4">Energy Utilization</p>
        <p className="text-space70 text-caption">{CHART_MESSAGES.noData}</p>
      </div>
    );
  }

  const startTime = isUTC ? start.local().valueOf() : start.valueOf();
  const endTime = isUTC ? end.local().valueOf() : end.valueOf();

  // Filter out null/undefined values and ensure they have a valid time property
  const filteredValues = timeseries.values
    .filter((value) => value != null && value.time != null)
    .filter((value) => {
      try {
        const valueTime = new Date(value.time).getTime();
        return !Number.isNaN(valueTime) && valueTime >= startTime && valueTime <= endTime;
      } catch (error) {
        console.warn("Invalid time value in timeseries data:", value.time);
        return false;
      }
    });
  return (
    <div className="p-3">
      <p className="text-space50 text-heading3 mb-4">Energy Utilization</p>
      <ResponsiveContainer width="100%" height={275}>
        <ComposedChart
          data={filteredValues}
          margin={{ right: 20, left: -20, top: 10 }}
          barGap={0}
          barCategoryGap={0}
          onMouseMove={(state) => {
            if (state.isTooltipActive) {
              setFocusedBar(state.activeTooltipIndex);
            } else {
              setFocusedBar(null);
            }
          }}
        >
          <XAxis
            dataKey="time"
            stroke={CHART_COLORS.text}
            scale={"time"}
            tickFormatter={(timestamp) => formatDateAxis(timestamp)}
            domain={[
              isUTC ? start.local().valueOf() : start.valueOf(),
              isUTC ? end.local().valueOf() : end.valueOf(),
            ]}
          />
          <YAxis scale={"linear"} stroke={CHART_COLORS.text} />
          <Tooltip
            cursor={TOOLTIP_CONFIG.cursor}
            formatter={tooltipFormatter}
          />
          <Legend />
          {/* <Bar type="monotone" dataKey="net" fill="#89A7BF" name="Net (kWh)">
            {timeseries.values.map((_, index) => (
              <Cell
                key={index}
                fill={focusedBar === index ? "#89A7BF" : "#6B89A2"}
              />
            ))}
          </Bar> */}
          <Line
            type="monotone"
            dataKey="stored"
            fill={CHART_COLORS.stored}
            name="Stored (kWh)"
            dot={false}
          />
          <Bar
            type="monotone"
            stackId="a"
            dataKey="fwd"
            fill={CHART_COLORS.forward}
            name="Fwd (kWh)"
          >
            {filteredValues.map((value, index) => {
              // Additional safety check for individual values
              if (!value || value.time == null) {
                return null;
              }

              const isLastDataPoint = index === filteredValues.length - 1;
              const ONE_HOUR_MS = 3600000;

              let timeElapsed = 0;
              try {
                timeElapsed = new Date().getTime() - new Date(value.time).getTime();
              } catch (error) {
                console.warn("Error calculating time elapsed for value:", value.time);
                timeElapsed = ONE_HOUR_MS + 1; // Default to not recent
              }

              const isRecentData = isLastDataPoint && timeElapsed <= ONE_HOUR_MS;

              return (
                <Cell
                  key={`cell-${index}-${value.time}`}
                  fill={
                    isRecentData
                      ? "url(#blinkingGradient)"
                      : focusedBar === index
                        ? CHART_COLORS.net
                        : CHART_COLORS.forward
                  }
                />
              );
            }).filter(Boolean)}
          </Bar>
          <defs>
            <linearGradient id="blinkingGradient" x1="0" y1="1" x2="0" y2="0">
              <animate
                attributeName="y1"
                values="1;0"
                dur="2s"
                repeatCount="indefinite"
              />
              <stop offset="0%" stopColor={CHART_COLORS.forward} />
              <stop offset="100%" stopColor="#D3DBE1" />
            </linearGradient>
          </defs>
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export const SmallHorizontalTimeseriesChart = ({
  timeseries,
}: {
  timeseries: Timeseries | null;
}) => {
  const [focusedBar, setFocusedBar] = useState<number | null>(null);

  if (timeseries === undefined) {
    return (
      <div>
        <p className="text-space70 text-caption">{CHART_MESSAGES.loading}</p>
      </div>
    );
  }

  if (!timeseries) {
    return (
      <div>
        <p className="text-space70 text-caption">{CHART_MESSAGES.noData}</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={275}>
        <BarChart
          data={timeseries.values}
          margin={{ right: 20, left: -20 }}
          barGap={0}
          barCategoryGap={0}
          onMouseMove={(state) => {
            if (state.isTooltipActive) {
              setFocusedBar(state.activeTooltipIndex);
            } else {
              setFocusedBar(null);
            }
          }}
        >
          <XAxis
            dataKey="time"
            stroke={CHART_COLORS.text}
            scale={"time"}
            tickFormatter={(timestamp) => formatDateAxis(timestamp)}
          />
          <Tooltip cursor={false} formatter={tooltipFormatter} />
          <Bar
            type="monotone"
            stackId="a"
            dataKey="net"
            fill={CHART_COLORS.net}
            name="Net (kWh)"
          >
            {timeseries.values
              .filter((value) => value != null && value.time != null)
              .map((value, index) => (
                <Cell
                  key={`net-cell-${index}-${value.time}`}
                  fill={
                    focusedBar === index ? CHART_COLORS.stored : CHART_COLORS.net
                  }
                />
              ))}
          </Bar>
          <Bar
            type="monotone"
            stackId="a"
            dataKey="fwd"
            fill={CHART_COLORS.forward}
            name="Fwd (kWh)"
          >
            {timeseries.values
              .filter((value) => value != null && value.time != null)
              .map((value, index) => (
                <Cell
                  key={`fwd-cell-${index}-${value.time}`}
                  fill={
                    focusedBar === index ? CHART_COLORS.net : CHART_COLORS.forward
                  }
                />
              ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

const MicroHorizontalTimeseriesChartInner = ({
  timeseries,
}: {
  timeseries: Timeseries | null;
}) => {
  const height = CHART_DIMENSIONS.micro;

  if (timeseries === undefined) {
    return (
      <div
        className={`h-[${height}px] flex flex-col justify-center items-center`}
      >
        <p className="text-space80 text-caption">{CHART_MESSAGES.loading}</p>
      </div>
    );
  }

  if (!timeseries) {
    return (
      <div
        className={`h-[${height}px] flex flex-col justify-center items-center`}
      >
        <p className="text-space80 text-caption">
          No power utilization data for the past 7 days
        </p>
      </div>
    );
  }

  // Filter out null values before processing
  const validValues = timeseries.values.filter((v) => v != null && v.time != null);

  const netMax = validValues.reduce(
    (acc, v) => Math.max(acc, v.net || 0),
    Number.NEGATIVE_INFINITY,
  );

  const fwdMax = validValues.reduce(
    (acc, v) => Math.max(acc, v.fwd || 0),
    Number.NEGATIVE_INFINITY,
  );

  return (
    <div
      className={`self-stretch h-[${height}px] pb-1 flex-col justify-center items-center gap-0.5 flex`}
    >
      <div className="self-stretch grow gap-0.5 justify-start items-end inline-flex">
        {validValues.map((value, index) => (
          <div
            className="grow basis-0 h-full flex-col justify-end items-start inline-flex relative"
            key={`micro-${index}-${value.time}`}
          >
            <div
              className="bg-slate-400 w-full absolute"
              style={{
                height: `${((value.net || 0) / netMax) * 100}%`,
              }}
            />
            <div
              className="bg-sky-200 w-full absolute"
              style={{
                height: `${((value.fwd || 0) / fwdMax) * 100}%`,
              }}
            />
          </div>
        ))}
      </div>
      <div className="self-stretch text-center text-zinc-500 text-xs font-normal">
        7 Day Power Utilization
      </div>
    </div>
  );
};

// Wrapped exports with error boundaries
export const HorizontalTimeseriesChart = ({
  timeseries,
}: {
  timeseries: Timeseries | null;
}) => (
  <ChartErrorBoundary chartName="Energy Utilization Chart">
    <HorizontalTimeseriesChartInner timeseries={timeseries} />
  </ChartErrorBoundary>
);

export const MicroHorizontalTimeseriesChart = ({
  timeseries,
}: {
  timeseries: Timeseries | null;
}) => (
  <ChartErrorBoundary chartName="Power Utilization Chart">
    <MicroHorizontalTimeseriesChartInner timeseries={timeseries} />
  </ChartErrorBoundary>
);
