import type { Timeseries } from "api/data.ts";
import { useSelectedTimeRange } from "context/SelectedTimeRangeContext";
import {
  Bar,
  BarChart,
  Cell,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { useState } from "react";
import {
  formatNumericValue,
  formatDateAxis,
  CHART_COLORS,
  CHART_DIMENSIONS,
  CHART_MESSAGES,
  TOOLTIP_CONFIG,
} from "./foundation";

const tooltipFormatter = (value: number | string) => {
  return formatNumericValue(value);
};

export const HorizontalTimeseriesChart = ({
  timeseries,
}: {
  timeseries: Timeseries | null;
}) => {
  const [focusedBar, setFocusedBar] = useState<number | null>(null);
  const { start, end, isUTC } = useSelectedTimeRange();
  if (timeseries === undefined) {
    return (
      <div className="p-3">
        <p className="text-space50 text-heading3 mb-4">Energy Utilization</p>
        <p className="text-space70 text-caption">{CHART_MESSAGES.loading}</p>
      </div>
    );
  }

  if (!timeseries) {
    return (
      <div className="p-3">
        <p className="text-space50 text-heading3 mb-4">Energy Utilization</p>
        <p className="text-space70 text-caption">{CHART_MESSAGES.noData}</p>
      </div>
    );
  }

  const startTime = isUTC ? start.local().valueOf() : start.valueOf();
  const endTime = isUTC ? end.local().valueOf() : end.valueOf();

  const filteredValues = timeseries.values.filter((value) => {
    const valueTime = new Date(value.time).getTime();
    return valueTime >= startTime && valueTime <= endTime;
  });
  return (
    <div className="p-3">
      <p className="text-space50 text-heading3 mb-4">Energy Utilization</p>
      <ResponsiveContainer width="100%" height={275}>
        <ComposedChart
          data={filteredValues}
          margin={{ right: 20, left: -20, top: 10 }}
          barGap={0}
          barCategoryGap={0}
          onMouseMove={(state) => {
            if (state.isTooltipActive) {
              setFocusedBar(state.activeTooltipIndex);
            } else {
              setFocusedBar(null);
            }
          }}
        >
          <XAxis
            dataKey="time"
            stroke={CHART_COLORS.text}
            scale={"time"}
            tickFormatter={(timestamp) => formatDateAxis(timestamp)}
            domain={[
              isUTC ? start.local().valueOf() : start.valueOf(),
              isUTC ? end.local().valueOf() : end.valueOf(),
            ]}
          />
          <YAxis scale={"linear"} stroke={CHART_COLORS.text} />
          <Tooltip
            cursor={TOOLTIP_CONFIG.cursor}
            formatter={tooltipFormatter}
          />
          <Legend />
          {/* <Bar type="monotone" dataKey="net" fill="#89A7BF" name="Net (kWh)">
            {timeseries.values.map((_, index) => (
              <Cell
                key={index}
                fill={focusedBar === index ? "#89A7BF" : "#6B89A2"}
              />
            ))}
          </Bar> */}
          <Line
            type="monotone"
            dataKey="stored"
            fill={CHART_COLORS.stored}
            name="Stored (kWh)"
            dot={false}
          />
          <Bar
            type="monotone"
            stackId="a"
            dataKey="fwd"
            fill={CHART_COLORS.forward}
            name="Fwd (kWh)"
          >
            {filteredValues.map((value, index) => {
              const isLastDataPoint = index === filteredValues.length - 1;
              const ONE_HOUR_MS = 3600000;
              const timeElapsed =
                new Date().getTime() - new Date(value.time).getTime();
              const isRecentData =
                isLastDataPoint && timeElapsed <= ONE_HOUR_MS;

              return (
                <Cell
                  key={value.time}
                  fill={
                    isRecentData
                      ? "url(#blinkingGradient)"
                      : focusedBar === index
                        ? CHART_COLORS.net
                        : CHART_COLORS.forward
                  }
                />
              );
            })}
          </Bar>
          <defs>
            <linearGradient id="blinkingGradient" x1="0" y1="1" x2="0" y2="0">
              <animate
                attributeName="y1"
                values="1;0"
                dur="2s"
                repeatCount="indefinite"
              />
              <stop offset="0%" stopColor={CHART_COLORS.forward} />
              <stop offset="100%" stopColor="#D3DBE1" />
            </linearGradient>
          </defs>
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export const SmallHorizontalTimeseriesChart = ({
  timeseries,
}: {
  timeseries: Timeseries | null;
}) => {
  const [focusedBar, setFocusedBar] = useState<number | null>(null);

  if (timeseries === undefined) {
    return (
      <div>
        <p className="text-space70 text-caption">{CHART_MESSAGES.loading}</p>
      </div>
    );
  }

  if (!timeseries) {
    return (
      <div>
        <p className="text-space70 text-caption">{CHART_MESSAGES.noData}</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={275}>
        <BarChart
          data={timeseries.values}
          margin={{ right: 20, left: -20 }}
          barGap={0}
          barCategoryGap={0}
          onMouseMove={(state) => {
            if (state.isTooltipActive) {
              setFocusedBar(state.activeTooltipIndex);
            } else {
              setFocusedBar(null);
            }
          }}
        >
          <XAxis
            dataKey="time"
            stroke={CHART_COLORS.text}
            scale={"time"}
            tickFormatter={(timestamp) => formatDateAxis(timestamp)}
          />
          <Tooltip cursor={false} formatter={tooltipFormatter} />
          <Bar
            type="monotone"
            stackId="a"
            dataKey="net"
            fill={CHART_COLORS.net}
            name="Net (kWh)"
          >
            {timeseries.values.map((value, index) => (
              <Cell
                key={value.time}
                fill={
                  focusedBar === index ? CHART_COLORS.stored : CHART_COLORS.net
                }
              />
            ))}
          </Bar>
          <Bar
            type="monotone"
            stackId="a"
            dataKey="fwd"
            fill={CHART_COLORS.forward}
            name="Fwd (kWh)"
          >
            {timeseries.values.map((value, index) => (
              <Cell
                key={value.time}
                fill={
                  focusedBar === index ? CHART_COLORS.net : CHART_COLORS.forward
                }
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export const MicroHorizontalTimeseriesChart = ({
  timeseries,
}: {
  timeseries: Timeseries | null;
}) => {
  const height = CHART_DIMENSIONS.micro;

  if (timeseries === undefined) {
    return (
      <div
        className={`h-[${height}px] flex flex-col justify-center items-center`}
      >
        <p className="text-space80 text-caption">{CHART_MESSAGES.loading}</p>
      </div>
    );
  }

  if (!timeseries) {
    return (
      <div
        className={`h-[${height}px] flex flex-col justify-center items-center`}
      >
        <p className="text-space80 text-caption">
          No power utilization data for the past 7 days
        </p>
      </div>
    );
  }

  const netMax = timeseries.values.reduce(
    (acc, v) => Math.max(acc, v.net),
    Number.NEGATIVE_INFINITY,
  );

  const fwdMax = timeseries.values.reduce(
    (acc, v) => Math.max(acc, v.fwd),
    Number.NEGATIVE_INFINITY,
  );

  return (
    <div
      className={`self-stretch h-[${height}px] pb-1 flex-col justify-center items-center gap-0.5 flex`}
    >
      <div className="self-stretch grow gap-0.5 justify-start items-end inline-flex">
        {timeseries.values.map((value) => (
          <div
            className="grow basis-0 h-full flex-col justify-end items-start inline-flex relative"
            key={value.time}
          >
            <div
              className="bg-slate-400 w-full absolute"
              style={{
                height: `${(value.net / netMax) * 100}%`,
              }}
            />
            <div
              className="bg-sky-200 w-full absolute"
              style={{
                height: `${(value.fwd / fwdMax) * 100}%`,
              }}
            />
          </div>
        ))}
      </div>
      <div className="self-stretch text-center text-zinc-500 text-xs font-normal">
        7 Day Power Utilization
      </div>
    </div>
  );
};
