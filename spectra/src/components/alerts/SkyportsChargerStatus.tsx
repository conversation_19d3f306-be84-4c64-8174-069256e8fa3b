import type { Thing } from "api/ingestion/things";
import { DeviceTile } from "components";

interface DeviceGroup {
  [key: string]: Thing[];
}

const formatTitle = (type: string): string => {
  return type
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
};

const Section = ({ title, devices }: { title: string; devices: Thing[] }) => {
  if (devices.length === 0) return null;

  return (
    <>
      <p className="text-heading3 text-space50">{formatTitle(title)}</p>
      <div className="flex flex-wrap gap-4">
        {devices.map((device) => (
          <DeviceTile device={device} key={device.thingName} />
        ))}
      </div>
    </>
  );
};

const SkyportsChargerStatus = ({ devices }: { devices: DeviceGroup }) => {
  const hasDevices = Object.values(devices).some(
    (deviceList) => deviceList && deviceList.length > 0,
  );

  if (!hasDevices) {
    return (
      <div className="p-3">
        <div className="flex items-center justify-center py-8">
          <p className="text-space70 text-body">
            No devices exist in this site.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col gap-2 p-3">
        {Object.entries(devices).map(([type, deviceList]) => (
          <Section key={type} title={type} devices={deviceList} />
        ))}
      </div>
    </div>
  );
};

export default SkyportsChargerStatus;
