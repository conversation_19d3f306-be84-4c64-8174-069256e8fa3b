import { type AlertEvent, useEnterpriseApi } from "api/enterprise";
import { type Thing, useThingsApi } from "api/ingestion/things";
import AlertRow from "components/uikit/AlertRow";
import { useSelectedDevice } from "context/SelectedDeviceContext";
import type { Dayjs } from "dayjs";
import { useEffect, useState } from "react";

const getAlertGroupKey = (alert: AlertEvent): string => {
  return `${alert.monitorId}-${alert.resourceId}-${alert.condition}-${alert.alertMessage}`;
};

export const PlaceAlerts = ({
  placeType,
  placeId,
  partnerId,
  start,
  end,
  // simulationId,
}: {
  placeType: string;
  placeId: string;
  partnerId?: string; // TODO: technically not optional currently
  start: Dayjs;
  end: Dayjs;
  // simulationId: string | null;
}) => {
  const { getMonitorData } = useEnterpriseApi();
  const { setSelectedDevice } = useSelectedDevice();
  const { getThingsFromPlace } = useThingsApi();

  const [alerts, setAlerts] = useState<AlertEvent[] | undefined>(undefined);
  const [things, setThings] = useState<Thing[]>([]);
  const [error, setError] = useState<Error | undefined>(undefined);

  // biome-ignore lint/correctness/useExhaustiveDependencies: adding getMonitorData to the dependency array causes a re-render loop
  useEffect(() => {
    getMonitorData(placeType, placeId, partnerId, start, end)
      .then((data) => {
        const alertsByGroup = new Map<string, AlertEvent>();

        const unhealthyAlerts = data
          .flatMap((d) => d.alertEvents)
          .filter((e) => e.alertState !== "HEALTHY");

        for (const alert of unhealthyAlerts) {
          const groupKey = getAlertGroupKey(alert);
          if (
            !alertsByGroup.has(groupKey) ||
            new Date(alert.time) > new Date(alertsByGroup.get(groupKey).time)
          ) {
            alertsByGroup.set(groupKey, alert);
          }
        }

        setAlerts(Array.from(alertsByGroup.values()));
      })
      .catch((error) => {
        console.error("Error fetching alerts:", error);
        setError(error);
      });
  }, [placeType, placeId, partnerId, start, end]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: adding getThingsFromPlace to the dependency array causes a re-render loop
  useEffect(() => {
    // pre-load thing details for each alert
    getThingsFromPlace(placeType, placeId).then((thingsData) => {
      setThings(Array.isArray(thingsData) ? thingsData : [thingsData]);
    });
  }, [placeType, placeId]);

  const handleOnClick = (thingId: string) => {
    console.log("thingId", thingId);
    console.log("things", things);
    const device = things.find((thing) => thing.thingId === thingId);
    console.log("device", device);
    setSelectedDevice(device);
  };

  // TODO: re-enable this
  // if (error) {
  //   return (
  //     <div className="text-caption text-center text-red50 py-4">
  //       Error loading alerts: {error.message}
  //     </div>
  //   );
  // }

  return (
    <div className="overflow-auto h-full">
      <p className="text-space50 text-heading3">Alerts</p>
      <div className="flex flex-col gap-2 pt-2">
        {error ? (
          <div className="text-caption text-center text-space70 py-4">
            No alerts for selected timeframe
          </div>
        ) : !alerts ? (
          <div className="text-caption text-center text-space70 py-4">
            Loading...
          </div>
        ) : alerts.length > 0 ? (
          alerts.map((alert) => (
            <AlertRow
              alert={alert}
              key={alert.alertId}
              onSelect={handleOnClick}
            />
          ))
        ) : (
          <div className="text-caption text-center text-space70 py-4">
            No alerts for selected timeframe
          </div>
        )}
      </div>
    </div>
  );
};
