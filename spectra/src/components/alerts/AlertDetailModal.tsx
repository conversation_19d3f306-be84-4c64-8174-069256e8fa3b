import { type AlertEvent, useEnterpriseApi } from "api/enterprise";
import { type AlertConfig, useMonitorsApi } from "api/enterprise/monitors";
import { useAuth } from "context/AuthContext";
import { useSelectedTimeRange } from "context/SelectedTimeRangeContext";
import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import MonitorAlertStackedBar, {
  type ChartDataPoint,
} from "../monitors/MonitorAlertStackedBar";
import ButtonComponent from "../uikit/button";
import Modal from "../uikit/modal";

interface AlertDetailModalProps {
  alert: AlertEvent | null;
  placeType: string;
  placeId: string;
  onClose: () => void;
}

const AlertDetailModal = ({
  alert,
  placeType,
  placeId,
  onClose,
}: AlertDetailModalProps) => {
  const { user } = useAuth();
  const { start, end } = useSelectedTimeRange();
  const { getMonitorData } = useEnterpriseApi();
  const { getMonitorForThing } = useMonitorsApi();
  const navigate = useNavigate();

  const [monitorData, setMonitorData] = useState(undefined);
  const [alertConfig, setAlertConfig] = useState<AlertConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const chartData = useMemo(() => {
    if (!monitorData || !alertConfig) return [];

    return monitorData
      .flatMap((data) => {
        const matchingAlert = data.alertEvents.find(
          (event) => event.alertId === alertConfig.alertId,
        );
        if (!matchingAlert) return [];

        const dataPoint: ChartDataPoint = {
          timestamp: new Date(matchingAlert.time).getTime(),
          alertState: matchingAlert.alertState,
          alertSeverity: matchingAlert.alertSeverity,
          alertMessage: matchingAlert.alertMessage,
          conditionEvaluation: matchingAlert.conditionEvaluation,
        };

        return [dataPoint];
      })
      .filter(Boolean)
      .sort((a, b) => a.timestamp - b.timestamp);
  }, [monitorData, alertConfig]);

  useEffect(() => {
    if (alert) {
      setMonitorData(undefined);
      setAlertConfig(null);
      setError(null);
    }
  }, [alert]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: adding API functions to dependencies causes re-render loop
  useEffect(() => {
    if (!alert || !user?.partnerId) return;

    const fetchAlertDetails = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const monitorDataResponse = await getMonitorData(
          placeType,
          placeId,
          user.partnerId,
          start,
          end,
          alert.resourceId,
        );
        setMonitorData(monitorDataResponse);

        const monitor = await getMonitorForThing(
          alert.resourceId,
          alert.monitorId,
          user.partnerId,
        );

        const matchingAlertConfig = monitor.alertConfigs.find(
          (config) => config.alertId === alert.alertId,
        );

        if (matchingAlertConfig) {
          setAlertConfig(matchingAlertConfig);
        } else {
          setError("Alert configuration not found");
        }
      } catch (err) {
        console.error("Error fetching alert details:", err);
        setError("Failed to load alert details");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAlertDetails();
  }, [alert, user?.partnerId, start, end]);

  if (!alert) {
    return null;
  }

  const monitorPageUrl = `/monitors/${alert.resourceId}/${alert.monitorId}`;

  return (
    <div>
      <Modal
        open={!!alert}
        onClose={onClose}
        title="Monitor Chart"
        size="fullscreen"
      >
        <div className="space-y-4 pt-16">
          {isLoading && (
            <div className="flex justify-center py-8">
              <div className="text-gray-500">Loading monitor chart...</div>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="text-red-800">{error}</div>
            </div>
          )}

          {!isLoading && alertConfig && (
            <div className="space-y-4">
              <div
                className="w-full relative overflow-visible"
                style={{ minHeight: "120px" }}
              >
                {chartData.length > 0 ? (
                  <div className="relative w-full overflow-visible pt-8">
                    <MonitorAlertStackedBar
                      chartData={chartData}
                      disabled={false}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-24 bg-gray-50 rounded-lg">
                    <span className="text-gray-500 text-sm">
                      No chart data available for this alert
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="border-t pt-4 flex justify-end">
            <ButtonComponent.Pill
              onClick={() => {
                navigate(monitorPageUrl);
                onClose();
              }}
              buttonStyle="primary"
              variant="filled"
              iconAfter={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <title>Open external link</title>
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
              }
            >
              View Full Monitor Details
            </ButtonComponent.Pill>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AlertDetailModal;
