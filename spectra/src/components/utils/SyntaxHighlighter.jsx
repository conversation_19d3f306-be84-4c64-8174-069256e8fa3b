import Prism from "prismjs";
import React, { useEffect, useRef } from "react";
import "./prism.css";

const SyntaxHighlighter = ({ code, language = "clike", wrap = false }) => {
  const codeRef = useRef(null);

  // biome-ignore lint/correctness/useExhaustiveDependencies: language is needed to re-run highlighting
  useEffect(() => {
    if (codeRef.current) {
      Prism.highlightElement(codeRef.current);
    }
  }, [code, language]);

  const preStyle = {
    background: "transparent",
    margin: 0,
    padding: 0,
  };

  const codeStyle = {
    background: "transparent",
    padding: 0,
    border: "none",
    boxShadow: "none",
    whiteSpace: "pre-wrap",
  };

  return (
    <pre style={preStyle}>
      <code ref={codeRef} className={`language-${language}`} style={codeStyle}>
        {code}
      </code>
    </pre>
  );
};

export default SyntaxHighlighter;
