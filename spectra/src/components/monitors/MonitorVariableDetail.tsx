import type { MonitorDataNonOptional } from "api/enterprise";
import { useAuth } from "context/AuthContext";
import { ReactComponent as DeleteIcon } from "images/icons/delete.svg";
import { ReactComponent as PencilSquareLinedIcon } from "images/icons/pencil-square-lined.svg";
import { useEffect, useState } from "react";
import type { VariablesConfig } from "../../api/enterprise/monitors";
import { useMonitorsApi } from "../../api/enterprise/monitors";
import Modal from "../uikit/modal";
import SyntaxTextField from "../uikit/syntaxTextField";
import TextField from "../uikit/textField";
import SyntaxHighlighter from "../utils/SyntaxHighlighter";
import MonitorVariableChart from "./MonitorVariableChart";

type MonitorVariableDetailProps = {
  thingId: string;
  monitorId: string;
  variable: VariablesConfig;
  onDelete: () => void;
  onEdit: (updatedVariable: VariablesConfig) => void;
  monitorData: MonitorDataNonOptional[] | undefined;
  disableGraph?: boolean;
  availableProperties?: string[];
  availableMetrics?: string[];
};

const MonitorVariableDetail = ({
  thingId,
  monitorId,
  variable,
  onDelete,
  onEdit,
  monitorData,
  disableGraph = false,
  availableProperties = [],
  availableMetrics = [],
}: MonitorVariableDetailProps) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [editError, setEditError] = useState<string | null>(null);
  const [editedVariable, setEditedVariable] =
    useState<VariablesConfig>(variable);

  // evaulated variable type in case it cannot be determined
  const { user } = useAuth();
  const { evaluateMonitorVariable } = useMonitorsApi();
  const [evaluatedVariableType, setEvaluatedVariableType] = useState<
    string | null
  >(null);
  const [evaluationError, setEvaluationError] = useState<string | null>(null);

  // biome-ignore lint/correctness/useExhaustiveDependencies: only run on mount
  useEffect(() => {
    setEvaluationError(null);
    evaluateMonitorVariable(
      thingId,
      monitorId,
      user.partnerId,
      false,
      variable.expression,
    )
      .then((type) => {
        setEvaluatedVariableType(type);
        setEvaluationError(null);
      })
      .catch((error: unknown) => {
        console.error("Error evaluating variable type:", error);
        setEvaluationError("Unable to evaluate variable type");
        setEvaluatedVariableType(null);
      });
  }, [thingId, monitorId, user.partnerId, variable.expression]);

  const handleDelete = async () => {
    setIsDeleting(true);
    setDeleteError(null);
    try {
      await onDelete();
      setIsDeleteModalOpen(false);
    } catch (error) {
      setDeleteError(error.message);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEdit = async () => {
    setIsEditing(true);
    setEditError(null);
    try {
      await onEdit(editedVariable);
      setIsEditModalOpen(false);
    } catch (error) {
      setEditError(error.message);
    } finally {
      setIsEditing(false);
    }
  };

  const lastMonitorData = monitorData?.[monitorData.length - 1];
  const variableType =
    lastMonitorData?.monitorVariablesTypes[variable.name] ??
    evaluatedVariableType ??
    (evaluationError ? "Unknown" : "-");

  const variableData = monitorData?.map((data) => {
    const event = data.monitorEvent;
    const { monitorVariables } = event;
    return {
      timestamp: event.time,
      value: monitorVariables[variable.name],
    };
  });

  return (
    <>
      <div className="self-stretch p-4 bg-white rounded outline outline-1 outline-offset-[-1px] outline-gray95 flex flex-col lg:flex-row justify-start items-start gap-2">
        <div className="flex flex-col justify-start items-start gap-4 lg:w-1/2 w-full">
          <div className="self-stretch inline-flex justify-between items-center">
            <div className="flex-1 inline-flex flex-col justify-start items-start">
              <div className="self-stretch justify-start text-space70 text-[10px] font-normal">
                Variable Name
              </div>
              <div className="w-28 justify-start text-black text-xs font-normal font-mono leading-tight">
                {variable.name}
              </div>
            </div>
            <div className="flex-1 inline-flex flex-col justify-start items-start">
              <div className="self-stretch justify-start text-space70 text-[10px] font-normal">
                Type
              </div>
              <div
                className={`justify-start text-xs font-normal font-mono leading-tight ${
                  evaluationError ? "text-red50" : "text-black"
                }`}
              >
                {variableType}
                {evaluationError && (
                  <span className="ml-1 text-[10px] text-red50">
                    (evaluation failed)
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="self-stretch inline-flex justify-end items-end gap-2">
            <div className="flex-1 inline-flex flex-col justify-start items-start">
              <div className="self-stretch justify-start text-space70 text-[10px] font-normal">
                Variable Expression
              </div>
              <div className="justify-start text-black text-xs font-normal font-mono leading-tight">
                <SyntaxHighlighter code={variable.expression} wrap={true} />
              </div>
            </div>
            <div className="h-5 flex justify-start items-end gap-2.5">
              <button
                type="button"
                className="self-stretch rounded flex justify-center items-center gap-2.5 cursor-pointer"
                onClick={() => setIsEditModalOpen(true)}
              >
                <PencilSquareLinedIcon className="h-5 w-5 text-blue50" />
              </button>
              <button
                type="button"
                className="self-stretch rounded flex justify-center items-center gap-2.5 cursor-pointer"
                onClick={() => setIsDeleteModalOpen(true)}
              >
                <DeleteIcon className="h-5 w-5 text-red50" />
              </button>
            </div>
          </div>
        </div>
        <div className="lg:w-1/2 w-full inline-flex flex-col justify-start items-start">
          <MonitorVariableChart
            data={variableData}
            height={80}
            disabled={disableGraph}
          />
        </div>
      </div>

      <Modal
        open={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Variable"
        error={deleteError ? { message: deleteError } : undefined}
        actions={{
          cancel: {
            label: "Cancel",
            onClick: () => setIsDeleteModalOpen(false),
          },
          confirm: {
            label: isDeleting ? "Deleting..." : "Delete Variable",
            onClick: handleDelete,
            disabled: isDeleting,
            variant: "danger",
          },
        }}
      >
        <div className="text-center text-sm text-gray-500 mt-4">
          Are you sure you want to delete the variable{" "}
          <span className="font-semibold">{variable.name}</span>? This action
          cannot be undone.
        </div>
      </Modal>

      <Modal
        open={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditError(null);
          setEditedVariable(variable);
        }}
        title="Edit Variable"
        error={editError ? { message: editError } : undefined}
        actions={{
          cancel: {
            label: "Cancel",
            onClick: () => {
              setIsEditModalOpen(false);
              setEditError(null);
              setEditedVariable(variable);
            },
          },
          confirm: {
            label: isEditing ? "Saving..." : "Save Changes",
            onClick: handleEdit,
            disabled: isEditing,
          },
        }}
      >
        <div className="mt-4 space-y-4">
          <TextField
            label="Variable Name"
            value={editedVariable.name}
            onChange={(value) =>
              setEditedVariable({ ...editedVariable, name: value })
            }
          />
          <SyntaxTextField
            label="Variable Expression"
            value={editedVariable.expression}
            onChange={(value) =>
              setEditedVariable({
                ...editedVariable,
                expression: value,
              })
            }
            availableProperties={availableProperties}
            availableMetrics={availableMetrics}
          />
        </div>
      </Modal>
    </>
  );
};

export default MonitorVariableDetail;
