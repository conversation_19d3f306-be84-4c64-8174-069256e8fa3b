import { useEffect, useState } from "react";
import type {
  Monitor,
  MonitorDataState,
  MonitorState,
} from "../../api/enterprise/monitors";
import SelectField from "../../components/uikit/selectField";
import TextField from "../../components/uikit/textField";

type MonitorFiltersProps = {
  monitors: Monitor[];
  onFilterChange: (filteredMonitors: Monitor[]) => void;
};

const MonitorFilters = ({ monitors, onFilterChange }: MonitorFiltersProps) => {
  const [nameFilter, setNameFilter] = useState("");
  const [stateFilter, setStateFilter] = useState<MonitorState | "">("");
  const [dataStateFilter, setDataStateFilter] = useState<MonitorDataState | "">(
    "",
  );

  useEffect(() => {
    const filteredMonitors = monitors.filter((monitor) => {
      const matchesName = monitor.monitorName
        .toLowerCase()
        .includes(nameFilter.toLowerCase());
      const matchesState = !stateFilter || monitor.state === stateFilter;
      const matchesDataState =
        !dataStateFilter || monitor.dataState === dataStateFilter;

      return matchesName && matchesState && matchesDataState;
    });

    onFilterChange(filteredMonitors);
  }, [monitors, nameFilter, stateFilter, dataStateFilter, onFilterChange]);

  const stateOptions = [
    { value: "", label: "All States" },
    { value: "HEALTHY", label: "Healthy" },
    { value: "WARNING", label: "Warning" },
    { value: "UNHEALTHY", label: "Unhealthy" },
  ];

  const dataStateOptions = [
    { value: "", label: "All Data States" },
    { value: "COMPLETE_DATA", label: "Complete Data" },
    { value: "MISSING_DATA", label: "Missing Data" },
    { value: "NO_DATA", label: "No Data" },
  ];

  return (
    <div className="flex flex-col gap-4 mb-4">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-[200px]">
          <TextField
            label="Name"
            value={nameFilter}
            onChange={setNameFilter}
            placeholder="Filter by name..."
          />
        </div>

        <div className="w-[200px]">
          <SelectField
            label="State"
            value={stateFilter}
            options={stateOptions}
            onChange={(value) => setStateFilter(value as MonitorState | "")}
            placeholder="Select state"
          />
        </div>

        <div className="w-[200px]">
          <SelectField
            label="Data State"
            value={dataStateFilter}
            options={dataStateOptions}
            onChange={(value) =>
              setDataStateFilter(value as MonitorDataState | "")
            }
            placeholder="Select data state"
          />
        </div>
      </div>
    </div>
  );
};

export { MonitorFilters };
