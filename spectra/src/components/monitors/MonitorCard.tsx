import { useAuth } from "context/AuthContext";
import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { dayjs } from "utils/dayjs";
import type { MonitorData } from "../../api/enterprise";
import type {
  Monitor,
  MonitorDataState,
  MonitorState,
} from "../../api/enterprise/monitors";
import { useMonitorsApi } from "../../api/enterprise/monitors";
import { useDevicesStore } from "../../stores/devicesStore";
import MicroMonitorAlertChart from "./MicroMonitorAlertChart";

const MONITOR_STATE_COLORS: Record<MonitorState, string> = {
  UNHEALTHY: "bg-red90 text-red50",
  WARNING: "bg-yellow90 text-yellow40",
  HEALTHY: "bg-green90 text-green50",
};

const MONITOR_DATA_STATE_COLORS: Record<MonitorDataState, string> = {
  MISSING_DATA: "bg-yellow90 text-yellow50",
  NO_DATA: "bg-red90 text-red50",
  COMPLETE_DATA: "bg-green90 text-green50",
};

type MonitorCardProps = {
  monitor: Monitor;
};

const MonitorCard = ({ monitor }: MonitorCardProps) => {
  const devices = useDevicesStore((state) => state.devices);
  const device = devices.find((d) => d.thingId === monitor.resourceId);

  // State for monitor data
  const { user } = useAuth();
  const { getMonitorDataForPlace } = useMonitorsApi();

  const [monitorData, setMonitorData] = useState<MonitorData[] | undefined>(
    undefined,
  );
  const [error, setError] = useState<string | undefined>(undefined);
  const [hasApiError, setHasApiError] = useState(false);

  const fetchMonitorData = async () => {
    if (monitor && user?.partnerId) {
      try {
        const data = await getMonitorDataForPlace(
          monitor.placeId,
          monitor.placeType,
          monitor.resourceId,
          monitor.monitorId,
          user.partnerId,
          dayjs().subtract(1, "day"),
          dayjs(),
        );
        setMonitorData(data);
        setError(undefined);
        setHasApiError(false);
      } catch (error: unknown) {
        const errorObj = error as {
          status?: number;
          statusCode?: number;
          message?: string;
        };
        if (
          errorObj.status ||
          errorObj.statusCode ||
          errorObj.message?.includes("Monitor data not found")
        ) {
          setHasApiError(true);
          setError("Unable to fetch monitor data");
        } else {
          setError(errorObj.message || "An error occurred");
        }
      }
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchMonitorData is not a dependency
  useEffect(() => {
    fetchMonitorData();
  }, [monitor, user?.partnerId]);

  return (
    <Link
      to={`/monitors/${monitor.resourceId}/${monitor.monitorId}`}
      className="hover:no-underline"
    >
      <div className="w-full mb-4 bg-white rounded-md shadow border border-zinc-300 gap-4 transition-transform hover:-translate-y-0.5 hover:shadow-md p-4 flex flex-col gap-2 lg:flex-row">
        <div className="flex flex-col items-leading gap-2 grow">
          <div className="flex flex-row gap-2 items-center">
            <div className="text-space60 text-heading2">
              {monitor.monitorName}
            </div>
            {!monitor.isEnabled && (
              <div className="font-mono leading-tight px-2 py-1 rounded text-red50 bg-red90 text-[10px] uppercase">
                Disabled
              </div>
            )}
          </div>

          <div className="flex flex-row gap-6 justify-between">
            <div>
              <div className="text-space70 text-xs font-normal">Resource</div>
              <div className="text-space60 text-sm font-normal font-mono leading-tight">
                {monitor.resourceType}
              </div>
            </div>

            <div>
              <div className="text-space70 text-xs font-normal">
                Resource Name
              </div>
              <div className="text-space60 text-sm font-normal font-mono leading-tight">
                {device?.thingName ?? "-"}
              </div>
            </div>

            <div>
              <div className="text-space70 text-xs font-normal">Lookback</div>
              <div className="text-space60 text-sm font-normal font-mono leading-tight">
                {monitor.lookbackPeriod}
              </div>
            </div>

            <div>
              <div className="text-space70 text-xs font-normal">Frequency</div>
              <div className="text-space60 text-sm font-normal font-mono leading-tight">
                {monitor.frequency}
              </div>
            </div>
          </div>

          <div className="flex flex-row gap-4">
            <div>
              <div
                className={`text-xs font-normal font-mono leading-tight px-2 py-1 rounded ${
                  MONITOR_STATE_COLORS[monitor.state as MonitorState] || ""
                }`}
              >
                <span className="text-space70">State: </span>
                {monitor.state}
              </div>
            </div>

            <div>
              <div
                className={`text-xs font-normal font-mono leading-tight px-2 py-1 rounded ${
                  MONITOR_DATA_STATE_COLORS[
                    monitor.dataState as MonitorDataState
                  ] || ""
                }`}
              >
                <span className="text-space70">Data State: </span>
                {monitor.dataState}
              </div>
            </div>
          </div>
        </div>

        <div className="w-full lg:w-1/2 flex items-center justify-center">
          {hasApiError ? (
            <div className="text-center">
              <div className="text-red50 text-sm font-medium mb-1">
                Unable to Load Data
              </div>
              <div className="text-space70 text-xs mb-2">
                Monitor data unavailable
              </div>
            </div>
          ) : error ? (
            <div className="text-red50 text-xs">{error}</div>
          ) : !monitor.isEnabled ? (
            <div className="text-space70 text-xs">
              Graphs unavailable for disabled monitors
            </div>
          ) : (
            monitorData && <MicroMonitorAlertChart monitorData={monitorData} />
          )}
        </div>
      </div>
    </Link>
  );
};

export { MonitorCard };
