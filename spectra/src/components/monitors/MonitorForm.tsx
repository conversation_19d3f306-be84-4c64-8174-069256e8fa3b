import type { ReactNode } from "react";

type MonitorFormProps = {
  children: ReactNode;
  onSubmit: () => void;
  onCancel: () => void;
  submitLabel: string;
  isSubmitting: boolean;
  error?: string | null;
};

const MonitorForm = ({
  children,
  onSubmit,
  onCancel,
  submitLabel,
  isSubmitting,
  error,
}: MonitorFormProps) => (
  <div className="mt-4 space-y-4">
    {children}
    <div className="mt-5 flex justify-end gap-3">
      <button
        type="button"
        onClick={onCancel}
        className="inline-flex justify-center rounded-md bg-gray-100 px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm hover:bg-gray-200"
      >
        Cancel
      </button>
      <button
        type="button"
        onClick={onSubmit}
        disabled={isSubmitting}
        className="inline-flex justify-center rounded-md bg-blue50 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue40 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue50 disabled:bg-gray-400"
      >
        {isSubmitting ? "Saving..." : submitLabel}
      </button>
    </div>
  </div>
);

export default MonitorForm;
