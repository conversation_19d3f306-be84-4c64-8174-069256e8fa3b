import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import {
  AXIS_CONFIG,
  CHART_COLORS,
  CHART_MARGINS,
  CHART_TYPE_CONFIG,
  formatNumericValue,
  formatSmartDateTime,
  formatTooltipTime,
} from "../charts/foundation";

type VariableData = {
  timestamp: string | number;
  value: number | boolean | string;
};

type MonitorVariableChartProps = {
  data: VariableData[] | undefined;
  height?: number;
  disabled?: boolean;
};

const MonitorVariableChart = ({
  data,
  height = 200,
  disabled = false,
}: MonitorVariableChartProps) => {
  if (!data || data.length === 0) {
    return null;
  }

  // Check if all values are strings
  const isStringData = data.every((item) => typeof item.value === "string");

  if (isStringData) {
    return (
      <div
        className="flex w-full items-center justify-center bg-gray-50 rounded-lg"
        style={{ height: `${height}px` }}
      >
        <p className="text-gray-500">String data visualization not supported</p>
      </div>
    );
  }

  // Convert boolean values to numbers (0 or 1) for charting
  const chartData = data.map((item) => ({
    ...item,
    value: typeof item.value === "boolean" ? (item.value ? 1 : 0) : item.value,
  }));

  const isBooleanData = data.every((item) => typeof item.value === "boolean");

  const timeRangeMs =
    chartData.length > 1
      ? new Date(chartData[chartData.length - 1].timestamp).getTime() -
        new Date(chartData[0].timestamp).getTime()
      : 0;

  return (
    <div className="flex flex-col justify-center items-center gap-2 w-full">
      <ResponsiveContainer
        width="100%"
        height={height}
        className={disabled ? "opacity-50" : ""}
      >
        <LineChart data={chartData} margin={CHART_MARGINS.monitorVariable}>
          <XAxis
            dataKey="timestamp"
            stroke={AXIS_CONFIG.x.stroke}
            tick={{ fontSize: AXIS_CONFIG.x.fontSize }}
            tickFormatter={(timestamp) =>
              formatSmartDateTime(timestamp, timeRangeMs)
            }
            axisLine={AXIS_CONFIG.x.axisLine}
          />
          <YAxis
            stroke={AXIS_CONFIG.y.stroke}
            domain={isBooleanData ? [0, 1] : ["dataMin", "dataMax"]}
            tickFormatter={(value) => {
              if (isBooleanData) {
                return value === 1 ? "True" : "False";
              }
              return formatNumericValue(value);
            }}
            tickCount={2}
            fontSize={AXIS_CONFIG.y.fontSize}
          />
          <Tooltip
            formatter={(value) => {
              if (isBooleanData) {
                return value === 1 ? "True" : "False";
              }
              return formatNumericValue(value as number);
            }}
            labelFormatter={formatTooltipTime}
          />
          <Line
            type="monotone"
            dataKey="value"
            stroke={CHART_COLORS.primary}
            dot={CHART_TYPE_CONFIG.line.dot}
            strokeWidth={CHART_TYPE_CONFIG.line.strokeWidth}
          />
        </LineChart>
      </ResponsiveContainer>
      {disabled && (
        <div className="text-red50 text-xs font-normal">
          Variable graph is disabled while edits are being made
        </div>
      )}
    </div>
  );
};

export default MonitorVariableChart;
