import type { MonitorDataNonOptional } from "api/enterprise";
import {
  Compo<PERSON><PERSON><PERSON>,
  <PERSON>,
  ReferenceArea,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import type {
  MonitorDataState,
  MonitorState,
} from "../../api/enterprise/monitors";
import { ReactComponent as MissingDataPattern } from "../../images/patterns/missing-data-pattern.svg";
import { ReactComponent as NoDataPattern } from "../../images/patterns/no-data-pattern.svg";
import {
  AXIS_CONFIG,
  CHART_COLORS,
  CHART_FONTS,
  MONITOR_STATE_COLORS,
  formatTimeAxis,
  formatTooltipTime,
} from "../charts/foundation";

const MONITOR_STATE_Y_POSITIONS: Record<MonitorState, number> = {
  HEALTHY: 2,
  WARNING: 1,
  UNHEALTHY: 0,
};

type MonitorEvent = {
  time: number;
  monitorState: MonitorState;
  monitorDataState: MonitorDataState;
};

const MonitorAlertChart = ({
  monitorData,
}: {
  monitorData: MonitorDataNonOptional[];
}) => {
  // Transform monitor data into chart format
  const chartData = monitorData?.map((data) => ({
    time: new Date(data.monitorEvent.time).getTime(),
    monitorState: data.monitorEvent.monitorState,
    monitorDataState: data.monitorEvent.monitorDataState,
    y: MONITOR_STATE_Y_POSITIONS[data.monitorEvent.monitorState],
  }));

  // Find regions with NO_DATA or MISSING_DATA for background highlighting
  const dataStateRegions = chartData?.reduce(
    (regions, point, index) => {
      if (
        point.monitorDataState === "NO_DATA" ||
        point.monitorDataState === "MISSING_DATA"
      ) {
        if (
          index === 0 ||
          chartData[index - 1].monitorDataState !== point.monitorDataState
        ) {
          regions.push({
            start: point.time,
            end: point.time,
            type: point.monitorDataState,
          });
        }
        if (
          index === chartData.length - 1 ||
          chartData[index + 1].monitorDataState !== point.monitorDataState
        ) {
          regions[regions.length - 1].end = point.time;
        }
      }
      return regions;
    },
    [] as { start: number; end: number; type: MonitorDataState }[],
  );

  return (
    <div className="w-full h-[100px]">
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart margin={{ top: 0, right: 10, bottom: 0, left: 10 }}>
          <XAxis
            dataKey="time"
            type="number"
            domain={["dataMin", "dataMax"]}
            tickFormatter={(time: number) => formatTimeAxis(time)}
            name="Time"
            tickCount={6}
            padding={{ left: 10, right: 10 }}
            axisLine={AXIS_CONFIG.x.axisLine}
            fontSize={CHART_FONTS.tick}
          />
          <YAxis
            dataKey="y"
            type="number"
            domain={[0, 2]}
            ticks={[0, 1, 2]}
            tickLine={false}
            fontSize={CHART_FONTS.tick}
            tickFormatter={(value: number) =>
              Object.entries(MONITOR_STATE_Y_POSITIONS).find(
                ([_, pos]) => pos === value,
              )?.[0] ?? ""
            }
            name="State"
            padding={{ top: 10, bottom: 10 }}
            axisLine={AXIS_CONFIG.y.axisLine}
            interval={0}
          />
          <ZAxis
            dataKey="monitorDataState"
            name="Data State"
            type="number"
            range={[20, 100]}
          />

          <defs>
            <NoDataPattern />
            <MissingDataPattern />
          </defs>

          {/* Background highlighting for NO_DATA and MISSING_DATA regions */}
          {dataStateRegions?.map((region) => (
            <ReferenceArea
              key={`${region.start}-${region.end}-${region.type}`}
              x1={region.start}
              x2={region.end}
              y1={0}
              y2={2}
              fill={`url(#${region.type === "NO_DATA" ? "noDataPattern" : "missingDataPattern"})`}
              fillOpacity={0.3}
            />
          ))}

          <Tooltip
            formatter={(value: number | string, name: string) => {
              if (name === "time") {
                return formatTooltipTime(value as number);
              }
              return value;
            }}
            content={({ active, payload, label }) => {
              if (active && payload && payload.length) {
                const data = payload[0].payload as MonitorEvent;
                return (
                  <div className="bg-white p-2 border border-gray-200 rounded shadow-sm">
                    <p className="text-sm text-gray-600">
                      Time: {formatTooltipTime(data.time)}
                    </p>
                    <p className="text-sm text-gray-600">
                      State: {data.monitorState}
                    </p>
                    <p className="text-sm text-gray-600">
                      Data State: {data.monitorDataState}
                    </p>
                  </div>
                );
              }
              return null;
            }}
          />

          {/* Add a base line for the chart */}
          <Line
            key="base-line"
            data={chartData}
            dataKey="y"
            stroke={CHART_COLORS.stroke}
            strokeWidth={1}
            connectNulls={false}
            dot={false}
            activeDot={false}
          />

          {/* Plot points for each state */}
          {Object.entries(MONITOR_STATE_COLORS).map(([state, color]) => (
            <Scatter
              key={state}
              data={chartData?.filter((d) => d.monitorState === state)}
              name={state}
              fill={color}
              stroke="none"
              line={false}
            />
          ))}

          {/* Add lines connecting points, with dotted style for MISSING_DATA */}
          {Object.entries(MONITOR_STATE_COLORS).map(([state, color]) => (
            <Line
              key={`${state}-line`}
              data={chartData?.map((d) => ({
                time: d.time,
                y: d.monitorState === state ? d.y : null,
              }))}
              dataKey="y"
              stroke={color}
              strokeWidth={2}
              connectNulls={false}
              dot={false}
              activeDot={false}
            />
          ))}
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default MonitorAlertChart;
