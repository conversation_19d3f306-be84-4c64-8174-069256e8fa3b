const ALERT_STATE_COLORS = {
  UNHEALTHY: "#C55B57", // red50
  WARNING: "#E87D48", // orange50
  HEALTHY: "#6E9F66", // green50
};

export type ChartDataPoint = {
  timestamp: number;
  alertState: string;
  alertSeverity: string;
  alertMessage: string;
  conditionEvaluation: boolean;
};

type MonitorAlertStackedBarProps = {
  chartData: ChartDataPoint[];
  height?: number;
  disabled?: boolean;
};

const MonitorAlertStackedBar = ({
  chartData,
  height = 32,
  disabled = false,
}: MonitorAlertStackedBarProps) => {
  if (!chartData || chartData.length === 0) {
    return (
      <div
        className="w-full flex items-center justify-center text-gray-500"
        style={{ height: `${height}px` }}
      >
        No alert data available
      </div>
    );
  }

  // Calculate time range for positioning
  const timeRange = {
    min: Math.min(...chartData.map((d) => d.timestamp)),
    max: Math.max(...chartData.map((d) => d.timestamp)),
  };

  // Calculate segment widths
  const width = 100 / chartData.length;

  const allAlertStates = Array.from(
    new Set(
      chartData.map(
        (segment) => segment.alertState as keyof typeof ALERT_STATE_COLORS,
      ),
    ),
  );

  return (
    <div
      className={`w-full ${disabled ? "opacity-50 pointer-events-none" : ""}`}
    >
      {/* Time axis labels */}
      <div className="left-0 right-0 flex justify-between text-xs text-gray-500">
        <span>
          {timeRange.max - timeRange.min > 86400000
            ? new Date(timeRange.min).toLocaleDateString()
            : new Date(timeRange.min).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
        </span>
        <span>
          {timeRange.max - timeRange.min > 86400000
            ? new Date(timeRange.max).toLocaleDateString()
            : new Date(timeRange.max).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
        </span>
      </div>

      {/* Bars container */}
      <div className="w-full flex gap-[1px]" style={{ height: `${height}px` }}>
        {chartData.map((segment, index) => {
          const color =
            ALERT_STATE_COLORS[
              segment.alertState as keyof typeof ALERT_STATE_COLORS
            ] || "#d1d5db";

          return (
            <div
              key={`${segment.timestamp}-${index}`}
              className="h-full min-w-[5px] rounded-sm transition-all duration-200 hover:opacity-80 cursor-pointer group relative border"
              style={{
                width: `${width}%`,
                backgroundColor: segment.conditionEvaluation
                  ? color
                  : "transparent",
                borderColor: color,
              }}
              title={`Time: ${new Date(segment.timestamp).toLocaleString()}
State: ${segment.alertState}
Severity: ${segment.alertSeverity}
Message: ${segment.alertMessage}`}
            >
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block">
                <div className="bg-white p-2 border border-gray-200 rounded shadow-sm whitespace-nowrap">
                  <p className="text-sm text-gray-600">
                    Time: {new Date(segment.timestamp).toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600">
                    State: {segment.alertState}
                  </p>
                  <p className="text-sm text-gray-600">
                    Severity: {segment.alertSeverity}
                  </p>
                  <p className="text-sm text-gray-600">
                    Message: {segment.alertMessage}
                  </p>
                  <p className="text-sm text-gray-600">
                    Condition Evaluation:{" "}
                    {segment.conditionEvaluation?.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Alert state legend */}
      <div className="flex justify-center mt-2 gap-4">
        {allAlertStates.map((alertState) => (
          <div className="flex items-center gap-2 text-[10px]" key={alertState}>
            <div
              className="w-3 h-3 rounded-sm"
              style={{ backgroundColor: ALERT_STATE_COLORS[alertState] }}
            />
            <span>{alertState} condition met</span>
          </div>
        ))}
        <div className="flex items-center gap-2 text-[10px]">
          <div className="w-3 h-3 rounded-sm border border-gray-300" />
          <span>Alert condition not met</span>
        </div>
      </div>
    </div>
  );
};

export default MonitorAlertStackedBar;
