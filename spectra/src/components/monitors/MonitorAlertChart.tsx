import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import type { MonitorData } from "../../api/enterprise";
import { useSelectedTimeRange } from "../../context/SelectedTimeRangeContext";
import missingDataPattern, {
  ReactComponent as MissingDataPattern,
} from "../../images/patterns/missing-data-pattern.svg";
import noDataPattern, {
  ReactComponent as NoDataPattern,
} from "../../images/patterns/no-data-pattern.svg";
import { ChartErrorBoundary } from "../uikit/ErrorBoundary/ChartErrorBoundary";

type MonitorState = "UNHEALTHY" | "WARNING" | "HEALTHY";
type MonitorDataState = "MISSING_DATA" | "NO_DATA" | "COMPLETE_DATA";

const MONITOR_STATE_COLORS: Record<MonitorState, string> = {
  UNHEALTHY: "#C55B57", // red50
  WARNING: "#E87D48", // orange50
  HEALTHY: "#6E9F66", // green50
};

const MONITOR_DATA_STATE_COLORS: Record<MonitorDataState, string> = {
  MISSING_DATA: "#9CA3AF", // gray50
  NO_DATA: "#DDDDDD", // gray200
  COMPLETE_DATA: "transparent", // nothing
};

const MONITOR_STATE_Y_POSITIONS: Record<MonitorState, number> = {
  HEALTHY: 2,
  WARNING: 1,
  UNHEALTHY: 0,
};

type MonitorEvent = {
  time: number;
  monitorState: MonitorState;
  monitorDataState: MonitorDataState;
};

const MonitorAlertChartInner = ({
  monitorData,
}: {
  monitorData: MonitorData[];
}) => {
  const { start, end } = useSelectedTimeRange();

  // Early return for null or empty data
  if (!monitorData || monitorData.length === 0) {
    return (
      <div className="w-full h-[300px] flex items-center justify-center bg-gray-50 rounded-lg border border-gray-200">
        <div className="text-center">
          <p className="text-sm text-gray-600">No monitor data available</p>
        </div>
      </div>
    );
  }

  // Transform monitor data into chart format with null checks
  const chartData = monitorData
    ?.filter((data) => data != null && data.monitorEvent != null && data.monitorEvent.time != null)
    ?.map((data) => {
      try {
        const timeValue = new Date(data.monitorEvent.time).getTime();
        if (Number.isNaN(timeValue)) {
          console.warn("Invalid time value in monitor data:", data.monitorEvent.time);
          return null;
        }
        return {
          time: timeValue,
          monitorState: data.monitorEvent.monitorState,
          monitorDataState: data.monitorEvent.monitorDataState,
          y: MONITOR_STATE_Y_POSITIONS[data.monitorEvent.monitorState],
        };
      } catch (error) {
        console.warn("Error processing monitor data:", error, data);
        return null;
      }
    })
    ?.filter(Boolean); // Remove any null entries

  // Check if we have any valid data after filtering
  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full h-[300px] flex items-center justify-center bg-gray-50 rounded-lg border border-gray-200">
        <div className="text-center">
          <p className="text-sm text-gray-600">No valid monitor data for the selected time range</p>
        </div>
      </div>
    );
  }

  // Find regions with NO_DATA or MISSING_DATA for background highlighting
  const dataStateRegions = chartData?.reduce(
    (regions, point, index) => {
      // Additional null check for individual points
      if (!point || !chartData) return regions;

      if (
        point.monitorDataState === "NO_DATA" ||
        point.monitorDataState === "MISSING_DATA"
      ) {
        if (
          index === 0 ||
          !chartData[index - 1] ||
          chartData[index - 1].monitorDataState !== point.monitorDataState
        ) {
          regions.push({
            start: point.time,
            end: point.time,
            type: point.monitorDataState,
          });
        }
        if (
          index === chartData.length - 1 ||
          !chartData[index + 1] ||
          chartData[index + 1].monitorDataState !== point.monitorDataState
        ) {
          regions[regions.length - 1].end = point.time;
        }
      }
      return regions;
    },
    [] as { start: number; end: number; type: MonitorDataState }[],
  );

  return (
    <div className="w-full h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart margin={{ top: 0, right: 20, bottom: 20, left: 20 }}>
          <XAxis
            dataKey="time"
            type="number"
            domain={[start.valueOf(), end.valueOf()]}
            tickFormatter={(timestamp) => {
              const timeDiff = end.valueOf() - start.valueOf();
              const ONE_DAY = 24 * 60 * 60 * 1000;
              return timeDiff > ONE_DAY
                ? new Date(timestamp).toLocaleDateString()
                : new Date(timestamp).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  });
            }}
            name="Time"
            tickCount={6}
            padding={{ left: 10, right: 10 }}
          />
          <YAxis
            dataKey="y"
            type="number"
            domain={[0, 2]}
            ticks={[0, 1, 2]}
            tickFormatter={(value: number) =>
              Object.entries(MONITOR_STATE_Y_POSITIONS).find(
                ([_, pos]) => pos === value,
              )?.[0] ?? ""
            }
            name="State"
            padding={{ top: 20, bottom: 30 }}

          />
          <ZAxis
            dataKey="monitorDataState"
            name="Data State"
            type="number"
            range={[20, 100]}
          />

          <defs>
            <NoDataPattern />
            <MissingDataPattern />
          </defs>

          {/* Background highlighting for NO_DATA and MISSING_DATA regions */}
          {dataStateRegions?.map((region) => (
            <ReferenceArea
              key={`${region.start}-${region.end}-${region.type}`}
              x1={region.start}
              x2={region.end}
              y1={0}
              y2={2}
              fill={`url(#${region.type === "NO_DATA" ? "noDataPattern" : "missingDataPattern"})`}
              fillOpacity={0.3}
            />
          ))}

          <Legend
            verticalAlign="bottom"
            height={36}
            content={({ payload }) => (
              <div className="flex flex-wrap gap-4 justify-center">
                {payload
                  ?.filter((entry) => MONITOR_STATE_COLORS[entry.value])
                  .map((entry) => (
                    <div key={entry.value} className="flex items-center gap-2">
                      <div
                        className="w-3 h-3"
                        style={{ backgroundColor: entry.color }}
                      />
                      <span className="text-sm">{entry.value}</span>
                    </div>
                  ))}
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3"
                    style={{
                      backgroundImage: `url(${noDataPattern})`,
                      backgroundSize: "cover",
                    }}
                  />
                  <span className="text-sm">No Data</span>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3"
                    style={{
                      backgroundImage: `url(${missingDataPattern})`,
                      backgroundSize: "cover",
                    }}
                  />
                  <span className="text-sm">Missing Data</span>
                </div>
              </div>
            )}
          />

          <Tooltip
            formatter={(value: number | string, name: string) => {
              if (name === "time") {
                return new Date(value as number).toLocaleString();
              }
              return value;
            }}
            content={({ active, payload, label }) => {
              if (active && payload && payload.length) {
                const data = payload[0].payload as MonitorEvent;
                return (
                  <div className="bg-white p-2 border border-gray-200 rounded shadow-sm">
                    <p className="text-sm text-gray-600">
                      Time: {new Date(data.time).toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">
                      State: {data.monitorState}
                    </p>
                    <p className="text-sm text-gray-600">
                      Data State: {data.monitorDataState}
                    </p>
                  </div>
                );
              }
              return null;
            }}
          />

          {/* Add a base line for the chart */}
          <Line
            key="base-line"
            data={chartData}
            dataKey="y"
            stroke="#d1d5db"
            strokeWidth={1}
            connectNulls={false}
            dot={false}
            activeDot={false}
          />

          {/* Plot points for each state */}
          {Object.entries(MONITOR_STATE_COLORS).map(([state, color]) => (
            <Scatter
              key={state}
              data={chartData?.filter((d) => d != null && d.monitorState === state)}
              name={state}
              fill={color}
              stroke="none"
              line={false}
            />
          ))}

          {/* Add lines connecting points, with dotted style for MISSING_DATA */}
          {Object.entries(MONITOR_STATE_COLORS).map(([state, color]) => (
            <Line
              key={`${state}-line`}
              data={chartData
                ?.filter((d) => d != null)
                ?.map((d) => ({
                  time: d.time,
                  y: d.monitorState === state ? d.y : null,
                }))}
              dataKey="y"
              stroke={color}
              strokeWidth={2}
              connectNulls={false}
              dot={false}
              activeDot={false}
            />
          ))}
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

// Wrapped export with error boundary
const MonitorAlertChart = ({
  monitorData,
}: {
  monitorData: MonitorData[];
}) => (
  <ChartErrorBoundary chartName="Monitor Alert Chart">
    <MonitorAlertChartInner monitorData={monitorData} />
  </ChartErrorBoundary>
);

export default MonitorAlertChart;
