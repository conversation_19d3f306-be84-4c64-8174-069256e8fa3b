// TODO: this isnt exhaustive
export const typeToLabel = (type: string) => {
  switch (type) {
    case "net":
      return "Net";
    case "fwd":
      return "Forward";
    case "rev":
      return "Reverse";
    case "max":
      return "Maximum";
    case "gen":
      return "Generated";
    case "total":
      return "Total";
    case "grid":
      return "Grid Draw";
    case "soc":
      return "Capacity";
    case "soh":
      return "Health";
    case "batChargeCurrent":
      return "Current";
    case "batVoltage":
      return "Voltage";
    case "batTemp":
      return "Temperature";
    case "bmsMinCellTemp":
      return "Min Cell Temp";
    case "bmsMaxCellTemp":
      return "Max Cell Temp";
    case "batteryCapacity":
      return "Capacity";
    case "batteryDischarge":
      return "Discharge";
    case "batteryCharge":
      return "Charge Status";
    case "minVoltage":
      return "Min Voltage";
    case "maxVoltage":
      return "Max Voltage";
    default:
      return type;
  }
};
