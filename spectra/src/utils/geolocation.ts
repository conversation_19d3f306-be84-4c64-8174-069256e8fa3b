const degToRad = (deg: number) => deg * (Math.PI / 180);
const radToDeg = (rad: number) => rad * (180 / Math.PI);

export interface Coordinate {
  latitude: number;
  longitude: number;
}

export const US_CENTER_LOCATION: Coordinate = {
  latitude: 38.7946,
  longitude: -106.5348,
};

export const getCenterOfCoordinates = (coordinates: Coordinate[]) => {
  let x = 0;
  let y = 0;
  let z = 0;

  // convert from spherical to cartesian coordinates
  for (const coordinate of coordinates) {
    const latitude = degToRad(coordinate.latitude);
    const longitude = degToRad(coordinate.longitude);
    x += Math.cos(latitude) * Math.cos(longitude);
    y += Math.cos(latitude) * Math.sin(longitude);
    z += Math.sin(latitude);
  }

  const total = coordinates.length;
  const centerX = x / total;
  const centerY = y / total;
  const centerZ = z / total;

  const longitude = Math.atan2(centerY, centerX);
  const hypotenuse = Math.sqrt(centerX * centerX + centerY * centerY);
  const latitude = Math.atan2(centerZ, hypotenuse);

  return { latitude: radToDeg(latitude), longitude: radToDeg(longitude) };
};
