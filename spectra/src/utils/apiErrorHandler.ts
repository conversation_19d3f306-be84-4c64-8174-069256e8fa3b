/**
 * Enhanced API error handling utilities
 */

import { useErrorActions } from "../stores/errorStore";
import { createNetworkError, getUserFriendlyMessage } from "./errorUtils";

/**
 * Enhanced fetch wrapper with automatic error handling
 */
export const createApiErrorHandler = () => {
  const {
    addError,
    showNetworkError,
    showAuthError,
    showServerError,
    showNoAccessError,
  } = useErrorActions();

  /**
   * Wrapper for fetch with automatic error handling
   */
  const fetchWithErrorHandling = async (
    url: string,
    options: RequestInit = {},
    errorOptions: {
      showToast?: boolean;
      customErrorMessage?: string;
      retryable?: boolean;
      onRetry?: () => void;
    } = {},
  ): Promise<Response> => {
    const {
      showToast = true,
      customErrorMessage,
      retryable = false,
      onRetry,
    } = errorOptions;

    try {
      const response = await fetch(url, options);

      if (!response.ok) {
        if (showToast) {
          // Handle different types of HTTP errors
          switch (response.status) {
            case 401:
              showAuthError(customErrorMessage || "Authentication required");
              break;
            case 403:
              break;
            case 500:
            case 502:
            case 503:
              showServerError(
                response.status,
                customErrorMessage || "Server error occurred",
              );
              break;
            default:
              showNetworkError(response, url, options.method || "GET");
          }
        }

        // Still throw the error for the caller to handle if needed
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      // Handle network errors (no response)
      if (error instanceof TypeError && error.message.includes("fetch")) {
        if (showToast) {
          const networkError = createNetworkError(
            { status: 0, statusText: "Network Error", ok: false } as Response,
            url,
            options.method || "GET",
          );

          if (retryable && onRetry) {
            networkError.retryable = true;
            networkError.onRetry = onRetry;
          }

          addError(networkError);
        }
      }

      throw error;
    }
  };

  return { fetchWithErrorHandling };
};

/**
 * Hook for API error handling in components
 */
export const useApiErrorHandler = () => {
  const {
    addError,
    showNetworkError,
    showAuthError,
    showServerError,
    showValidationError,
    showNoAccessError,
  } = useErrorActions();

  /**
   * Handle API response errors
   */
  const handleApiError = async (
    error: unknown,
    context: {
      operation?: string;
      url?: string;
      method?: string;
      showToast?: boolean;
      customMessage?: string;
    } = {},
  ) => {
    const {
      operation = "API request",
      url,
      method,
      showToast = true,
      customMessage,
    } = context;

    if (!showToast) return;

    // Handle Response objects
    if (error instanceof Response) {
      showNetworkError(error, url, method);
      return;
    }

    // Handle fetch errors
    if (error instanceof TypeError && error.message.includes("fetch")) {
      const networkError = createNetworkError(
        { status: 0, statusText: "Network Error", ok: false } as Response,
        url,
        method,
      );
      addError(networkError);
      return;
    }

    // Handle errors with response property (axios-style)
    if (typeof error === "object" && error !== null && "response" in error) {
      // Define a more specific type for the error response
      interface ErrorResponse {
        status: number;
        data?: {
          errors?: unknown;
        };
        statusText?: string;
        [key: string]: unknown;
      }
      const response = (error as { response: ErrorResponse }).response;
      switch (response.status) {
        case 401:
          showAuthError(customMessage || "Authentication required");
          break;
        case 403:
          break;
        case 422:
          // Validation error
          showValidationError(
            customMessage || "Validation failed",
            undefined,
            response.data?.errors,
          );
          break;
        case 500:
        case 502:
        case 503:
          showServerError(
            response.status,
            customMessage || `${operation} failed`,
          );
          break;
        default:
          showNetworkError(response, url, method);
      }
      return;
    }

    // Handle generic errors
    const message = customMessage || getUserFriendlyMessage(error);
    addError({
      id: Date.now().toString(),
      title: `${operation} failed`,
      message,
      category: "unknown",
      severity: "medium",
      timestamp: new Date(),
      dismissible: true,
      autoHide: true,
      autoHideDelay: 5000,
    });
  };

  /**
   * Wrapper for async operations with error handling
   */
  const withErrorHandling = async <T>(
    operation: () => Promise<T>,
    context: {
      operationName?: string;
      showToast?: boolean;
      customErrorMessage?: string;
      onError?: (error: unknown) => void;
    } = {},
  ): Promise<T | null> => {
    const {
      operationName = "Operation",
      showToast = true,
      customErrorMessage,
      onError,
    } = context;

    try {
      return await operation();
    } catch (error) {
      await handleApiError(error, {
        operation: operationName,
        showToast,
        customMessage: customErrorMessage,
      });

      if (onError) {
        onError(error);
      }

      return null;
    }
  };

  return {
    handleApiError,
    withErrorHandling,
  };
};

/**
 * Retry mechanism for failed operations
 */
export const createRetryHandler = (
  maxRetries = 3,
  baseDelay = 1000,
  backoffMultiplier = 2,
) => {
  const retry = async <T>(
    operation: () => Promise<T>,
    retryCount = 0,
  ): Promise<T> => {
    try {
      return await operation();
    } catch (error) {
      if (retryCount >= maxRetries) {
        throw error;
      }

      const delay = baseDelay * backoffMultiplier ** retryCount;
      await new Promise((resolve) => setTimeout(resolve, delay));

      return retry(operation, retryCount + 1);
    }
  };

  return { retry };
};

/**
 * Global error handler for unhandled promise rejections
 */
export const setupGlobalErrorHandlers = () => {
  const { addError } = useErrorActions();

  // Handle unhandled promise rejections
  window.addEventListener("unhandledrejection", (event) => {
    console.error("Unhandled promise rejection:", event.reason);

    const error = createNetworkError(
      { status: 0, statusText: "Unhandled Error", ok: false } as Response,
      "unknown",
      "unknown",
    );
    error.title = "Unexpected Error";
    error.message = getUserFriendlyMessage(event.reason);

    addError(error);
  });

  // Handle global JavaScript errors
  window.addEventListener("error", (event) => {
    console.error("Global error:", event.error);

    const error = {
      id: Date.now().toString(),
      title: "JavaScript Error",
      message: event.error?.message || "An unexpected error occurred",
      category: "runtime" as const,
      severity: "high" as const,
      timestamp: new Date(),
      dismissible: true,
      autoHide: false,
      stack: event.error?.stack,
    };

    addError(error);
  });
};
