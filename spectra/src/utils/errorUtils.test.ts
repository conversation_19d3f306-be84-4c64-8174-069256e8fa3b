/**
 * Tests for error utilities to ensure UUID replacement works correctly
 */

import {
  createAuthenticationError,
  createError,
  createNetworkError,
  createOfflineError,
  createRuntimeError,
  createServerError,
  createValidationError,
} from "./errorUtils";

describe("Error Utilities", () => {
  describe("generateErrorId", () => {
    it("should generate unique error IDs", () => {
      const error1 = createError("Test 1", "Message 1", "client");
      const error2 = createError("Test 2", "Message 2", "client");

      expect(error1.id).toBeDefined();
      expect(error2.id).toBeDefined();
      expect(error1.id).not.toBe(error2.id);
      expect(error1.id).toMatch(/^error_[a-z0-9]+_[a-z0-9]+$/);
    });

    it("should generate IDs with consistent format", () => {
      const error = createError("Test", "Message", "client");
      expect(error.id).toMatch(/^error_[a-z0-9]+_[a-z0-9]+$/);
    });
  });

  describe("createError", () => {
    it("should create a basic error with all required properties", () => {
      const error = createError("Test Error", "Test message", "client");

      expect(error.id).toBeDefined();
      expect(error.title).toBe("Test Error");
      expect(error.message).toBe("Test message");
      expect(error.category).toBe("client");
      expect(error.severity).toBe("medium"); // default for client category
      expect(error.timestamp).toBeInstanceOf(Date);
      expect(error.dismissible).toBe(true);
      expect(error.autoHide).toBe(true);
      expect(error.autoHideDelay).toBe(5000);
    });

    it("should accept custom options", () => {
      const error = createError("Test Error", "Test message", "client", {
        severity: "high",
        dismissible: false,
        autoHide: false,
      });

      expect(error.severity).toBe("high");
      expect(error.dismissible).toBe(false);
      expect(error.autoHide).toBe(false);
    });
  });

  describe("createNetworkError", () => {
    it("should create a network error from HTTP response", () => {
      const mockResponse = {
        status: 500,
        statusText: "Internal Server Error",
        ok: false,
      } as Response;

      const error = createNetworkError(mockResponse, "/api/test", "GET");

      expect(error.category).toBe("server");
      expect(error.title).toBe("Server Error");
      expect(error.statusCode).toBe(500);
      expect(error.url).toBe("/api/test");
      expect(error.method).toBe("GET");
      expect(error.retryable).toBe(true);
    });

    it("should handle 401 authentication errors", () => {
      const mockResponse = {
        status: 401,
        statusText: "Unauthorized",
        ok: false,
      } as Response;

      const error = createNetworkError(mockResponse);

      expect(error.category).toBe("authentication");
      expect(error.title).toBe("Authentication Required");
    });
  });

  describe("createAuthenticationError", () => {
    it("should create an authentication error", () => {
      const error = createAuthenticationError();

      expect(error.category).toBe("authentication");
      expect(error.title).toBe("Authentication Required");
      expect(error.severity).toBe("high");
      expect(error.dismissible).toBe(false);
      expect(error.redirectToLogin).toBe(true);
    });

    it("should accept custom message and redirect option", () => {
      const error = createAuthenticationError("Custom message", false);

      expect(error.message).toBe("Custom message");
      expect(error.redirectToLogin).toBe(false);
    });
  });

  describe("createValidationError", () => {
    it("should create a validation error", () => {
      const error = createValidationError();

      expect(error.category).toBe("validation");
      expect(error.title).toBe("Validation Error");
    });

    it("should accept field and fieldErrors", () => {
      const fieldErrors = {
        email: ["Email is required"],
        password: ["Password too short"],
      };

      const error = createValidationError("Form invalid", "email", fieldErrors);

      expect(error.field).toBe("email");
      expect(error.fieldErrors).toEqual(fieldErrors);
    });
  });

  describe("createServerError", () => {
    it("should create a server error", () => {
      const error = createServerError(500);

      expect(error.category).toBe("server");
      expect(error.title).toBe("Server Error");
      expect(error.statusCode).toBe(500);
      expect(error.retryable).toBe(true);
    });

    it("should accept custom message and error code", () => {
      const error = createServerError(500, "Custom server error", "ERR_001");

      expect(error.message).toBe("Custom server error");
      expect(error.errorCode).toBe("ERR_001");
    });
  });

  describe("createRuntimeError", () => {
    it("should create a runtime error from JavaScript Error", () => {
      const jsError = new Error("Test runtime error");
      jsError.stack = "Error stack trace";

      const error = createRuntimeError(jsError, "Component stack");

      expect(error.category).toBe("runtime");
      expect(error.title).toBe("Runtime Error");
      expect(error.message).toBe("Test runtime error");
      expect(error.stack).toBe("Error stack trace");
      expect(error.componentStack).toBe("Component stack");
      expect(error.severity).toBe("critical");
    });
  });

  describe("createOfflineError", () => {
    it("should create an offline error", () => {
      const error = createOfflineError();

      expect(error.category).toBe("network");
      expect(error.title).toBe("Connection Lost");
      expect(error.severity).toBe("high");
      expect(error.autoHide).toBe(false);
    });
  });
});
