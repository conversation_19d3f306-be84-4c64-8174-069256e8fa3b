/**
 * Global Error Testing Utility
 *
 * This utility provides functions to manually trigger global error events
 * for testing the centralized error management system.
 */

/**
 * Test scenarios for unhandled promise rejections
 * These will trigger the 'unhandledrejection' event listener
 */
export const testUnhandledPromiseRejections = {
  /**
   * 1. Basic unhandled promise rejection
   * Creates a promise that rejects without a .catch() handler
   */
  basicRejection: () => {
    console.log("🧪 Testing: Basic unhandled promise rejection");

    // This will trigger unhandledrejection event
    Promise.reject(new Error("Test unhandled promise rejection"));

    console.log("✅ Triggered: Promise.reject() without .catch()");
  },

  /**
   * 2. Async function that throws without try-catch
   * Creates an async function that throws an error
   */
  asyncFunctionError: () => {
    console.log("🧪 Testing: Async function unhandled error");

    const asyncFunction = async () => {
      throw new Error("Async function error - unhandled");
    };

    // Call without await or .catch() - triggers unhandledrejection
    asyncFunction();

    console.log("✅ Triggered: Async function error without .catch()");
  },

  /**
   * 3. Fetch request without error handling
   * Makes a request to a non-existent endpoint
   */
  fetchWithoutCatch: () => {
    console.log("🧪 Testing: Fetch without error handling");

    // This will fail and trigger unhandledrejection if no .catch()
    fetch("/non-existent-endpoint-12345");

    console.log("✅ Triggered: fetch() without .catch()");
  },

  /**
   * 4. Promise chain that rejects
   * Creates a promise chain where one step rejects
   */
  promiseChainRejection: () => {
    console.log("🧪 Testing: Promise chain rejection");

    Promise.resolve("initial value")
      .then(() => {
        throw new Error("Promise chain rejection test");
      })
      .then(() => {
        // This won't execute
        console.log("This should not execute");
      });
    // No .catch() - will trigger unhandledrejection

    console.log("✅ Triggered: Promise chain rejection without .catch()");
  },

  /**
   * 5. setTimeout with promise rejection
   * Uses setTimeout to create a delayed unhandled rejection
   */
  delayedRejection: () => {
    console.log("🧪 Testing: Delayed promise rejection");

    setTimeout(() => {
      Promise.reject(new Error("Delayed unhandled rejection"));
    }, 1000);

    console.log(
      "✅ Triggered: Delayed promise rejection (will fire in 1 second)",
    );
  },

  /**
   * 6. JSON parsing error in promise
   * Creates a promise that fails during JSON parsing
   */
  jsonParsingError: () => {
    console.log("🧪 Testing: JSON parsing error in promise");

    Promise.resolve("invalid json {").then((data) => JSON.parse(data)); // This will throw
    // No .catch() - will trigger unhandledrejection

    console.log("✅ Triggered: JSON parsing error without .catch()");
  },
};

/**
 * Test scenarios for global JavaScript errors
 * These will trigger the 'error' event listener
 */
export const testGlobalJavaScriptErrors = {
  /**
   * 1. Access undefined property
   * Tries to access a property on undefined/null
   */
  undefinedPropertyAccess: () => {
    console.log("🧪 Testing: Undefined property access");

    setTimeout(() => {
      // This will throw a TypeError
      (null as unknown as { someProperty: { anotherProperty: unknown } })
        .someProperty.anotherProperty;
    }, 100);

    console.log(
      "✅ Triggered: Undefined property access (will fire in `100ms)",
    );
  },

  /**
   * 2. Call undefined function
   * Tries to call a function that doesn't exist
   */
  undefinedFunctionCall: () => {
    console.log("🧪 Testing: Undefined function call");

    setTimeout(() => {
      // This will throw a TypeError
      (
        (window as unknown as Record<string, unknown>).nonExistentFunction as (
          ...args: unknown[]
        ) => unknown
      )();
    }, 100);

    console.log("✅ Triggered: Undefined function call (will fire in 100ms)");
  },

  /**
   * 3. Invalid array access
   * Tries to call array methods on non-arrays
   */
  invalidArrayAccess: () => {
    console.log("🧪 Testing: Invalid array access");

    setTimeout(() => {
      // This will throw a TypeError
      (null as unknown as never[]).map(() => {});
    }, 100);

    console.log("✅ Triggered: Invalid array access (will fire in 100ms)");
  },

  /**
   * 4. Reference error
   * References a variable that doesn't exist
   */
  referenceError: () => {
    console.log("🧪 Testing: Reference error");

    setTimeout(() => {
      // This will throw a ReferenceError
      const undefinedVar = (window as unknown as { [key: string]: unknown })
        .undefinedVariable as unknown;
      // Attempt to access property (will throw if undefinedVar is undefined)
      // @ts-expect-error: This is intentional for error testing
      console.log(undefinedVar.property);
    }, 100);

    console.log("✅ Triggered: Reference error (will fire in 100ms)");
  },

  /**
   * 5. Type error with string methods
   * Tries to call string methods on non-strings
   */
  stringMethodError: () => {
    console.log("🧪 Testing: String method error");

    setTimeout(() => {
      // This will throw a TypeError
      (null as unknown as string).toUpperCase();
    }, 100);

    console.log("✅ Triggered: String method error (will fire in 100ms)");
  },

  /**
   * 6. Eval syntax error
   * Uses eval with invalid JavaScript syntax
   */
  evalSyntaxError: () => {
    console.log("🧪 Testing: Eval syntax error");

    setTimeout(() => {
      // This will throw a SyntaxError without using eval
      new Function("invalid javascript syntax {{{");
    }, 100);

    console.log("✅ Triggered: Eval syntax error (will fire in 100ms)");
  },
};

// Define the types for your test objects
type TestFunction = () => void;
type TestMap = Record<string, TestFunction>;

/**
 * Utility functions for testing
 */
export const errorTestUtils = {
  /**
   * Run all unhandled promise rejection tests
   */
  runAllPromiseTests: () => {
    console.log("🚀 Running all unhandled promise rejection tests...\n");

    Object.entries(testUnhandledPromiseRejections).forEach(
      ([name, test], index) => {
        setTimeout(() => {
          console.log(`\n--- Test ${index + 1}: ${name} ---`);
          test();
        }, index * 2000); // Stagger tests by 2 seconds
      },
    );
  },

  /**
   * Run all global JavaScript error tests
   */
  runAllJavaScriptTests: () => {
    console.log("🚀 Running all global JavaScript error tests...\n");

    Object.entries(testGlobalJavaScriptErrors).forEach(
      ([name, test], index) => {
        setTimeout(() => {
          console.log(`\n--- Test ${index + 1}: ${name} ---`);
          test();
        }, index * 2000); // Stagger tests by 2 seconds
      },
    );
  },

  /**
   * Run a single test by name
   */
  runSingleTest: (category: "promise" | "javascript", testName: string) => {
    const tests: TestMap =
      category === "promise"
        ? testUnhandledPromiseRejections
        : testGlobalJavaScriptErrors;

    const test = tests[testName];

    if (typeof test === "function") {
      console.log(`🎯 Running single test: ${category} - ${testName}`);
      test();
    } else {
      console.error(`❌ Test not found: ${category} - ${testName}`);
      console.log("Available tests:", Object.keys(tests));
    }
  },

  /**
   * Clear all errors from the error store
   */
  clearAllErrors: () => {
    // This will be implemented when integrated with the error store
    console.log("🧹 Clearing all errors...");
    // useErrorActions().clearErrors();
  },
};

/**
 * Console commands for easy testing
 * Copy and paste these into the browser console
 */
export const consoleCommands = {
  // Basic tests
  testPromiseRejection: `
// Test unhandled promise rejection
Promise.reject(new Error('Console test: Unhandled promise rejection'));
console.log('✅ Triggered unhandled promise rejection');
  `,

  testJavaScriptError: `
// Test global JavaScript error
setTimeout(() => {
  (null).someProperty;
}, 100);
console.log('✅ Triggered JavaScript error (will fire in 100ms)');
  `,

  // Advanced tests
  testAsyncError: `
// Test async function error
(async () => {
  throw new Error('Console test: Async function error');
})();
console.log('✅ Triggered async function error');
  `,

  testFetchError: `
// Test fetch error
fetch('/non-existent-endpoint-console-test');
console.log('✅ Triggered fetch error');
  `,
};
