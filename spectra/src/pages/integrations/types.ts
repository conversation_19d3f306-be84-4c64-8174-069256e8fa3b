export type ResourceMetadata = {
  id: string;
  key: string;
  value: string;
};

export type Resource = {
  id: string;
  path: string;
  metadata: ResourceMetadata[];
};

export type Property = {
  key: string;
  values: string[];
};

export type Metadata = {
  id: string;
  key: string;
  value: string;
};

export type ResourceMapping = {
  resourceId: string;
  resourceThirdPartyId: string;
  thirdPartyId: string;
  resourceBaseType: string;
  resourceType: string;
  metaData: Record<string, string>;
};

export type IntegrationTypeConfig = {
  name: string;
  description: string;
};

export type IntegrationTypesResponse = {
  [direction: string]: {
    [type: string]: IntegrationTypeConfig[];
  };
};
