import {
  EgressIntegrationType,
  IngressIntegrationType,
  IntegrationDirection,
} from "api/data/integrations";
import { useAuth } from "context/AuthContext";
import { useCallback, useEffect, useState } from "react";
import {
  type IntegrationResponse,
  useIntegrationsApi,
} from "../../api/ingestion/integrations";
import CreateIntegrationModal from "../../components/integrations/CreateIntegrationModal";
import IntegrationCard from "../../components/integrations/IntegrationCard";
import IntegrationFilters from "../../components/integrations/IntegrationFilters";
import QuickAddCard from "../../components/integrations/QuickAddCard";
import QuickAddSection from "../../components/integrations/QuickAddSection";
import ButtonComponent from "../../components/uikit/button";

const IntegrationsPage = () => {
  const [integrations, setIntegrations] = useState<IntegrationResponse[]>([]);
  const [filteredIntegrations, setFilteredIntegrations] = useState<
    IntegrationResponse[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const [preSelectedDirection, setPreSelectedDirection] =
    useState<IntegrationDirection>(IntegrationDirection.INGRESS);
  const [preSelectedType, setPreSelectedType] = useState<
    IngressIntegrationType | EgressIntegrationType
  >(IngressIntegrationType.UtilityAPI);

  const { user } = useAuth();
  const { getIntegrations } = useIntegrationsApi();

  const fetchIntegrations = useCallback(async () => {
    try {
      const fetchedIntegrations = await getIntegrations();
      setIntegrations(fetchedIntegrations);
      setFilteredIntegrations(fetchedIntegrations);
      setIsLoading(false);
    } catch (err) {
      setError(err as Error);
      setIsLoading(false);
    }
  }, [getIntegrations]);

  const handleQuickAddClick = (
    direction: IntegrationDirection,
    type: IngressIntegrationType | EgressIntegrationType,
  ) => {
    setPreSelectedDirection(direction);
    setPreSelectedType(type);
    setIsCreateModalOpen(true);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchIntegrations is not a dependency
  useEffect(() => {
    if (user.partnerId) {
      fetchIntegrations();
    }
  }, [user.partnerId]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  const ingressIntegrations = filteredIntegrations.filter(
    (i) => i.direction === "INGRESS",
  );
  const egressIntegrations = filteredIntegrations.filter(
    (i) => i.direction === "EGRESS",
  );

  return (
    <div className="px-4">
      <div className="flex justify-between items-center pt-4 pb-5">
        <div className="text-heading1 text-space50">Integrations</div>
        <ButtonComponent.Pill
          buttonStyle="primary"
          onClick={() => setIsCreateModalOpen(true)}
        >
          + Add New Integration
        </ButtonComponent.Pill>
      </div>

      <hr className="border-gray95" />

      <QuickAddSection>
        <QuickAddCard
          title="UtilityAPI"
          description="Connect to UtilityAPI for energy data"
          direction={IntegrationDirection.INGRESS}
          type={IngressIntegrationType.UtilityAPI}
          onClick={handleQuickAddClick}
        />
        <QuickAddCard
          title="Weather"
          description="Integrate weather data for environmental monitoring"
          direction={IntegrationDirection.INGRESS}
          type={IngressIntegrationType.Weather}
          onClick={handleQuickAddClick}
        />
        <QuickAddCard
          title="Webhook"
          description="Send real-time data to external systems via HTTP webhooks"
          direction={IntegrationDirection.EGRESS}
          type={EgressIntegrationType.WEBHOOK}
          onClick={handleQuickAddClick}
        />
        <QuickAddCard
          title="Email Notifications"
          description="Send alerts and notifications via email"
          direction={IntegrationDirection.EGRESS}
          type={EgressIntegrationType.EMAIL}
          onClick={handleQuickAddClick}
        />
        <QuickAddCard
          title="SMS Alerts"
          description="Send urgent notifications via SMS"
          direction={IntegrationDirection.EGRESS}
          type={EgressIntegrationType.SMS}
          onClick={handleQuickAddClick}
        />
        <QuickAddCard
          title="Phone Calls"
          description="Make automated phone calls for critical alerts"
          direction={IntegrationDirection.EGRESS}
          type={EgressIntegrationType.PHONE}
          onClick={handleQuickAddClick}
        />
      </QuickAddSection>

      <IntegrationFilters
        integrations={integrations}
        onFilterChange={setFilteredIntegrations}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <div className="text-heading2 text-space50 mb-4">Ingress</div>
          {ingressIntegrations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No ingress integrations found
            </div>
          ) : (
            <div>
              {ingressIntegrations.map((integration) => (
                <IntegrationCard
                  key={integration.integrationId}
                  integration={integration}
                />
              ))}
            </div>
          )}
        </div>

        <div>
          <div className="text-heading2 text-space50 mb-4">Egress</div>
          {egressIntegrations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No egress integrations found
            </div>
          ) : (
            <div className="space-y-4">
              {egressIntegrations.map((integration) => (
                <IntegrationCard
                  key={integration.integrationId}
                  integration={integration}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      <CreateIntegrationModal
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreated={fetchIntegrations}
        preSelectedDirection={preSelectedDirection}
        preSelectedType={preSelectedType}
      />
    </div>
  );
};

export { IntegrationsPage };
