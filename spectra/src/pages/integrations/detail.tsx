import {
  type IntegrationAction,
  IntegrationDirection,
  type IntegrationPropertiesResponse,
  useIntegrationsDataAPI,
} from "api/data/integrations";
import { IntegrationActionModal } from "components/integrations/IntegrationActionModal";
import { IntegrationPropertiesForm } from "components/integrations/IntegrationPropertiesForm";
import { useAuth } from "context/AuthContext";
import { ReactComponent as DeleteIcon } from "images/icons/delete.svg";
import { ReactComponent as PencilSquareLinedIcon } from "images/icons/pencil-square-lined.svg";
import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import {
  type IntegrationResponse,
  useIntegrationsApi,
} from "../../api/ingestion/integrations";
import { useThingsApi } from "../../api/ingestion/things";
import {
  ContentLayout,
  <PERSON><PERSON>,
  HeaderDivider,
  HeaderTitle,
  PageContent,
} from "../../components/pageLayout";
import ButtonComponent from "../../components/uikit/button";
import Modal from "../../components/uikit/modal";
import SelectField from "../../components/uikit/selectField";
import TextField from "../../components/uikit/textField";
import type { ResourceMapping } from "./types";

// TODO: this should be moved to a shared type file
type ResourceType = "site" | "fleet" | string; // this can be a thing type if ResourceBaseType is thing
type ResourceBaseType = "place" | "thing";

const IntegrationDetailPage = () => {
  const { integrationId } = useParams();
  const { user } = useAuth();
  const {
    getIntegrationDetail,
    updateIntegration,
    deleteIntegration,
    deleteResourceMapping,
    createResourceMapping,
    getResourceMappings,
  } = useIntegrationsApi();
  const { getThingTypes } = useThingsApi();
  const { getIntegrationActions, getIntegrationProperties } =
    useIntegrationsDataAPI();

  const [integration, setIntegration] = useState<IntegrationResponse | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [deleteError, setDeleteError] = useState<Error | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isResourceModalOpen, setIsResourceModalOpen] = useState(false);
  const [editingResource, setEditingResource] =
    useState<ResourceMapping | null>(null);
  const [resourceMetadata, setResourceMetadata] = useState<
    Record<string, string>
  >({});
  const [resourceError, setResourceError] = useState<string | null>(null);
  const [editName, setEditName] = useState("");
  const [editProperties, setEditProperties] = useState<
    Record<string, string[]>
  >({});
  const [thirdPartyId, setThirdPartyId] = useState("");
  const [resourceThirdPartyId, setResourceThirdPartyId] = useState("");
  const [isResourceSaving, setIsResourceSaving] = useState(false);
  const [resourceMappings, setResourceMappings] = useState<ResourceMapping[]>(
    [],
  );
  const [resourceId, setResourceId] = useState("");

  const [resourceBaseType, setResourceBaseType] =
    useState<ResourceBaseType>("place");
  const [resourceType, setResourceType] = useState<ResourceType>("site");
  const [thingTypes, setThingTypes] = useState<string[]>([]);

  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [availableActions, setAvailableActions] = useState<IntegrationAction[]>(
    [],
  );
  const [integrationProperties, setIntegrationProperties] =
    useState<IntegrationPropertiesResponse | null>(null);
  const [selectedAction, setSelectedAction] =
    useState<IntegrationAction | null>(null);

  const properties =
    integrationProperties?.[
      (integration?.direction as IntegrationDirection) ||
        IntegrationDirection.INGRESS
    ]?.[integration?.integrationType || ""];
  const isEditable = (property: string) =>
    properties?.find((p) => p.name === property)?.isEditable;

  const handlePropertiesChange = (
    newProperties: Array<{ key: string; values: string[] }>,
  ) => {
    const updatedProperties = newProperties.reduce(
      (acc, prop) => {
        acc[prop.key] = prop.values;
        return acc;
      },
      {} as Record<string, string[]>,
    );
    setEditProperties(updatedProperties);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: getIntegrationDetail is not a dependency
  const fetchIntegration = useCallback(async () => {
    try {
      if (!integrationId) return;
      const data = await getIntegrationDetail(integrationId);
      setIntegration(data);
      setIsLoading(false);
    } catch (err) {
      setError(err as Error);
      setIsLoading(false);
    }
  }, [integrationId]);

  const fetchResourceMappings = async () => {
    if (!integrationId) return;
    try {
      const data = await getResourceMappings(integrationId);
      setResourceMappings(data);
    } catch (err) {
      setResourceMappings([]);
    }
  };

  const fetchIntegrationProperties = async () => {
    const data = await getIntegrationProperties();
    setIntegrationProperties(data);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchResourceMappings is not a dependency
  useEffect(() => {
    fetchIntegration();
    fetchResourceMappings();
    fetchIntegrationProperties();
  }, [fetchIntegration]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchThingTypes is not a dependency
  useEffect(() => {
    const fetchThingTypes = async () => {
      try {
        const types = await getThingTypes();
        setThingTypes(types);
      } catch (err) {
        console.error("Error fetching thing types:", err);
      }
    };
    fetchThingTypes();
  }, []);

  useEffect(() => {
    if (integration) {
      setEditName(integration.integrationName);
      setEditProperties({ ...integration.properties });
    }
  }, [integration]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchActions is not a dependency
  useEffect(() => {
    const fetchActions = async () => {
      try {
        const actions = await getIntegrationActions();
        const directionActions =
          actions[
            (integration?.direction as IntegrationDirection) ||
              IntegrationDirection.INGRESS
          ];
        const typeActions =
          directionActions?.[integration?.integrationType || ""] || [];
        setAvailableActions(typeActions);
      } catch (err) {
        console.error("Error fetching integration actions:", err);
      }
    };
    if (integration) {
      fetchActions();
    }
  }, [integration]);

  const handleDelete = async () => {
    if (!integration) return;
    setIsDeleting(true);
    try {
      await deleteIntegration(integration.integrationId);
      window.location.href = "/integrations";
    } catch (err) {
      setDeleteError(err as Error);
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  };

  const handleOpenAddResource = () => {
    setEditingResource(null);
    setResourceId("");
    setResourceMetadata({});
    setThirdPartyId("");
    setResourceThirdPartyId("");
    setResourceError(null);
    setResourceBaseType("place");
    setResourceType("site");
    setIsResourceModalOpen(true);
  };

  const handleOpenEditResource = (resource: ResourceMapping) => {
    setEditingResource(resource);
    setResourceId(resource.resourceId || "");
    setResourceMetadata(resource.metaData || {});
    setThirdPartyId(resource.thirdPartyId || "");
    setResourceThirdPartyId(resource.resourceThirdPartyId || "");
    setResourceError(null);

    setResourceBaseType(resource.resourceBaseType as ResourceBaseType);
    setResourceType(resource.resourceType as ResourceType);

    setIsResourceModalOpen(true);
  };

  const handleResourceBaseTypeChange = (
    newResourceBaseType: ResourceBaseType,
  ) => {
    setResourceBaseType(newResourceBaseType);
    if (newResourceBaseType === "place") {
      setResourceType("site" as ResourceType);
    } else {
      // TODO: this should be a dropdown with the thing types
      setResourceType(
        thingTypes.length > 0 ? (thingTypes[0] as ResourceType) : "",
      );
    }
  };

  const handleSaveResource = async () => {
    if (!integration) return;

    if (!resourceBaseType) {
      setResourceError("Resource base type is required");
      return;
    }
    if (!resourceType) {
      setResourceError("Resource type is required");
      return;
    }
    if (!resourceId) {
      setResourceError("Resource ID is required");
      return;
    }
    if (!thirdPartyId) {
      setResourceError("Third Party Id is required");
      return;
    }
    if (!resourceThirdPartyId) {
      setResourceError("Resource Third Party Id is required");
      return;
    }

    setIsResourceSaving(true);

    if (editingResource) {
      await deleteResourceMapping(
        integration.integrationId,
        editingResource.resourceId,
      );
    }
    const result = await createResourceMapping({
      integrationId: integration.integrationId,
      direction: integration.direction,
      integrationType: integration.integrationType,
      thirdPartyId: thirdPartyId,
      resourceThirdPartyId: resourceThirdPartyId,
      resourceBaseType: resourceBaseType,
      resourceType: resourceType,
      resourceId: resourceId,
      metaData: resourceMetadata,
    });
    setIsResourceSaving(false);
    if (result.success) {
      setIsResourceModalOpen(false);
      setEditingResource(null);
      setResourceId("");
      setResourceMetadata({});
      setThirdPartyId("");
      setResourceThirdPartyId("");
      setResourceError(null);
      setResourceBaseType("place");
      setResourceType("site");
      fetchResourceMappings();
    } else {
      if (result.error?.errors?.length > 0) {
        setResourceError(result.error.errors[0].message);
      } else if (result.error?.message) {
        setResourceError(result.error.message);
      } else {
        setResourceError("Failed to update resources");
      }
    }
  };

  const handleDeleteResource = async (resource: ResourceMapping) => {
    if (!integration) return;
    const result = await deleteResourceMapping(
      integration.integrationId,
      resource.resourceId,
    );
    if (result.success) {
      fetchResourceMappings();
    } else {
      alert(result.error?.message || "Failed to delete resource");
    }
  };

  const isDirty =
    editName !== integration?.integrationName ||
    JSON.stringify(editProperties) !== JSON.stringify(integration?.properties);

  const handleCancelEdit = () => {
    setEditName(integration?.integrationName || "");
    setEditProperties({ ...(integration?.properties || {}) });
  };

  const handleSaveEdit = async () => {
    if (!integration) return;
    setIsSaving(true);
    try {
      // exclude ineditable properties
      const editableProperties = Object.entries(editProperties).filter(
        ([property, values]) => isEditable(property),
      );
      const result = await updateIntegration(integration.integrationId, {
        integrationName: editName,
        properties: Object.fromEntries(editableProperties),
      });
      if (result.success) {
        setSaveError(null);
        await fetchIntegration();
      } else {
        setSaveError(result.message);
      }
    } catch (err) {
      setSaveError(err.message);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error || !integration) {
    return <div>Error: {error?.message || "Integration not found"}</div>;
  }

  return (
    <ContentLayout>
      <Header>
        <HeaderTitle
          category="Integrations"
          categoryLink="/integrations"
          pageTitle={integration.integrationName}
        >
          {isDirty && (
            <div className="flex gap-2">
              <ButtonComponent.Pill
                variant="filled"
                buttonStyle="primary"
                onClick={handleSaveEdit}
                disabled={isSaving}
              >
                Save Changes
              </ButtonComponent.Pill>
              <ButtonComponent.Pill
                variant="outline"
                buttonStyle="default"
                onClick={handleCancelEdit}
              >
                Discard Changes
              </ButtonComponent.Pill>
            </div>
          )}
        </HeaderTitle>
      </Header>
      <HeaderDivider />
      <PageContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-4">
          <div>
            <div className="text-heading2 text-space50 mb-4">
              Integration Details
            </div>
            <div className="space-y-4">
              <TextField
                label="Integration Name"
                value={editName}
                onChange={setEditName}
              />
              <SelectField
                label="Integration Type"
                value={integration.integrationType}
                options={[
                  {
                    value: integration.integrationType,
                    label: integration.integrationType,
                  },
                ]}
                disabled={true}
              />
              <SelectField
                label="Direction"
                value={integration.direction}
                options={[
                  {
                    value: integration.direction,
                    label: integration.direction,
                  },
                ]}
                disabled={true}
              />

              {properties && (
                <IntegrationPropertiesForm
                  typeProperties={properties.map((prop) => ({
                    ...prop,
                    isEditable: isEditable(prop.name),
                  }))}
                  properties={Object.entries(editProperties).map(
                    ([key, values]) => ({
                      key,
                      values,
                    }),
                  )}
                  setProperties={handlePropertiesChange}
                  selectedType={integration.integrationType}
                  selectedDirection={integration.direction}
                />
              )}

              <div className="space-y-3">
                <div className="text-heading2 text-space50">
                  Associated Resource Paths
                </div>
                {integration.resources?.map((resource) => (
                  <div
                    key={resource.path}
                    className="p-4 mb-4 rounded-md shadow border border-zinc-300 transition-transform"
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex flex-col gap-2">
                        <div className="flex flex-col gap-1">
                          <div className="text-space70 text-[10px] font-normal">
                            Resource Path
                          </div>
                          <div className="text-black text-xs font-normal font-mono">
                            {resource.path}
                          </div>
                        </div>
                        {Object.entries(resource.metadata || {}).length > 0 && (
                          <div className="flex flex-col gap-1">
                            <div className="text-space70 text-[10px] font-normal">
                              Metadata
                            </div>
                            <div className="text-black text-xs font-normal">
                              {Object.entries(resource.metadata || {}).map(
                                ([key, value]) => (
                                  <div key={key} className="flex gap-2">
                                    <span className="font-medium">{key}:</span>
                                    <span>{String(value)}</span>
                                  </div>
                                ),
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {(!integration.resources ||
                  integration.resources.length === 0) && (
                  <div className="text-center py-8 text-gray-500">
                    No resource paths configured
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="text-heading2 text-space50">Resource Mappings</div>
            <div className="self-stretch justify-start text-space70 text-[10px] font-medium tracking-wide">
              To set up an ingress integration with an external provider, you
              may need to create explicit mappings between external resources
              and Things or Places on the Spectra platform. Create Resource
              mappings with a resource using its base type, resource type, and
              its id, and provide the external provider id and its id for your
              resource (ex. Meter) under the resource third party id.
            </div>
            <div className="space-y-4">
              {resourceMappings.map((resource) => (
                <div
                  key={resource.resourceId}
                  className="p-4 mb-4 rounded-md shadow border border-zinc-300 transition-transform hover:-translate-y-0.5 hover:shadow-md"
                >
                  <div className="flex justify-between items-center">
                    <div className="flex flex-col gap-2">
                      <div className="flex flex-row gap-3">
                        <div className="flex flex-col gap-1">
                          <div className="text-space70 text-[10px] font-normal">
                            Resource Type
                          </div>
                          <div className="text-black text-xs font-normal font-mono">
                            {resource.resourceBaseType}/{resource.resourceType}
                          </div>
                        </div>
                        <div className="flex flex-col gap-1">
                          <div className="text-space70 text-[10px] font-normal">
                            Resource ID
                          </div>
                          <div className="text-black text-xs font-normal font-mono">
                            {resource.resourceId}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button
                          type="button"
                          className="self-stretch rounded flex justify-center items-center gap-2.5 cursor-pointer"
                          onClick={() => handleOpenEditResource(resource)}
                        >
                          <PencilSquareLinedIcon className="h-5 w-5 text-blue50" />
                        </button>
                        <button
                          type="button"
                          className="self-stretch rounded flex justify-center items-center gap-2.5 cursor-pointer"
                          onClick={() => handleDeleteResource(resource)}
                        >
                          <DeleteIcon className="h-5 w-5 text-red50" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              <ButtonComponent.Pill
                variant="outline"
                buttonStyle="default"
                onClick={handleOpenAddResource}
              >
                + Connect New Resource
              </ButtonComponent.Pill>
            </div>
          </div>

          <div className="space-y-4">
            <div className="text-heading2 text-space50">Actions</div>
            <div className="space-y-4">
              {availableActions.length > 0 ? (
                <div className="space-y-4">
                  {availableActions.map((action) => (
                    <div
                      key={action.name}
                      className="flex flex-row justify-between p-4 mb-4 rounded-md shadow border border-zinc-300"
                    >
                      <div>
                        <div className="font-body text-space50 capitalize">
                          {action.name.split("_").join(" ")}
                        </div>
                        <div className="text-caption text-space70">
                          {action.description}
                        </div>
                      </div>

                      <ButtonComponent.Pill
                        variant="outline"
                        buttonStyle="default"
                        onClick={() => {
                          setSelectedAction(action);
                          setIsActionModalOpen(true);
                        }}
                      >
                        Execute
                      </ButtonComponent.Pill>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-gray-500">
                  No actions available for this integration
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 flex flex-col justify-start items-start">
          <div className="opacity-50">
            <div className="justify-start text-black text-[10px] font-normal font-mono">
              Integration ID: {integration.integrationId}
            </div>
            <div className="justify-start text-black text-[10px] font-normal font-mono">
              Metadata: {JSON.stringify(integration.metaData)}
            </div>
            <div className="justify-start text-black text-[10px] font-normal font-mono">
              Created At: {new Date(integration.createdAt).toLocaleString()}
            </div>
            <div className="justify-start text-black text-[10px] font-normal font-mono">
              Created By: {integration.createdBy}
            </div>
          </div>
          <div className="mt-8 flex justify-end">
            <ButtonComponent.Pill
              variant="filled"
              buttonStyle="danger"
              onClick={() => setIsDeleteModalOpen(true)}
              iconBefore={<DeleteIcon className="h-3 w-3 text-white" />}
            >
              Delete Integration
            </ButtonComponent.Pill>
          </div>
        </div>

        <Modal
          open={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          title="Delete Integration"
          error={
            deleteError ? { message: JSON.stringify(deleteError) } : undefined
          }
          actions={{
            cancel: {
              label: "Cancel",
              onClick: () => setIsDeleteModalOpen(false),
            },
            confirm: {
              label: isDeleting ? "Deleting..." : "Delete Integration",
              onClick: handleDelete,
              disabled: isDeleting,
              variant: "danger",
            },
          }}
        >
          <div className="text-center text-sm text-gray-500 mt-4">
            Are you sure you want to delete{" "}
            <span className="font-semibold">{integration.integrationName}</span>
            ? <br />
            This action cannot be undone.
          </div>
        </Modal>

        <Modal
          open={saveError !== null}
          onClose={() => setSaveError(null)}
          title="Error Saving Integration"
          error={saveError ? { message: saveError } : undefined}
          actions={{
            cancel: {
              label: "Close",
              onClick: () => setSaveError(null),
            },
          }}
        >
          <div className="text-center text-sm text-gray-500 mt-4">
            There was an error saving the integration. Please try again.
          </div>
        </Modal>

        <Modal
          open={isResourceModalOpen}
          onClose={() => setIsResourceModalOpen(false)}
          title={editingResource ? "Edit Resource" : "Add Resource"}
          actions={{
            cancel: {
              label: "Cancel",
              onClick: () => setIsResourceModalOpen(false),
            },
            confirm: {
              label: isResourceSaving
                ? editingResource
                  ? "Saving..."
                  : "Adding..."
                : editingResource
                  ? "Save Resource"
                  : "Add Resource",
              onClick: handleSaveResource,
              disabled:
                !resourceBaseType ||
                !resourceType ||
                !resourceId ||
                isResourceSaving,
            },
          }}
        >
          <div className="mt-4 space-y-4">
            <SelectField
              label="Resource Base Type"
              value={resourceBaseType}
              options={[
                { value: "place", label: "Place" },
                { value: "thing", label: "Thing" },
              ]}
              onChange={handleResourceBaseTypeChange}
              required
              disabled={!!editingResource}
            />
            <SelectField
              label="Resource Type"
              value={resourceType}
              options={
                resourceBaseType === "place"
                  ? [
                      { value: "site", label: "Site" },
                      { value: "fleet", label: "Fleet" },
                    ]
                  : thingTypes.map((type) => ({
                      value: type,
                      label: type,
                    }))
              }
              onChange={setResourceType}
              required
              disabled={!resourceBaseType || !!editingResource}
            />
            <TextField
              label="Resource ID"
              value={resourceId}
              onChange={setResourceId}
              required
              placeholder="Enter the unique identifier for this resource"
              disabled={!!editingResource}
            />
            <TextField
              label="Third Party Id"
              value={thirdPartyId}
              onChange={setThirdPartyId}
              required
              disabled={!!editingResource}
            />
            <TextField
              label="Resource Third Party Id"
              value={resourceThirdPartyId}
              onChange={setResourceThirdPartyId}
              required
              disabled={!!editingResource}
            />
            <div>
              <div className="text-xs font-medium text-gray-700 mb-1">
                Metadata
              </div>
              {Object.entries(resourceMetadata).map(([key, value], idx) => (
                <div key={key} className="flex gap-2 mb-1">
                  <TextField
                    label="Key"
                    value={key}
                    onChange={(newKey) => {
                      const newMeta = { ...resourceMetadata };
                      delete newMeta[key];
                      newMeta[newKey] = value;
                      setResourceMetadata(newMeta);
                    }}
                  />
                  <TextField
                    label="Value"
                    value={value}
                    onChange={(newValue) => {
                      setResourceMetadata({
                        ...resourceMetadata,
                        [key]: newValue,
                      });
                    }}
                  />
                  <button
                    type="button"
                    className="self-stretch rounded flex justify-center items-center gap-2.5 cursor-pointer"
                    onClick={() => {
                      const newMeta = { ...resourceMetadata };
                      delete newMeta[key];
                      setResourceMetadata(newMeta);
                    }}
                  >
                    <DeleteIcon className="h-5 w-5 text-red50" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                className="text-xs text-blue-500 px-2 mt-1"
                onClick={() => {
                  setResourceMetadata({
                    ...resourceMetadata,
                    "": "",
                  });
                }}
              >
                + Add Metadata
              </button>
            </div>
            {resourceError && (
              <div className="text-red-500 text-sm mt-2">{resourceError}</div>
            )}
          </div>
        </Modal>

        <IntegrationActionModal
          open={isActionModalOpen}
          onClose={() => {
            setIsActionModalOpen(false);
            setSelectedAction(null);
          }}
          integrationId={integration.integrationId}
          action={selectedAction}
        />
      </PageContent>
    </ContentLayout>
  );
};

export { IntegrationDetailPage };
