import { DefaultErrorFallback } from "components/uikit/ErrorBoundary/ErrorBoundary";
import { useErrorActions } from "stores/errorStore";
import type { AppError } from "types/errors";

const NoAccessPage = () => {
  const { clearNoAccessError } = useErrorActions();

  // Create a 403 error object following the same pattern as ErrorBoundary
  const errorNoAccess: AppError = {
    id: "403-access-denied",
    title: "Access Denied",
    message:
      "Insufficient permissions to view this resource, please contact your Organization owner.",
    category: "authorization",
    severity: "high",
    timestamp: new Date(),
    dismissible: true,
    autoHide: false,
  };

  const handleTryAgain = () => {
    clearNoAccessError();
  };

  return (
    <DefaultErrorFallback error={errorNoAccess} onRetry={handleTryAgain} />
  );
};

export default NoAccessPage;
