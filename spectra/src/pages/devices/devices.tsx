import { type Thing, useThingsApi } from "api/ingestion/things";
import { DeviceDetail } from "components";
import { SingleCategoryFilter } from "components/controls/SingleCategoryFilter";
import { useSelectedDevice } from "context/SelectedDeviceContext";
import { SelectedSimulationProvider } from "context/SelectedSimulationContext";
import { TimeRangeSelector } from "context/SelectedTimeRangeContext";

import { useEffect, useState } from "react";

import { useAuth } from "../../context/AuthContext";
import AddDeviceModal from "./AddDeviceModal";
import { DevicesByPlace } from "./DevicesByPlace";
import { DevicesByType } from "./DevicesByType";
import DevicesTable from "./DevicesTable";
import { useAppData } from "hooks/useAppData";

const DeviceDetailWrapper = () => {
  const { selectedDevice, setSelectedDevice } = useSelectedDevice();
  const placeType = selectedDevice?.placeType?.toLowerCase() || "site";
  return (
    <DeviceDetail
      selectedDevice={selectedDevice}
      setSelectedDevice={setSelectedDevice}
      placeType={placeType}
    />
  );
};

const PageContent = () => {
  const [viewMode, setViewMode] = useState("tile");
  const [showByPlace, setShowByPlace] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deviceTypes, setDeviceTypes] = useState<string[]>([]);
  const [selectedDeviceType, setSelectedDeviceType] = useState<string>("All");

  const { permissions } = useAuth();
  const { getThingTypes } = useThingsApi();
  const { selectedDevice } = useSelectedDevice();
  const { fetchAllIfNeeded, sites, devices, fleets, addDevice } = useAppData();

  const placeType = selectedDevice?.placeType?.toLowerCase() || "site";

  // biome-ignore lint/correctness/useExhaustiveDependencies: adding getSites, getFleets, getThingTypes, getThings to the dependency array causes a re-render loop
  useEffect(() => {
    const fetchData = async () => {
      const [_, allDevicesTypes] = await Promise.all([
        fetchAllIfNeeded(),
        getThingTypes(),
      ]);

      setDeviceTypes(["All", ...allDevicesTypes]);
    };

    fetchData();
  }, []);

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);

  const handleDeviceAdded = (siteId: string, deviceData: Thing) => {
    return addDevice(placeType, siteId, deviceData);
  };

  const filteredDevices =
    selectedDeviceType === "All"
      ? devices
      : devices.filter((device) => device.thingType === selectedDeviceType);

  const deviceTypesWithoutAll = deviceTypes.filter((type) => type !== "All");

  const renderActionButtons = () => {
    if (permissions.includes("write:ingest_things")) {
      return (
        <button
          type="button"
          onClick={handleOpenModal}
          className="px-3.5 py-2 rounded-full border border-space80 justify-end items-center gap-1 cursor-pointer hover:bg-gray95 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue90 focus:ring-blue90 flex"
        >
          <div className="text-space70 text-xs font-medium leading-[14px]">
            + Add Device
          </div>
        </button>
      );
    }
    return null;
  };

  return (
    <div className="flex flex-row w-full min-h-screen">
      <div className="flex-1 h-screen overflow-y-auto">
        <div className="flex pt-4 pb-5 justify-between">
          <p className="text-heading1 text-space50">Devices</p>
          <div className="flex gap-2">
            <TimeRangeSelector />
            <SingleCategoryFilter
              options={["Tile View", "Table View"]}
              defaultSelected={viewMode === "tile" ? "Tile View" : "Table View"}
              onChange={(value) =>
                setViewMode(value === "Tile View" ? "tile" : "table")
              }
            />
            {viewMode === "tile" && (
              <SingleCategoryFilter
                options={["Group by Place", "Group by Type"]}
                defaultSelected={
                  showByPlace ? "Group by Place" : "Group by Type"
                }
                onChange={(value) => setShowByPlace(value === "Group by Place")}
              />
            )}
            {viewMode === "table" && (
              <SingleCategoryFilter
                options={deviceTypes}
                defaultSelected={selectedDeviceType}
                onChange={setSelectedDeviceType}
              />
            )}
            {renderActionButtons()}
            <AddDeviceModal
              isOpen={isModalOpen}
              onClose={handleCloseModal}
              onDeviceAdded={handleDeviceAdded}
            />
          </div>
        </div>

        {viewMode === "tile" ? (
          showByPlace ? (
            <DevicesByPlace
              sites={sites}
              fleets={fleets}
              devices={devices}
              deviceTypes={deviceTypesWithoutAll}
            />
          ) : (
            <DevicesByType
              places={[...sites, ...fleets]}
              devices={devices}
              deviceTypes={deviceTypesWithoutAll}
            />
          )
        ) : (
          <DevicesTable devices={filteredDevices} />
        )}
      </div>
      <DeviceDetailWrapper />
    </div>
  );
};

const DevicesPage = () => {
  const { setSelectedDevice } = useSelectedDevice();

  useEffect(() => {
    return () => {
      setSelectedDevice(null);
    };
  }, [setSelectedDevice]);

  return (
    <SelectedSimulationProvider simulations={[]}>
      <PageContent />
    </SelectedSimulationProvider>
  );
};

export { DevicesPage };
