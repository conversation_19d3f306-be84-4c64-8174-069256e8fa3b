import type { Thing } from "api/ingestion/things.ts";
import { TableProgressBar } from "components/devices/DeviceTile";
import { useSelectedDevice } from "context/SelectedDeviceContext.tsx";
import { useSelectedTimeRange } from "context/SelectedTimeRangeContext.tsx";
import { typeToLabel } from "utils/typeToLabel";

import React, { useEffect, useMemo, useState } from "react";

import { useScrollIndicatorRecalculate } from "components/uikit/ScrollIndicatorWrapper";
import { useDeviceStats } from "hooks/useDeviceStats";
import { ReactComponent as BatteriesIcon } from "images/icons/batteries.svg";
import { ReactComponent as ChargersIcon } from "images/icons/chargers.svg";
import { ReactComponent as MeterIcon } from "images/icons/meter.svg";
import { ReactComponent as SwapStationIcon } from "images/icons/swap-station.svg";

interface DeviceStatsProps {
  device: Thing;
}

export enum SortDirection {
  ASCENDING = "ascending",
  DESCENDING = "descending",
}

const DevicesTable = ({
  devices,
  defaultSortKey = null,
  defaultSortDirection = SortDirection.ASCENDING,
  additionalColumns = [],
}: {
  devices: Thing[];
  defaultSortKey?: string | null;
  defaultSortDirection?: SortDirection;
  additionalColumns?: {
    key: string;
    label: string;
    format?: (value: string) => string;
  }[];
}) => {
  const { selectedDevice, setSelectedDevice } = useSelectedDevice();
  const { start, end } = useSelectedTimeRange();
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState({
    key: defaultSortKey,
    direction: defaultSortDirection,
  });

  const deviceTypes = useMemo(() => {
    return Array.from(new Set(devices.map((device) => device.thingType)));
  }, [devices]);

  const filteredDevices = useMemo(() => {
    return selectedTypes.length > 0
      ? devices.filter((device) => selectedTypes.includes(device.thingType))
      : devices;
  }, [devices, selectedTypes]);

  const sortedDevices = useMemo(() => {
    const sortableDevices = [...filteredDevices];
    if (sortConfig.key !== null) {
      sortableDevices.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === SortDirection.ASCENDING ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === SortDirection.ASCENDING ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableDevices;
  }, [filteredDevices, sortConfig]);

  const requestSort = (key) => {
    let direction = SortDirection.ASCENDING;
    if (
      sortConfig.key === key &&
      sortConfig.direction === SortDirection.ASCENDING
    ) {
      direction = SortDirection.DESCENDING;
    }
    setSortConfig({ key, direction });
  };

  const handleRowClick = (device: Thing) => {
    setSelectedDevice(selectedDevice === device ? null : device);
  };

  const handleTypeChange = (type: string) => {
    setSelectedTypes((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type],
    );
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case "Battery":
        return <BatteriesIcon />;
      case "Charger":
        return <ChargersIcon />;
      case "Meter":
        return <MeterIcon />;
      case "SwapStation":
        return <SwapStationIcon />;
      default:
        return null;
    }
  };

  return (
    <div>
      {/* <div className="relative">
        <input
          type="text"
          placeholder="Search devices..."
          className="pl-8 pr-2 py-1 border rounded-md"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <svg
          className="w-4 h-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
      <div className="mb-4 flex space-x-4">
        {deviceTypes.map((type) => (
          <label key={type} className="inline-flex items-center">
            <input
              type="checkbox"
              checked={selectedTypes.includes(type)}
              onChange={() => handleTypeChange(type)}
              className="form-checkbox h-5 w-5 text-blue-600"
            />
            <span className="ml-2 text-gray-700">{type}</span>
          </label>
        ))}
      </div> */}
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 tracking-wider cursor-pointer w-24"
              onClick={() => requestSort("thingType")}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  requestSort("thingType");
                }
              }}
            >
              Type{" "}
              {sortConfig.key === "thingType" &&
                (sortConfig.direction === SortDirection.ASCENDING ? "▲" : "▼")}
            </th>
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 tracking-wider cursor-pointer w-24"
              onClick={() => requestSort("placeType")}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  requestSort("placeType");
                }
              }}
            >
              Place Type{" "}
              {sortConfig.key === "placeType" &&
                (sortConfig.direction === SortDirection.ASCENDING ? "▲" : "▼")}
            </th>
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 tracking-wider cursor-pointer w-40"
              onClick={() =>
                requestSort("thingManufacturerId ?? device.thingId")
              }
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  requestSort("thingManufacturerId ?? device.thingId");
                }
              }}
            >
              Name{" "}
              {sortConfig.key === "thingManufacturerId ?? device.thingId" &&
                (sortConfig.direction === SortDirection.ASCENDING ? "▲" : "▼")}
            </th>
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 tracking-wider cursor-pointer w-48"
              onClick={() => requestSort("thingDescription")}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  requestSort("thingDescription");
                }
              }}
            >
              Description{" "}
              {sortConfig.key === "thingDescription" &&
                (sortConfig.direction === SortDirection.ASCENDING ? "▲" : "▼")}
            </th>
            {additionalColumns.map((column) => (
              <th
                className="px-3 py-3 text-left text-xs font-medium text-gray-500 tracking-wider cursor-pointer w-24"
                onClick={() => requestSort(column.key)}
                key={column.key}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    requestSort(column.key);
                  }
                }}
              >
                {column.label}{" "}
                {sortConfig.key === column.key &&
                  (sortConfig.direction === SortDirection.ASCENDING
                    ? "▲"
                    : "▼")}
              </th>
            ))}
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">
              Data
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {sortedDevices.map((device) => (
            <tr
              key={device.thingId}
              onClick={() => handleRowClick(device)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleRowClick(device);
                }
              }}
              className={`cursor-pointer hover:bg-gray-100 transition-colors ${
                selectedDevice === device ? "bg-blue90" : ""
              }`}
            >
              <td className="px-3 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  {getDeviceIcon(device.thingType)}
                  <span className="ml-2 text-sm">{device.thingType}</span>
                </div>
              </td>
              <td className="px-3 py-4 whitespace-nowrap text-sm">
                {device.placeType || (device.fleetId ? "fleet" : "site")}
              </td>
              <td className="px-3 py-4 whitespace-nowrap text-sm">
                {device.thingManufacturerId ?? device.thingId}
              </td>
              <td className="px-3 py-4 whitespace-nowrap text-sm overflow-hidden overflow-ellipsis">
                {device.thingDescription}
              </td>
              {additionalColumns.map((column) => (
                <td
                  className="px-3 py-4 whitespace-nowrap text-sm"
                  key={column.key}
                >
                  {column.format
                    ? column.format(device[column.key])
                    : device[column.key]}
                </td>
              ))}
              <td className="px-3 py-4">
                <DeviceStats device={device} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const DeviceStats = React.memo(
  ({ device }: DeviceStatsProps) => {
    const { stats, isLoading } = useDeviceStats(device);
    const recalculate = useScrollIndicatorRecalculate();

    // biome-ignore lint/correctness/useExhaustiveDependencies: recalculate is not a dependency of the useEffect hook
    useEffect(() => {
      if (!isLoading) {
        recalculate();
      }
    }, [isLoading]);

    if (isLoading) {
      return <div className="h-4 bg-gray-200 w-full animate-pulse" />;
    }

    if (Object.values(stats).length === 0) {
      return (
        <TableProgressBar value={"-"} percentage={0} unit="" label="No data" />
      );
    }

    return (
      <div className="flex items-center space-x-2">
        {Object.values(stats).map((stat) => (
          <TableProgressBar
            key={stat.type}
            value={stat.value ?? "-"}
            percentage={stat.value / 100}
            unit={stat.unit}
            label={typeToLabel(stat.type)}
            digits={Math.abs(stat.value) < 1 ? 3 : 0} // HACK: this is a hack to get the digits to be correct for small values
          />
        ))}
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.device.thingId === nextProps.device.thingId &&
      prevProps.start === nextProps.start &&
      prevProps.end === nextProps.end
    );
  },
);

export default DevicesTable;
