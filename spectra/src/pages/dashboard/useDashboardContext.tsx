import type { FleetResponse, SiteResponse } from "api/ingestion/places";
import type { Thing } from "api/ingestion/things";
import { type ReactNode, createContext, useContext, useEffect } from "react";

import { useAppData } from "hooks/useAppData";

type DashboardContextType = {
  sites: SiteResponse[];
  fleets: FleetResponse[];
  devices: Thing[];
  fetchAllIfNeeded: () => void;
};

const DashboardContext = createContext<DashboardContextType | undefined>(
  undefined,
);

type DashboardProviderProps = {
  children: ReactNode;
};

export const DashboardProvider = ({ children }: DashboardProviderProps) => {
  const { sites, fleets, devices, fetchAllIfNeeded } = useAppData();

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchAllIfNeeded is not a dependency of the useEffect hook
  useEffect(() => {
    fetchAllIfNeeded();
  }, []);

  const value: DashboardContextType = {
    sites,
    fleets,
    devices,
    fetchAllIfNeeded,
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboardContext = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error(
      "useDashboardContext must be used within a DashboardProvider",
    );
  }
  return context;
};
