import { type MonitorSummary, useMonitorsApi } from "api/enterprise/monitors";
import {
  CHART_COLORS,
  CHART_MESSAGES,
  CHART_TYPE_CONFIG,
  TOOLTIP_CONFIG,
  formatNumericValue,
} from "components/charts/foundation";
import { ErrorBoundary } from "components/uikit/ErrorBoundary/ErrorBoundary";
import Trend from "components/uikit/trend";
import { useAuth } from "context/AuthContext";
import { SelectedSimulationProvider } from "context/SelectedSimulationContext";
import { useAppData } from "hooks/useAppData";
import { MapView } from "pages/locationSelection/mapView";
import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Cell, Pie, PieChart, ResponsiveContainer, Tooltip } from "recharts";

import { useDeviceState } from "hooks/useDeviceState";
import {
  default as DevicesTable,
  SortDirection,
} from "pages/devices/DevicesTable";

export const AssetMap = () => {
  const { sites, fleets, fetchSitesIfNeeded, fetchFleetsIfNeeded, isLoading } =
    useAppData();

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchSitesIfNeeded is not a dependency of the useEffect hook
  useEffect(() => {
    fetchSitesIfNeeded();
    fetchFleetsIfNeeded();
  }, []);

  const locations = [...sites, ...fleets];

  if (isLoading.sites || isLoading.fleets) {
    return <div>Loading...</div>;
  }

  return (
    <MapView
      projection="globe"
      locations={locations}
      autoCenter={false}
      initialZoom={3}
    />
  );
};

export const SummaryBar = () => {
  const { devices, sites, fleets } = useAppData();
  const navigate = useNavigate();

  // HACK: Monitors endpoint is not working
  const { getMonitorSummary } = useMonitorsApi();
  const { user } = useAuth();

  const [monitorSummary, setMonitorSummary] = useState<MonitorSummary | null>(
    null,
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: getMonitorSummary is not a dependency of the useEffect hook
  useEffect(() => {
    if (!user) return;
    getMonitorSummary(user.partnerId).then(setMonitorSummary);
  }, [user]);

  // const totalMonitors = monitors.length;
  const totalMonitors = Object.values(
    monitorSummary?.stateSummary ?? {},
  ).reduce((acc, curr) => acc + curr, 0);

  const totalDevices = devices.length;
  const totalSites = sites.length;
  const totalFleets = fleets.length;
  const financeStat = 612;

  const stats = [
    {
      title: "Sites",
      value: totalSites,
      path: "/sites",
    },
    {
      title: "Fleets",
      value: totalFleets,
      path: "/fleets",
    },
    {
      title: "Devices",
      value: totalDevices,
      path: "/devices",
    },
    {
      title: "Monitors",
      value: totalMonitors,
      path: "/monitors",
    },
    {
      title: "Transaction Volume",
      value: financeStat,
      path: "/transaction",
    },
  ];

  return (
    <div className="w-full rounded-md shadow border border-gray95">
      <div className="flex divide-x divide-gray95">
        {stats.map((stat, index) => (
          <button
            key={stat.title}
            type="button"
            className="flex-1 p-3 text-center hover:bg-blue90 transition-colors duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue50 focus:ring-inset"
            onClick={() => navigate(stat.path)}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                navigate(stat.path);
              }
            }}
          >
            <div className="text-2xl font-light text-space50">
              {formatNumericValue(stat.value)}
            </div>
            <div className="text-xs text-space70 font-normal">{stat.title}</div>
          </button>
        ))}
      </div>
    </div>
  );
};

export const DeviceOverview = ({
  onlineDevicesCount,
  offlineDevicesCount,
}: {
  onlineDevicesCount: number | null;
  offlineDevicesCount: number | null;
}) => {
  if (onlineDevicesCount === null || offlineDevicesCount === null) {
    return <div>{CHART_MESSAGES.loading}</div>;
  }

  return (
    <div className="flex">
      <div className="w-1/2">
        <ResponsiveContainer width="100%" height={200}>
          <PieChart>
            <Pie
              data={[
                {
                  name: "Online",
                  value: onlineDevicesCount,
                  color: CHART_COLORS.online,
                },
                {
                  name: "Offline",
                  value: offlineDevicesCount,
                  color: CHART_COLORS.offline,
                },
              ]}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={CHART_TYPE_CONFIG.pie.outerRadius}
              innerRadius={CHART_TYPE_CONFIG.pie.innerRadius}
            >
              <Cell key="Online" fill={CHART_COLORS.online} />
              <Cell key="Offline" fill={CHART_COLORS.offline} />
            </Pie>
            <Tooltip
              contentStyle={TOOLTIP_CONFIG.contentStyle}
              formatter={(value: number) => formatNumericValue(value)}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
      <div className="w-1/2 pl-4">
        <div className="flex items-center mb-2">
          <div
            className="w-4 h-4 rounded-full mr-2"
            style={{ backgroundColor: CHART_COLORS.online }}
          />
          <span className="flex-grow">Online</span>
          <span>{formatNumericValue(onlineDevicesCount)}</span>
        </div>
        <div className="flex items-center mb-2">
          <div
            className="w-4 h-4 rounded-full mr-2"
            style={{ backgroundColor: CHART_COLORS.offline }}
          />
          <span className="flex-grow">Offline</span>
          <span>{formatNumericValue(offlineDevicesCount)}</span>
        </div>
      </div>
    </div>
  );
};

export const MonitorOverview = () => {
  const { getMonitorSummary } = useMonitorsApi();
  const { user } = useAuth();

  const [monitorSummary, setMonitorSummary] = useState<MonitorSummary | null>(
    null,
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: getMonitorSummary is not a dependency of the useEffect hook
  useEffect(() => {
    if (!user) return;

    getMonitorSummary(user.partnerId).then(setMonitorSummary);
  }, [user]);

  const healthyMonitors = monitorSummary?.stateSummary?.HEALTHY ?? 0;
  const unhealthyMonitors = monitorSummary?.stateSummary?.UNHEALTHY ?? 0;
  const warningMonitors = monitorSummary?.stateSummary?.WARNING ?? 0;
  const completeDataMonitors =
    monitorSummary?.dataStateSummary?.COMPLETE_DATA ?? 0;
  const missingDataMonitors =
    monitorSummary?.dataStateSummary?.MISSING_DATA ?? 0;
  const noDataMonitors = monitorSummary?.dataStateSummary?.NO_DATA ?? 0;

  return (
    <div className="flex flex-col gap-2">
      <div className="text-caption text-space70">States</div>
      <div className="grid grid-cols-3 gap-2">
        <div
          className={`p-2 rounded-md ${
            healthyMonitors > 0 ? "bg-green90 text-green40" : ""
          }`}
        >
          <div className="text-xs font-normal">Healthy</div>
          <div className="text-2xl">{healthyMonitors}</div>
        </div>
        <div
          className={`p-2 rounded-md ${
            unhealthyMonitors > 0 ? "bg-red90 text-red50" : ""
          }`}
        >
          <div className="text-xs font-normal">Unhealthy</div>
          <div className="text-2xl">{unhealthyMonitors}</div>
        </div>
        <div
          className={`p-2 rounded-md ${
            warningMonitors > 0 ? "bg-yellow90 text-yellow40" : ""
          }`}
        >
          <div className="text-xs font-normal">Warning</div>
          <div className="text-2xl">{warningMonitors}</div>
        </div>
      </div>
      <div className="text-caption text-space70 mt-2">Data States</div>
      <div className="grid grid-cols-3 gap-2">
        <div
          className={`p-2 rounded-md ${
            completeDataMonitors > 0 ? "bg-green90 text-green40" : ""
          }`}
        >
          <div className="text-xs font-normal">Complete Data</div>
          <div className="text-2xl">{completeDataMonitors}</div>
        </div>
        <div
          className={`p-2 rounded-md ${
            missingDataMonitors > 0 ? "bg-yellow90 text-yellow40" : ""
          }`}
        >
          <div className="text-xs font-normal">Missing Data</div>
          <div className="text-2xl">{missingDataMonitors}</div>
        </div>
        <div
          className={`p-2 rounded-md ${
            noDataMonitors > 0 ? "bg-gray90 text-gray40" : ""
          }`}
        >
          <div className="text-xs font-normal">No Data</div>
          <div className="text-2xl">{noDataMonitors}</div>
        </div>
      </div>
      <div className="text-xs text-gray-500 pt-2">
        Total monitors: {healthyMonitors + unhealthyMonitors + warningMonitors}
      </div>
      <div className="opacity-50 justify-start text-black text-[10px] font-normal font-mono mt-2">
        Last Updated:{" "}
        {new Date(monitorSummary?.calculationTime).toLocaleString()}
      </div>
    </div>
  );
};

export const SummaryStats = ({
  stats,
}: {
  stats: {
    activeAlerts: number;
    assetsOutOfService: number;
    devicesOnline: number;
    downtime: number;
  };
}) => (
  <div className="p-4 mb-4 rounded-md shadow border border-zinc-300">
    <div className="flex gap-2">
      <div className="flex-1 px-4">
        <StatCard
          title="Active Alerts"
          value={stats.activeAlerts.toString()}
          change={-0.01}
          onClick={() => {}}
        />
      </div>
      <div className="flex-1 px-4">
        <StatCard
          title="Assets Out of Service"
          value={stats.assetsOutOfService.toString()}
          change={0}
          onClick={() => {}}
        />
      </div>
      <div className="flex-1 px-4">
        <StatCard
          title="Devices Online"
          value={stats.devicesOnline.toString()}
          change={0.0}
          onClick={() => {}}
        />
      </div>
      <div className="flex-1 px-4">
        <StatCard
          title="% Downtime"
          value={stats.downtime.toLocaleString()}
          unit="kWh"
          change={-0.01}
          onClick={() => {}}
        />
      </div>
    </div>
  </div>
);

export type StatCardProps = {
  title: string;
  value: string;
  change: number;
  onClick: () => void;
  unit?: string;
};

const StatCard = ({ title, value, unit, change, onClick }: StatCardProps) => (
  <button
    type="button"
    className="w-full cursor-pointer text-left"
    onClick={onClick}
    onKeyDown={(e) => {
      if (e.key === "Enter" || e.key === " ") {
        onClick();
      }
    }}
  >
    <div className="flex items-center gap-1 justify-between">
      <div className="text-title text-space50">
        {value}{" "}
        {unit && <span className="text-caption text-space70">{unit}</span>}
      </div>
      <Trend percent={change} />
    </div>
    <div className="text-caption text-space70">{title}</div>
  </button>
);

export const DeviceList = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const { devices, isLoading } = useDeviceState();

  const filteredDevices = useMemo(() => {
    return devices.filter((device) =>
      Object.values(device).some((value) =>
        value?.toString().toLowerCase().includes(searchTerm.toLowerCase()),
      ),
    );
  }, [devices, searchTerm]);

  if (isLoading) {
    return (
      <div className="text-caption text-space70 w-full text-center py-4">
        Loading...
      </div>
    );
  }

  return (
    <ErrorBoundary
      onError={(_) => {
        // ignore errors in this component
      }}
      showErrorInStore={false}
    >
      <SelectedSimulationProvider simulations={[]}>
        {/* <div className="flex flex-wrap gap-4">
            {devices.map((device) => (
              <DeviceTile device={device} key={device.thingId} />
            ))}
          </div> */}
        <DevicesTable
          devices={filteredDevices}
          defaultSortKey="lastEventTime"
          defaultSortDirection={SortDirection.DESCENDING}
          additionalColumns={[
            {
              key: "lastEventTime",
              label: "Last Event Time",
              format: (value) => new Date(value).toLocaleString(),
            },
          ]}
        />
      </SelectedSimulationProvider>
    </ErrorBoundary>
  );
};
