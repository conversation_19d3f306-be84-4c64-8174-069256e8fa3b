import { useMemo, useState } from "react";
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Line,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON>A<PERSON>s,
  <PERSON>A<PERSON><PERSON>,
} from "recharts";

interface Transaction {
  id: string;
  userId: string; // US_{id}
  site: string; // HUB_{id}
  returnedBatteryId: string; // BAT_{id}
  installedBatteryId: string; // BAT_{id}
  chargeState: string; // 0-100%
  revenue: string;
  timestamp: string; // ISO 8601
  paymentMethod: "Credit Card" | "Debit Card" | "Cash" | "Mobile Wallet";
}

const transactions: Transaction[] = [
  {
    id: "TX_73624",
    userId: "US_0640",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2158",
    installedBatteryId: "BAT_2509",
    chargeState: "92",
    revenue: "19431",
    timestamp: "2025-06-20T08:34:05.083Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_73625",
    userId: "US_2288",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8510",
    installedBatteryId: "BAT_9871",
    chargeState: "88",
    revenue: "11461",
    timestamp: "2025-06-20T11:06:53.120Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_73626",
    userId: "US_2876",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5307",
    installedBatteryId: "BAT_7263",
    chargeState: "92",
    revenue: "11581",
    timestamp: "2025-06-20T14:38:27.122Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_73627",
    userId: "US_2498",
    site: "HUB_7789",
    returnedBatteryId: "BAT_7602",
    installedBatteryId: "BAT_2918",
    chargeState: "95",
    revenue: "12831",
    timestamp: "2025-06-20T19:25:12.752Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73628",
    userId: "US_5582",
    site: "HUB_0466",
    returnedBatteryId: "BAT_7339",
    installedBatteryId: "BAT_4251",
    chargeState: "96",
    revenue: "19915",
    timestamp: "2025-06-20T21:49:27.242Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_73724",
    userId: "US_8165",
    site: "HUB_8188",
    returnedBatteryId: "BAT_6228",
    installedBatteryId: "BAT_9535",
    chargeState: "90",
    revenue: "11820",
    timestamp: "2025-06-21T07:47:22.545Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73725",
    userId: "US_1917",
    site: "HUB_0611",
    returnedBatteryId: "BAT_5486",
    installedBatteryId: "BAT_4159",
    chargeState: "91",
    revenue: "10179",
    timestamp: "2025-06-21T11:48:11.197Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_73726",
    userId: "US_4789",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2545",
    installedBatteryId: "BAT_378",
    chargeState: "94",
    revenue: "19360",
    timestamp: "2025-06-21T12:24:43.245Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73727",
    userId: "US_7835",
    site: "HUB_0466",
    returnedBatteryId: "BAT_8113",
    installedBatteryId: "BAT_7541",
    chargeState: "99",
    revenue: "14623",
    timestamp: "2025-06-21T15:52:09.604Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73728",
    userId: "US_0439",
    site: "HUB_9196",
    returnedBatteryId: "BAT_8883",
    installedBatteryId: "BAT_218",
    chargeState: "90",
    revenue: "12116",
    timestamp: "2025-06-21T17:41:47.475Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73729",
    userId: "US_8660",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7201",
    installedBatteryId: "BAT_1109",
    chargeState: "90",
    revenue: "16890",
    timestamp: "2025-06-21T17:44:47.635Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_73824",
    userId: "US_6059",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8529",
    installedBatteryId: "BAT_5720",
    chargeState: "92",
    revenue: "14756",
    timestamp: "2025-06-22T05:13:13.617Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_73825",
    userId: "US_6223",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5077",
    installedBatteryId: "BAT_1178",
    chargeState: "91",
    revenue: "18582",
    timestamp: "2025-06-22T09:09:41.331Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_73826",
    userId: "US_3127",
    site: "HUB_8188",
    returnedBatteryId: "BAT_4530",
    installedBatteryId: "BAT_938",
    chargeState: "92",
    revenue: "17028",
    timestamp: "2025-06-22T10:19:50.229Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73827",
    userId: "US_2382",
    site: "HUB_0466",
    returnedBatteryId: "BAT_557",
    installedBatteryId: "BAT_7070",
    chargeState: "94",
    revenue: "15840",
    timestamp: "2025-06-22T10:59:38.245Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_73828",
    userId: "US_2648",
    site: "HUB_9196",
    returnedBatteryId: "BAT_268",
    installedBatteryId: "BAT_9661",
    chargeState: "92",
    revenue: "10485",
    timestamp: "2025-06-22T12:03:30.998Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_73829",
    userId: "US_9423",
    site: "HUB_7789",
    returnedBatteryId: "BAT_6200",
    installedBatteryId: "BAT_8258",
    chargeState: "92",
    revenue: "17391",
    timestamp: "2025-06-22T16:46:43.577Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_73830",
    userId: "US_3922",
    site: "HUB_0611",
    returnedBatteryId: "BAT_6939",
    installedBatteryId: "BAT_4087",
    chargeState: "91",
    revenue: "16506",
    timestamp: "2025-06-22T21:31:59.407Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73924",
    userId: "US_5533",
    site: "HUB_9196",
    returnedBatteryId: "BAT_1817",
    installedBatteryId: "BAT_4872",
    chargeState: "99",
    revenue: "18441",
    timestamp: "2025-06-23T05:05:13.541Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73925",
    userId: "US_0610",
    site: "HUB_8188",
    returnedBatteryId: "BAT_6579",
    installedBatteryId: "BAT_7414",
    chargeState: "94",
    revenue: "18117",
    timestamp: "2025-06-23T09:06:34.101Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73926",
    userId: "US_6607",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6396",
    installedBatteryId: "BAT_4282",
    chargeState: "89",
    revenue: "10456",
    timestamp: "2025-06-23T11:17:23.429Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_73927",
    userId: "US_3823",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4878",
    installedBatteryId: "BAT_4455",
    chargeState: "98",
    revenue: "18278",
    timestamp: "2025-06-23T13:33:31.907Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_73928",
    userId: "US_1865",
    site: "HUB_0611",
    returnedBatteryId: "BAT_6642",
    installedBatteryId: "BAT_6760",
    chargeState: "91",
    revenue: "16245",
    timestamp: "2025-06-23T15:33:38.747Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_73929",
    userId: "US_9006",
    site: "HUB_4273",
    returnedBatteryId: "BAT_135",
    installedBatteryId: "BAT_5418",
    chargeState: "96",
    revenue: "12521",
    timestamp: "2025-06-23T20:20:23.953Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_73930",
    userId: "US_0171",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2766",
    installedBatteryId: "BAT_6200",
    chargeState: "88",
    revenue: "13975",
    timestamp: "2025-06-23T20:55:36.665Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_73931",
    userId: "US_9075",
    site: "HUB_7188",
    returnedBatteryId: "BAT_1203",
    installedBatteryId: "BAT_2158",
    chargeState: "95",
    revenue: "10647",
    timestamp: "2025-06-24T01:18:43.660Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74024",
    userId: "US_7624",
    site: "HUB_9196",
    returnedBatteryId: "BAT_3750",
    installedBatteryId: "BAT_3801",
    chargeState: "90",
    revenue: "18290",
    timestamp: "2025-06-24T05:01:57.840Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74025",
    userId: "US_7217",
    site: "HUB_4273",
    returnedBatteryId: "BAT_2572",
    installedBatteryId: "BAT_2003",
    chargeState: "89",
    revenue: "15975",
    timestamp: "2025-06-24T05:58:10.248Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74026",
    userId: "US_6253",
    site: "HUB_7188",
    returnedBatteryId: "BAT_9916",
    installedBatteryId: "BAT_7660",
    chargeState: "96",
    revenue: "18391",
    timestamp: "2025-06-24T07:39:14.522Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74027",
    userId: "US_4852",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5556",
    installedBatteryId: "BAT_6294",
    chargeState: "87",
    revenue: "16288",
    timestamp: "2025-06-24T11:27:38.372Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74028",
    userId: "US_9514",
    site: "HUB_1651",
    returnedBatteryId: "BAT_1616",
    installedBatteryId: "BAT_8897",
    chargeState: "99",
    revenue: "10135",
    timestamp: "2025-06-24T11:52:27.403Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74029",
    userId: "US_6544",
    site: "HUB_7188",
    returnedBatteryId: "BAT_1914",
    installedBatteryId: "BAT_2070",
    chargeState: "94",
    revenue: "14241",
    timestamp: "2025-06-24T15:15:58.107Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74030",
    userId: "US_9297",
    site: "HUB_0466",
    returnedBatteryId: "BAT_1954",
    installedBatteryId: "BAT_440",
    chargeState: "94",
    revenue: "16031",
    timestamp: "2025-06-24T19:53:50.401Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74031",
    userId: "US_8835",
    site: "HUB_7789",
    returnedBatteryId: "BAT_6967",
    installedBatteryId: "BAT_9120",
    chargeState: "88",
    revenue: "18625",
    timestamp: "2025-06-24T21:33:27.912Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74032",
    userId: "US_8441",
    site: "HUB_4273",
    returnedBatteryId: "BAT_2471",
    installedBatteryId: "BAT_5005",
    chargeState: "87",
    revenue: "19063",
    timestamp: "2025-06-25T00:12:30.568Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74124",
    userId: "US_8743",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8646",
    installedBatteryId: "BAT_2469",
    chargeState: "90",
    revenue: "10750",
    timestamp: "2025-06-25T09:36:59.063Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74125",
    userId: "US_9908",
    site: "HUB_8188",
    returnedBatteryId: "BAT_230",
    installedBatteryId: "BAT_1274",
    chargeState: "97",
    revenue: "13792",
    timestamp: "2025-06-25T12:33:49.867Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74126",
    userId: "US_0511",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7007",
    installedBatteryId: "BAT_9385",
    chargeState: "96",
    revenue: "16967",
    timestamp: "2025-06-25T17:07:26.922Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74127",
    userId: "US_5892",
    site: "HUB_0466",
    returnedBatteryId: "BAT_9523",
    installedBatteryId: "BAT_1643",
    chargeState: "89",
    revenue: "16045",
    timestamp: "2025-06-25T18:05:24.342Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74128",
    userId: "US_7868",
    site: "HUB_0611",
    returnedBatteryId: "BAT_9025",
    installedBatteryId: "BAT_2489",
    chargeState: "93",
    revenue: "13307",
    timestamp: "2025-06-25T20:30:20.256Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74129",
    userId: "US_8269",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8665",
    installedBatteryId: "BAT_8804",
    chargeState: "89",
    revenue: "17061",
    timestamp: "2025-06-26T00:56:41.854Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74130",
    userId: "US_7322",
    site: "HUB_9196",
    returnedBatteryId: "BAT_3882",
    installedBatteryId: "BAT_7997",
    chargeState: "87",
    revenue: "14849",
    timestamp: "2025-06-26T05:44:32.942Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74131",
    userId: "US_9512",
    site: "HUB_0466",
    returnedBatteryId: "BAT_8994",
    installedBatteryId: "BAT_7018",
    chargeState: "97",
    revenue: "14872",
    timestamp: "2025-06-26T07:02:46.885Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74132",
    userId: "US_5476",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4541",
    installedBatteryId: "BAT_9037",
    chargeState: "92",
    revenue: "12267",
    timestamp: "2025-06-26T11:13:54.965Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74133",
    userId: "US_4205",
    site: "HUB_0611",
    returnedBatteryId: "BAT_6036",
    installedBatteryId: "BAT_9373",
    chargeState: "89",
    revenue: "17703",
    timestamp: "2025-06-26T14:41:02.254Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74224",
    userId: "US_6006",
    site: "HUB_9097",
    returnedBatteryId: "BAT_9469",
    installedBatteryId: "BAT_1481",
    chargeState: "87",
    revenue: "19560",
    timestamp: "2025-06-27T05:15:59.131Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74225",
    userId: "US_8276",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8462",
    installedBatteryId: "BAT_5173",
    chargeState: "88",
    revenue: "19641",
    timestamp: "2025-06-27T07:46:41.936Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74226",
    userId: "US_5227",
    site: "HUB_4273",
    returnedBatteryId: "BAT_5901",
    installedBatteryId: "BAT_5556",
    chargeState: "93",
    revenue: "18266",
    timestamp: "2025-06-27T10:32:55.483Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74227",
    userId: "US_2839",
    site: "HUB_0466",
    returnedBatteryId: "BAT_7581",
    installedBatteryId: "BAT_5563",
    chargeState: "87",
    revenue: "10143",
    timestamp: "2025-06-27T11:57:10.689Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74228",
    userId: "US_8522",
    site: "HUB_4273",
    returnedBatteryId: "BAT_4195",
    installedBatteryId: "BAT_7750",
    chargeState: "98",
    revenue: "10913",
    timestamp: "2025-06-27T16:12:04.048Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74229",
    userId: "US_0640",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2509",
    installedBatteryId: "BAT_3186",
    chargeState: "95",
    revenue: "15271",
    timestamp: "2025-06-27T17:05:53.582Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74230",
    userId: "US_2288",
    site: "HUB_7188",
    returnedBatteryId: "BAT_9871",
    installedBatteryId: "BAT_8673",
    chargeState: "96",
    revenue: "11969",
    timestamp: "2025-06-27T20:42:30.824Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74231",
    userId: "US_2876",
    site: "HUB_0611",
    returnedBatteryId: "BAT_7263",
    installedBatteryId: "BAT_9388",
    chargeState: "99",
    revenue: "11785",
    timestamp: "2025-06-28T01:06:21.022Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74232",
    userId: "US_2498",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2918",
    installedBatteryId: "BAT_3478",
    chargeState: "87",
    revenue: "15712",
    timestamp: "2025-06-28T01:32:19.267Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74233",
    userId: "US_5582",
    site: "HUB_1651",
    returnedBatteryId: "BAT_4251",
    installedBatteryId: "BAT_660",
    chargeState: "94",
    revenue: "19005",
    timestamp: "2025-06-28T05:34:11.542Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74234",
    userId: "US_8165",
    site: "HUB_4273",
    returnedBatteryId: "BAT_9535",
    installedBatteryId: "BAT_2918",
    chargeState: "98",
    revenue: "14086",
    timestamp: "2025-06-28T09:42:09.876Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74324",
    userId: "US_1917",
    site: "HUB_8778",
    returnedBatteryId: "BAT_4159",
    installedBatteryId: "BAT_2265",
    chargeState: "96",
    revenue: "11458",
    timestamp: "2025-06-29T06:38:35.300Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74325",
    userId: "US_4789",
    site: "HUB_0611",
    returnedBatteryId: "BAT_378",
    installedBatteryId: "BAT_065",
    chargeState: "94",
    revenue: "19704",
    timestamp: "2025-06-29T09:14:40.315Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74326",
    userId: "US_7835",
    site: "HUB_0611",
    returnedBatteryId: "BAT_7541",
    installedBatteryId: "BAT_7294",
    chargeState: "91",
    revenue: "13657",
    timestamp: "2025-06-29T11:45:53.770Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74327",
    userId: "US_0439",
    site: "HUB_0466",
    returnedBatteryId: "BAT_218",
    installedBatteryId: "BAT_9633",
    chargeState: "87",
    revenue: "10936",
    timestamp: "2025-06-29T14:42:34.405Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74328",
    userId: "US_8660",
    site: "HUB_4273",
    returnedBatteryId: "BAT_1109",
    installedBatteryId: "BAT_1326",
    chargeState: "97",
    revenue: "17141",
    timestamp: "2025-06-29T14:47:34.238Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74329",
    userId: "US_6059",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5720",
    installedBatteryId: "BAT_7085",
    chargeState: "98",
    revenue: "10768",
    timestamp: "2025-06-29T17:06:52.344Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74330",
    userId: "US_6223",
    site: "HUB_8778",
    returnedBatteryId: "BAT_1178",
    installedBatteryId: "BAT_8379",
    chargeState: "98",
    revenue: "12931",
    timestamp: "2025-06-29T19:52:24.277Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74331",
    userId: "US_3127",
    site: "HUB_4273",
    returnedBatteryId: "BAT_938",
    installedBatteryId: "BAT_7333",
    chargeState: "99",
    revenue: "17508",
    timestamp: "2025-06-29T20:16:44.191Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74332",
    userId: "US_2382",
    site: "HUB_9097",
    returnedBatteryId: "BAT_7070",
    installedBatteryId: "BAT_2373",
    chargeState: "93",
    revenue: "10035",
    timestamp: "2025-06-29T22:46:22.656Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74333",
    userId: "US_2648",
    site: "HUB_0611",
    returnedBatteryId: "BAT_9661",
    installedBatteryId: "BAT_4801",
    chargeState: "87",
    revenue: "12087",
    timestamp: "2025-06-30T02:14:52.133Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74334",
    userId: "US_9423",
    site: "HUB_4273",
    returnedBatteryId: "BAT_8258",
    installedBatteryId: "BAT_2247",
    chargeState: "89",
    revenue: "13844",
    timestamp: "2025-06-30T05:30:07.516Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74335",
    userId: "US_3922",
    site: "HUB_7188",
    returnedBatteryId: "BAT_4087",
    installedBatteryId: "BAT_9064",
    chargeState: "95",
    revenue: "18584",
    timestamp: "2025-06-30T06:09:02.004Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74424",
    userId: "US_5533",
    site: "HUB_4273",
    returnedBatteryId: "BAT_4872",
    installedBatteryId: "BAT_4159",
    chargeState: "91",
    revenue: "14958",
    timestamp: "2025-07-01T08:41:38.223Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74425",
    userId: "US_0610",
    site: "HUB_1651",
    returnedBatteryId: "BAT_7414",
    installedBatteryId: "BAT_5326",
    chargeState: "87",
    revenue: "12733",
    timestamp: "2025-07-01T12:43:26.219Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74426",
    userId: "US_6607",
    site: "HUB_0611",
    returnedBatteryId: "BAT_4282",
    installedBatteryId: "BAT_8510",
    chargeState: "97",
    revenue: "19101",
    timestamp: "2025-07-01T14:08:07.466Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74427",
    userId: "US_3823",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4455",
    installedBatteryId: "BAT_8883",
    chargeState: "99",
    revenue: "13574",
    timestamp: "2025-07-01T15:52:30.351Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74428",
    userId: "US_1865",
    site: "HUB_7789",
    returnedBatteryId: "BAT_6760",
    installedBatteryId: "BAT_4650",
    chargeState: "90",
    revenue: "13825",
    timestamp: "2025-07-01T19:09:57.457Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74429",
    userId: "US_9006",
    site: "HUB_0611",
    returnedBatteryId: "BAT_5418",
    installedBatteryId: "BAT_2292",
    chargeState: "96",
    revenue: "15958",
    timestamp: "2025-07-01T19:22:19.437Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74430",
    userId: "US_0171",
    site: "HUB_8188",
    returnedBatteryId: "BAT_6200",
    installedBatteryId: "BAT_1726",
    chargeState: "92",
    revenue: "17931",
    timestamp: "2025-07-01T20:05:00.225Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74431",
    userId: "US_9075",
    site: "HUB_8778",
    returnedBatteryId: "BAT_2158",
    installedBatteryId: "BAT_5355",
    chargeState: "91",
    revenue: "18128",
    timestamp: "2025-07-01T21:17:49.955Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74432",
    userId: "US_7624",
    site: "HUB_8778",
    returnedBatteryId: "BAT_3801",
    installedBatteryId: "BAT_6417",
    chargeState: "98",
    revenue: "17722",
    timestamp: "2025-07-01T21:46:46.039Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74433",
    userId: "US_7217",
    site: "HUB_8778",
    returnedBatteryId: "BAT_2003",
    installedBatteryId: "BAT_5554",
    chargeState: "98",
    revenue: "10709",
    timestamp: "2025-07-01T23:56:14.305Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74434",
    userId: "US_6253",
    site: "HUB_8188",
    returnedBatteryId: "BAT_7660",
    installedBatteryId: "BAT_9366",
    chargeState: "97",
    revenue: "18687",
    timestamp: "2025-07-02T01:26:19.419Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74435",
    userId: "US_4852",
    site: "HUB_7188",
    returnedBatteryId: "BAT_6294",
    installedBatteryId: "BAT_1454",
    chargeState: "93",
    revenue: "18717",
    timestamp: "2025-07-02T05:22:32.802Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74436",
    userId: "US_9514",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8897",
    installedBatteryId: "BAT_8637",
    chargeState: "94",
    revenue: "15282",
    timestamp: "2025-07-02T05:27:11.374Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74524",
    userId: "US_6544",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2070",
    installedBatteryId: "BAT_8500",
    chargeState: "97",
    revenue: "10388",
    timestamp: "2025-07-03T06:39:02.610Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74525",
    userId: "US_9297",
    site: "HUB_0466",
    returnedBatteryId: "BAT_440",
    installedBatteryId: "BAT_1785",
    chargeState: "97",
    revenue: "19525",
    timestamp: "2025-07-03T10:29:06.741Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74526",
    userId: "US_8835",
    site: "HUB_0611",
    returnedBatteryId: "BAT_9120",
    installedBatteryId: "BAT_2829",
    chargeState: "99",
    revenue: "14235",
    timestamp: "2025-07-03T12:10:32.019Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74527",
    userId: "US_8441",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5005",
    installedBatteryId: "BAT_9509",
    chargeState: "98",
    revenue: "11419",
    timestamp: "2025-07-03T16:17:30.664Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74528",
    userId: "US_8743",
    site: "HUB_0466",
    returnedBatteryId: "BAT_2469",
    installedBatteryId: "BAT_6959",
    chargeState: "90",
    revenue: "10996",
    timestamp: "2025-07-03T18:06:08.974Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74529",
    userId: "US_9908",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1274",
    installedBatteryId: "BAT_1748",
    chargeState: "87",
    revenue: "10374",
    timestamp: "2025-07-03T19:45:34.341Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74530",
    userId: "US_0511",
    site: "HUB_4273",
    returnedBatteryId: "BAT_9385",
    installedBatteryId: "BAT_4272",
    chargeState: "93",
    revenue: "10773",
    timestamp: "2025-07-04T00:30:39.263Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74531",
    userId: "US_5892",
    site: "HUB_0611",
    returnedBatteryId: "BAT_1643",
    installedBatteryId: "BAT_5465",
    chargeState: "88",
    revenue: "15327",
    timestamp: "2025-07-04T04:13:17.942Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74532",
    userId: "US_7868",
    site: "HUB_9097",
    returnedBatteryId: "BAT_2489",
    installedBatteryId: "BAT_4632",
    chargeState: "96",
    revenue: "19045",
    timestamp: "2025-07-04T07:57:17.186Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74533",
    userId: "US_8269",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8804",
    installedBatteryId: "BAT_5720",
    chargeState: "97",
    revenue: "17387",
    timestamp: "2025-07-04T12:28:24.940Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74534",
    userId: "US_7322",
    site: "HUB_7789",
    returnedBatteryId: "BAT_7997",
    installedBatteryId: "BAT_9518",
    chargeState: "95",
    revenue: "16265",
    timestamp: "2025-07-04T15:27:03.173Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74535",
    userId: "US_9512",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7018",
    installedBatteryId: "BAT_3083",
    chargeState: "91",
    revenue: "14872",
    timestamp: "2025-07-04T20:05:05.278Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74536",
    userId: "US_5476",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9037",
    installedBatteryId: "BAT_7023",
    chargeState: "99",
    revenue: "16464",
    timestamp: "2025-07-04T23:56:00.832Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74537",
    userId: "US_4205",
    site: "HUB_4273",
    returnedBatteryId: "BAT_9373",
    installedBatteryId: "BAT_3127",
    chargeState: "97",
    revenue: "18130",
    timestamp: "2025-07-05T00:41:04.938Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74624",
    userId: "US_6006",
    site: "HUB_9097",
    returnedBatteryId: "BAT_1481",
    installedBatteryId: "BAT_4966",
    chargeState: "95",
    revenue: "11666",
    timestamp: "2025-07-05T09:03:01.162Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74625",
    userId: "US_8276",
    site: "HUB_0466",
    returnedBatteryId: "BAT_5173",
    installedBatteryId: "BAT_3446",
    chargeState: "88",
    revenue: "14605",
    timestamp: "2025-07-05T09:52:59.771Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74626",
    userId: "US_5227",
    site: "HUB_0611",
    returnedBatteryId: "BAT_5556",
    installedBatteryId: "BAT_6925",
    chargeState: "88",
    revenue: "10054",
    timestamp: "2025-07-05T12:08:45.376Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74627",
    userId: "US_2839",
    site: "HUB_9097",
    returnedBatteryId: "BAT_5563",
    installedBatteryId: "BAT_7422",
    chargeState: "99",
    revenue: "13689",
    timestamp: "2025-07-05T13:22:45.867Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74628",
    userId: "US_8522",
    site: "HUB_0466",
    returnedBatteryId: "BAT_7750",
    installedBatteryId: "BAT_5307",
    chargeState: "96",
    revenue: "12927",
    timestamp: "2025-07-05T15:35:32.201Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74629",
    userId: "US_0640",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3186",
    installedBatteryId: "BAT_2489",
    chargeState: "94",
    revenue: "16867",
    timestamp: "2025-07-05T19:37:52.356Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74630",
    userId: "US_2288",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8673",
    installedBatteryId: "BAT_159",
    chargeState: "97",
    revenue: "15100",
    timestamp: "2025-07-05T20:11:30.036Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74631",
    userId: "US_2876",
    site: "HUB_8188",
    returnedBatteryId: "BAT_9388",
    installedBatteryId: "BAT_4872",
    chargeState: "96",
    revenue: "14044",
    timestamp: "2025-07-06T00:33:44.821Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74632",
    userId: "US_2498",
    site: "HUB_0611",
    returnedBatteryId: "BAT_3478",
    installedBatteryId: "BAT_2158",
    chargeState: "99",
    revenue: "10004",
    timestamp: "2025-07-06T01:11:12.734Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74633",
    userId: "US_5582",
    site: "HUB_4273",
    returnedBatteryId: "BAT_660",
    installedBatteryId: "BAT_8082",
    chargeState: "96",
    revenue: "16882",
    timestamp: "2025-07-06T01:21:04.795Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74634",
    userId: "US_8165",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2918",
    installedBatteryId: "BAT_5344",
    chargeState: "90",
    revenue: "10066",
    timestamp: "2025-07-06T04:50:39.148Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74635",
    userId: "US_1917",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2265",
    installedBatteryId: "BAT_9719",
    chargeState: "87",
    revenue: "11047",
    timestamp: "2025-07-06T06:52:20.175Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74636",
    userId: "US_4789",
    site: "HUB_1651",
    returnedBatteryId: "BAT_065",
    installedBatteryId: "BAT_3599",
    chargeState: "89",
    revenue: "10553",
    timestamp: "2025-07-06T11:29:20.230Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74637",
    userId: "US_7835",
    site: "HUB_7789",
    returnedBatteryId: "BAT_7294",
    installedBatteryId: "BAT_3071",
    chargeState: "89",
    revenue: "12407",
    timestamp: "2025-07-06T13:41:45.083Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74638",
    userId: "US_0439",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9633",
    installedBatteryId: "BAT_3348",
    chargeState: "92",
    revenue: "19570",
    timestamp: "2025-07-06T18:15:01.080Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74724",
    userId: "US_8660",
    site: "HUB_7789",
    returnedBatteryId: "BAT_1326",
    installedBatteryId: "BAT_3038",
    chargeState: "96",
    revenue: "19018",
    timestamp: "2025-07-07T08:14:20.196Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74725",
    userId: "US_6059",
    site: "HUB_1651",
    returnedBatteryId: "BAT_7085",
    installedBatteryId: "BAT_4235",
    chargeState: "90",
    revenue: "13957",
    timestamp: "2025-07-07T11:19:17.891Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74726",
    userId: "US_6223",
    site: "HUB_7188",
    returnedBatteryId: "BAT_8379",
    installedBatteryId: "BAT_8742",
    chargeState: "98",
    revenue: "13207",
    timestamp: "2025-07-07T13:52:00.111Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74727",
    userId: "US_3127",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7333",
    installedBatteryId: "BAT_7810",
    chargeState: "94",
    revenue: "19230",
    timestamp: "2025-07-07T14:17:03.360Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74728",
    userId: "US_2382",
    site: "HUB_0466",
    returnedBatteryId: "BAT_2373",
    installedBatteryId: "BAT_9695",
    chargeState: "98",
    revenue: "14230",
    timestamp: "2025-07-07T17:53:32.533Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74729",
    userId: "US_2648",
    site: "HUB_8188",
    returnedBatteryId: "BAT_4801",
    installedBatteryId: "BAT_3370",
    chargeState: "94",
    revenue: "15133",
    timestamp: "2025-07-07T18:57:49.020Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74730",
    userId: "US_9423",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2247",
    installedBatteryId: "BAT_4747",
    chargeState: "91",
    revenue: "17319",
    timestamp: "2025-07-07T23:01:57.462Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74731",
    userId: "US_3922",
    site: "HUB_8778",
    returnedBatteryId: "BAT_9064",
    installedBatteryId: "BAT_9977",
    chargeState: "89",
    revenue: "10184",
    timestamp: "2025-07-08T00:33:10.164Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74732",
    userId: "US_5533",
    site: "HUB_1651",
    returnedBatteryId: "BAT_4159",
    installedBatteryId: "BAT_6295",
    chargeState: "92",
    revenue: "15427",
    timestamp: "2025-07-08T03:05:21.537Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74733",
    userId: "US_0610",
    site: "HUB_9097",
    returnedBatteryId: "BAT_5326",
    installedBatteryId: "BAT_4282",
    chargeState: "88",
    revenue: "10872",
    timestamp: "2025-07-08T04:25:04.330Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74734",
    userId: "US_6607",
    site: "HUB_0466",
    returnedBatteryId: "BAT_8510",
    installedBatteryId: "BAT_7557",
    chargeState: "98",
    revenue: "16849",
    timestamp: "2025-07-08T06:39:52.984Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74735",
    userId: "US_3823",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8883",
    installedBatteryId: "BAT_2974",
    chargeState: "88",
    revenue: "13394",
    timestamp: "2025-07-08T08:39:51.968Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74736",
    userId: "US_1865",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4650",
    installedBatteryId: "BAT_1723",
    chargeState: "99",
    revenue: "11039",
    timestamp: "2025-07-08T12:06:42.105Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74737",
    userId: "US_9006",
    site: "HUB_0611",
    returnedBatteryId: "BAT_2292",
    installedBatteryId: "BAT_7333",
    chargeState: "92",
    revenue: "19068",
    timestamp: "2025-07-08T14:10:20.612Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74738",
    userId: "US_0171",
    site: "HUB_9196",
    returnedBatteryId: "BAT_1726",
    installedBatteryId: "BAT_8379",
    chargeState: "92",
    revenue: "14696",
    timestamp: "2025-07-08T16:55:35.598Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74739",
    userId: "US_9075",
    site: "HUB_8778",
    returnedBatteryId: "BAT_5355",
    installedBatteryId: "BAT_935",
    chargeState: "88",
    revenue: "19045",
    timestamp: "2025-07-08T18:57:06.358Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74824",
    userId: "US_7624",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6417",
    installedBatteryId: "BAT_2509",
    chargeState: "93",
    revenue: "16611",
    timestamp: "2025-07-09T06:29:20.285Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74825",
    userId: "US_7217",
    site: "HUB_9097",
    returnedBatteryId: "BAT_5554",
    installedBatteryId: "BAT_8429",
    chargeState: "96",
    revenue: "12941",
    timestamp: "2025-07-09T06:49:16.112Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74826",
    userId: "US_6253",
    site: "HUB_0466",
    returnedBatteryId: "BAT_9366",
    installedBatteryId: "BAT_5929",
    chargeState: "90",
    revenue: "10814",
    timestamp: "2025-07-09T09:54:06.864Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74827",
    userId: "US_4852",
    site: "HUB_7188",
    returnedBatteryId: "BAT_1454",
    installedBatteryId: "BAT_4638",
    chargeState: "96",
    revenue: "16864",
    timestamp: "2025-07-09T13:39:56.581Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74828",
    userId: "US_9514",
    site: "HUB_4273",
    returnedBatteryId: "BAT_8637",
    installedBatteryId: "BAT_3022",
    chargeState: "98",
    revenue: "13267",
    timestamp: "2025-07-09T17:59:50.934Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74829",
    userId: "US_6544",
    site: "HUB_4273",
    returnedBatteryId: "BAT_8500",
    installedBatteryId: "BAT_1203",
    chargeState: "88",
    revenue: "15764",
    timestamp: "2025-07-09T22:39:52.172Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74830",
    userId: "US_9297",
    site: "HUB_7188",
    returnedBatteryId: "BAT_1785",
    installedBatteryId: "BAT_276",
    chargeState: "90",
    revenue: "19665",
    timestamp: "2025-07-10T03:21:39.191Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74831",
    userId: "US_8835",
    site: "HUB_0466",
    returnedBatteryId: "BAT_2829",
    installedBatteryId: "BAT_6583",
    chargeState: "98",
    revenue: "10273",
    timestamp: "2025-07-10T06:50:19.481Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74832",
    userId: "US_8441",
    site: "HUB_4273",
    returnedBatteryId: "BAT_9509",
    installedBatteryId: "BAT_9042",
    chargeState: "92",
    revenue: "13812",
    timestamp: "2025-07-10T08:36:07.463Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74833",
    userId: "US_8743",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6959",
    installedBatteryId: "BAT_3800",
    chargeState: "97",
    revenue: "16574",
    timestamp: "2025-07-10T08:54:50.080Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74834",
    userId: "US_9908",
    site: "HUB_7188",
    returnedBatteryId: "BAT_1748",
    installedBatteryId: "BAT_8258",
    chargeState: "91",
    revenue: "14641",
    timestamp: "2025-07-10T10:53:04.162Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74835",
    userId: "US_0511",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4272",
    installedBatteryId: "BAT_6968",
    chargeState: "89",
    revenue: "10903",
    timestamp: "2025-07-10T13:25:27.451Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74836",
    userId: "US_5892",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5465",
    installedBatteryId: "BAT_5150",
    chargeState: "96",
    revenue: "10529",
    timestamp: "2025-07-10T14:54:10.810Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74837",
    userId: "US_7868",
    site: "HUB_9097",
    returnedBatteryId: "BAT_4632",
    installedBatteryId: "BAT_8042",
    chargeState: "88",
    revenue: "15610",
    timestamp: "2025-07-10T17:52:27.507Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74838",
    userId: "US_8269",
    site: "HUB_9196",
    returnedBatteryId: "BAT_5720",
    installedBatteryId: "BAT_5912",
    chargeState: "96",
    revenue: "12770",
    timestamp: "2025-07-10T21:28:31.339Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74839",
    userId: "US_7322",
    site: "HUB_8778",
    returnedBatteryId: "BAT_9518",
    installedBatteryId: "BAT_3526",
    chargeState: "93",
    revenue: "12508",
    timestamp: "2025-07-10T22:35:00.623Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74840",
    userId: "US_9512",
    site: "HUB_8778",
    returnedBatteryId: "BAT_3083",
    installedBatteryId: "BAT_8984",
    chargeState: "94",
    revenue: "19062",
    timestamp: "2025-07-10T23:12:48.829Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74924",
    userId: "US_5476",
    site: "HUB_4273",
    returnedBatteryId: "BAT_7023",
    installedBatteryId: "BAT_3575",
    chargeState: "89",
    revenue: "15347",
    timestamp: "2025-07-11T07:44:38.804Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74925",
    userId: "US_4205",
    site: "HUB_0611",
    returnedBatteryId: "BAT_3127",
    installedBatteryId: "BAT_5414",
    chargeState: "89",
    revenue: "11781",
    timestamp: "2025-07-11T08:10:59.540Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74926",
    userId: "US_6006",
    site: "HUB_8778",
    returnedBatteryId: "BAT_4966",
    installedBatteryId: "BAT_3212",
    chargeState: "94",
    revenue: "16524",
    timestamp: "2025-07-11T08:12:38.619Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74927",
    userId: "US_8276",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3446",
    installedBatteryId: "BAT_6200",
    chargeState: "92",
    revenue: "12099",
    timestamp: "2025-07-11T12:35:29.634Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74928",
    userId: "US_5227",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6925",
    installedBatteryId: "BAT_378",
    chargeState: "99",
    revenue: "11951",
    timestamp: "2025-07-11T14:50:17.373Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74929",
    userId: "US_2839",
    site: "HUB_4273",
    returnedBatteryId: "BAT_7422",
    installedBatteryId: "BAT_8804",
    chargeState: "89",
    revenue: "15086",
    timestamp: "2025-07-11T18:38:55.482Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74930",
    userId: "US_8522",
    site: "HUB_7789",
    returnedBatteryId: "BAT_5307",
    installedBatteryId: "BAT_645",
    chargeState: "92",
    revenue: "10110",
    timestamp: "2025-07-11T21:41:26.019Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74931",
    userId: "US_0640",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2489",
    installedBatteryId: "BAT_6335",
    chargeState: "95",
    revenue: "19040",
    timestamp: "2025-07-11T22:43:09.822Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74932",
    userId: "US_2288",
    site: "HUB_1651",
    returnedBatteryId: "BAT_159",
    installedBatteryId: "BAT_7263",
    chargeState: "89",
    revenue: "19423",
    timestamp: "2025-07-12T01:29:20.869Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_74933",
    userId: "US_2876",
    site: "HUB_8778",
    returnedBatteryId: "BAT_4872",
    installedBatteryId: "BAT_5556",
    chargeState: "99",
    revenue: "16896",
    timestamp: "2025-07-12T05:16:41.842Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74934",
    userId: "US_2498",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2158",
    installedBatteryId: "BAT_2471",
    chargeState: "95",
    revenue: "17996",
    timestamp: "2025-07-12T09:30:01.350Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_74935",
    userId: "US_5582",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8082",
    installedBatteryId: "BAT_2158",
    chargeState: "88",
    revenue: "10391",
    timestamp: "2025-07-12T12:14:04.357Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74936",
    userId: "US_8165",
    site: "HUB_4273",
    returnedBatteryId: "BAT_5344",
    installedBatteryId: "BAT_5807",
    chargeState: "93",
    revenue: "10801",
    timestamp: "2025-07-12T13:28:09.635Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74937",
    userId: "US_1917",
    site: "HUB_8188",
    returnedBatteryId: "BAT_9719",
    installedBatteryId: "BAT_7956",
    chargeState: "91",
    revenue: "18779",
    timestamp: "2025-07-12T14:37:14.815Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74938",
    userId: "US_4789",
    site: "HUB_8778",
    returnedBatteryId: "BAT_3599",
    installedBatteryId: "BAT_2373",
    chargeState: "96",
    revenue: "15877",
    timestamp: "2025-07-12T15:46:25.266Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_74939",
    userId: "US_7835",
    site: "HUB_8778",
    returnedBatteryId: "BAT_3071",
    installedBatteryId: "BAT_3083",
    chargeState: "90",
    revenue: "16313",
    timestamp: "2025-07-12T18:56:42.735Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74940",
    userId: "US_0439",
    site: "HUB_8188",
    returnedBatteryId: "BAT_3348",
    installedBatteryId: "BAT_3766",
    chargeState: "90",
    revenue: "15141",
    timestamp: "2025-07-12T22:57:16.651Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_74941",
    userId: "US_8660",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3038",
    installedBatteryId: "BAT_2929",
    chargeState: "87",
    revenue: "18544",
    timestamp: "2025-07-12T23:40:10.797Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75024",
    userId: "US_6059",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4235",
    installedBatteryId: "BAT_4607",
    chargeState: "98",
    revenue: "13168",
    timestamp: "2025-07-13T05:56:22.583Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75025",
    userId: "US_6223",
    site: "HUB_0611",
    returnedBatteryId: "BAT_8742",
    installedBatteryId: "BAT_6236",
    chargeState: "94",
    revenue: "16213",
    timestamp: "2025-07-13T07:18:28.508Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75026",
    userId: "US_3127",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7810",
    installedBatteryId: "BAT_4980",
    chargeState: "95",
    revenue: "18303",
    timestamp: "2025-07-13T10:43:51.759Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75027",
    userId: "US_2382",
    site: "HUB_0466",
    returnedBatteryId: "BAT_9695",
    installedBatteryId: "BAT_7076",
    chargeState: "98",
    revenue: "13774",
    timestamp: "2025-07-13T14:30:38.441Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75028",
    userId: "US_2648",
    site: "HUB_7789",
    returnedBatteryId: "BAT_3370",
    installedBatteryId: "BAT_8744",
    chargeState: "94",
    revenue: "10296",
    timestamp: "2025-07-13T17:57:04.392Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75029",
    userId: "US_9423",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4747",
    installedBatteryId: "BAT_1643",
    chargeState: "95",
    revenue: "17848",
    timestamp: "2025-07-13T21:32:15.551Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75030",
    userId: "US_3922",
    site: "HUB_0466",
    returnedBatteryId: "BAT_9977",
    installedBatteryId: "BAT_2289",
    chargeState: "87",
    revenue: "14846",
    timestamp: "2025-07-13T23:07:10.613Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75031",
    userId: "US_5533",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6295",
    installedBatteryId: "BAT_5554",
    chargeState: "96",
    revenue: "19099",
    timestamp: "2025-07-14T02:35:30.785Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75032",
    userId: "US_0610",
    site: "HUB_0611",
    returnedBatteryId: "BAT_4282",
    installedBatteryId: "BAT_5077",
    chargeState: "91",
    revenue: "12398",
    timestamp: "2025-07-14T06:43:34.901Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75033",
    userId: "US_6607",
    site: "HUB_0466",
    returnedBatteryId: "BAT_7557",
    installedBatteryId: "BAT_5563",
    chargeState: "88",
    revenue: "15235",
    timestamp: "2025-07-14T07:19:20.942Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75034",
    userId: "US_3823",
    site: "HUB_9196",
    returnedBatteryId: "BAT_2974",
    installedBatteryId: "BAT_2882",
    chargeState: "96",
    revenue: "12058",
    timestamp: "2025-07-14T09:40:36.858Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75035",
    userId: "US_1865",
    site: "HUB_4273",
    returnedBatteryId: "BAT_1723",
    installedBatteryId: "BAT_6357",
    chargeState: "98",
    revenue: "16288",
    timestamp: "2025-07-14T11:17:50.542Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75036",
    userId: "US_9006",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7333",
    installedBatteryId: "BAT_9217",
    chargeState: "97",
    revenue: "15478",
    timestamp: "2025-07-14T12:39:27.270Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75037",
    userId: "US_0171",
    site: "HUB_0466",
    returnedBatteryId: "BAT_8379",
    installedBatteryId: "BAT_10000",
    chargeState: "92",
    revenue: "12570",
    timestamp: "2025-07-14T15:44:00.912Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75038",
    userId: "US_9075",
    site: "HUB_9097",
    returnedBatteryId: "BAT_935",
    installedBatteryId: "BAT_5720",
    chargeState: "98",
    revenue: "14625",
    timestamp: "2025-07-14T20:00:44.298Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75039",
    userId: "US_7624",
    site: "HUB_8778",
    returnedBatteryId: "BAT_2509",
    installedBatteryId: "BAT_6092",
    chargeState: "88",
    revenue: "19757",
    timestamp: "2025-07-14T23:51:44.395Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75040",
    userId: "US_7217",
    site: "HUB_9196",
    returnedBatteryId: "BAT_8429",
    installedBatteryId: "BAT_4059",
    chargeState: "88",
    revenue: "17344",
    timestamp: "2025-07-14T23:55:06.563Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75041",
    userId: "US_6253",
    site: "HUB_9097",
    returnedBatteryId: "BAT_5929",
    installedBatteryId: "BAT_6506",
    chargeState: "89",
    revenue: "19056",
    timestamp: "2025-07-15T03:01:18.698Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75042",
    userId: "US_4852",
    site: "HUB_0611",
    returnedBatteryId: "BAT_4638",
    installedBatteryId: "BAT_938",
    chargeState: "90",
    revenue: "10296",
    timestamp: "2025-07-15T04:42:05.097Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75124",
    userId: "US_9514",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3022",
    installedBatteryId: "BAT_4971",
    chargeState: "97",
    revenue: "13834",
    timestamp: "2025-07-15T05:09:33.495Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75125",
    userId: "US_6544",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1203",
    installedBatteryId: "BAT_3484",
    chargeState: "88",
    revenue: "14220",
    timestamp: "2025-07-15T06:15:26.291Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75126",
    userId: "US_9297",
    site: "HUB_9097",
    returnedBatteryId: "BAT_276",
    installedBatteryId: "BAT_268",
    chargeState: "91",
    revenue: "17861",
    timestamp: "2025-07-15T10:31:07.931Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75127",
    userId: "US_8835",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6583",
    installedBatteryId: "BAT_7346",
    chargeState: "95",
    revenue: "11796",
    timestamp: "2025-07-15T13:05:49.651Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75128",
    userId: "US_8441",
    site: "HUB_9097",
    returnedBatteryId: "BAT_9042",
    installedBatteryId: "BAT_660",
    chargeState: "93",
    revenue: "16777",
    timestamp: "2025-07-15T17:16:26.189Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75129",
    userId: "US_8743",
    site: "HUB_9097",
    returnedBatteryId: "BAT_3800",
    installedBatteryId: "BAT_8158",
    chargeState: "99",
    revenue: "19022",
    timestamp: "2025-07-15T21:57:53.076Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75130",
    userId: "US_9908",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8258",
    installedBatteryId: "BAT_9385",
    chargeState: "91",
    revenue: "16140",
    timestamp: "2025-07-16T00:41:08.593Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75131",
    userId: "US_0511",
    site: "HUB_9196",
    returnedBatteryId: "BAT_6968",
    installedBatteryId: "BAT_9388",
    chargeState: "98",
    revenue: "11808",
    timestamp: "2025-07-16T03:43:26.791Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75132",
    userId: "US_5892",
    site: "HUB_9196",
    returnedBatteryId: "BAT_5150",
    installedBatteryId: "BAT_2782",
    chargeState: "88",
    revenue: "14079",
    timestamp: "2025-07-16T04:40:58.985Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75133",
    userId: "US_7868",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8042",
    installedBatteryId: "BAT_8908",
    chargeState: "89",
    revenue: "10933",
    timestamp: "2025-07-16T06:59:54.649Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75134",
    userId: "US_8269",
    site: "HUB_8188",
    returnedBatteryId: "BAT_5912",
    installedBatteryId: "BAT_1785",
    chargeState: "87",
    revenue: "14301",
    timestamp: "2025-07-16T08:56:12.940Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75135",
    userId: "US_7322",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3526",
    installedBatteryId: "BAT_1723",
    chargeState: "90",
    revenue: "16343",
    timestamp: "2025-07-16T11:43:07.636Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75136",
    userId: "US_9512",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8984",
    installedBatteryId: "BAT_5763",
    chargeState: "97",
    revenue: "17748",
    timestamp: "2025-07-16T12:47:04.358Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75137",
    userId: "US_5476",
    site: "HUB_7188",
    returnedBatteryId: "BAT_3575",
    installedBatteryId: "BAT_3446",
    chargeState: "93",
    revenue: "15161",
    timestamp: "2025-07-16T14:50:49.944Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75138",
    userId: "US_4205",
    site: "HUB_7789",
    returnedBatteryId: "BAT_5414",
    installedBatteryId: "BAT_3190",
    chargeState: "95",
    revenue: "17981",
    timestamp: "2025-07-16T19:24:47.331Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75139",
    userId: "US_6006",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3212",
    installedBatteryId: "BAT_5142",
    chargeState: "91",
    revenue: "15891",
    timestamp: "2025-07-16T22:55:03.453Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75140",
    userId: "US_8276",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6200",
    installedBatteryId: "BAT_4541",
    chargeState: "95",
    revenue: "15675",
    timestamp: "2025-07-17T01:31:30.300Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75141",
    userId: "US_5227",
    site: "HUB_4273",
    returnedBatteryId: "BAT_378",
    installedBatteryId: "BAT_317",
    chargeState: "95",
    revenue: "12991",
    timestamp: "2025-07-17T04:34:19.458Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75142",
    userId: "US_2839",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8804",
    installedBatteryId: "BAT_5228",
    chargeState: "98",
    revenue: "10540",
    timestamp: "2025-07-17T05:32:07.414Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75143",
    userId: "US_8522",
    site: "HUB_8778",
    returnedBatteryId: "BAT_645",
    installedBatteryId: "BAT_7337",
    chargeState: "96",
    revenue: "12008",
    timestamp: "2025-07-17T09:02:37.230Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75225",
    userId: "US_0640",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6335",
    installedBatteryId: "BAT_4878",
    chargeState: "93",
    revenue: "15582",
    timestamp: "2025-07-18T07:03:25.722Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75226",
    userId: "US_2288",
    site: "HUB_9097",
    returnedBatteryId: "BAT_7263",
    installedBatteryId: "BAT_7085",
    chargeState: "96",
    revenue: "10120",
    timestamp: "2025-07-18T09:21:56.945Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75227",
    userId: "US_2876",
    site: "HUB_9097",
    returnedBatteryId: "BAT_5556",
    installedBatteryId: "BAT_4222",
    chargeState: "97",
    revenue: "13268",
    timestamp: "2025-07-18T09:49:22.143Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75228",
    userId: "US_2498",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2471",
    installedBatteryId: "BAT_8897",
    chargeState: "93",
    revenue: "14875",
    timestamp: "2025-07-18T11:08:49.172Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75229",
    userId: "US_5582",
    site: "HUB_9196",
    returnedBatteryId: "BAT_2158",
    installedBatteryId: "BAT_5005",
    chargeState: "94",
    revenue: "11438",
    timestamp: "2025-07-18T13:26:23.691Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75230",
    userId: "US_8165",
    site: "HUB_8188",
    returnedBatteryId: "BAT_5807",
    installedBatteryId: "BAT_3348",
    chargeState: "88",
    revenue: "14575",
    timestamp: "2025-07-18T14:09:18.654Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75231",
    userId: "US_1917",
    site: "HUB_1651",
    returnedBatteryId: "BAT_7956",
    installedBatteryId: "BAT_8413",
    chargeState: "97",
    revenue: "15040",
    timestamp: "2025-07-18T16:50:46.197Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75232",
    userId: "US_4789",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2373",
    installedBatteryId: "BAT_322",
    chargeState: "91",
    revenue: "17190",
    timestamp: "2025-07-18T19:41:40.579Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75233",
    userId: "US_7835",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3083",
    installedBatteryId: "BAT_8539",
    chargeState: "95",
    revenue: "19452",
    timestamp: "2025-07-18T21:57:46.289Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75234",
    userId: "US_0439",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3766",
    installedBatteryId: "BAT_441",
    chargeState: "97",
    revenue: "14169",
    timestamp: "2025-07-18T23:34:34.032Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75235",
    userId: "US_8660",
    site: "HUB_0611",
    returnedBatteryId: "BAT_2929",
    installedBatteryId: "BAT_2158",
    chargeState: "98",
    revenue: "10479",
    timestamp: "2025-07-19T01:05:57.069Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75236",
    userId: "US_6059",
    site: "HUB_0611",
    returnedBatteryId: "BAT_4607",
    installedBatteryId: "BAT_6967",
    chargeState: "94",
    revenue: "19908",
    timestamp: "2025-07-19T02:20:32.192Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75237",
    userId: "US_6223",
    site: "HUB_0466",
    returnedBatteryId: "BAT_6236",
    installedBatteryId: "BAT_065",
    chargeState: "92",
    revenue: "14187",
    timestamp: "2025-07-19T06:09:59.213Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75238",
    userId: "US_3127",
    site: "HUB_0466",
    returnedBatteryId: "BAT_4980",
    installedBatteryId: "BAT_4903",
    chargeState: "95",
    revenue: "12205",
    timestamp: "2025-07-19T09:22:43.565Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75239",
    userId: "US_2382",
    site: "HUB_9097",
    returnedBatteryId: "BAT_7076",
    installedBatteryId: "BAT_7581",
    chargeState: "88",
    revenue: "12634",
    timestamp: "2025-07-19T09:55:53.943Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75240",
    userId: "US_2648",
    site: "HUB_1651",
    returnedBatteryId: "BAT_8744",
    installedBatteryId: "BAT_8189",
    chargeState: "97",
    revenue: "10447",
    timestamp: "2025-07-19T12:04:48.022Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75241",
    userId: "US_9423",
    site: "HUB_7789",
    returnedBatteryId: "BAT_1643",
    installedBatteryId: "BAT_1078",
    chargeState: "93",
    revenue: "11553",
    timestamp: "2025-07-19T14:35:52.330Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75242",
    userId: "US_3922",
    site: "HUB_0466",
    returnedBatteryId: "BAT_2289",
    installedBatteryId: "BAT_5413",
    chargeState: "94",
    revenue: "15962",
    timestamp: "2025-07-19T17:05:50.092Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75243",
    userId: "US_5533",
    site: "HUB_9097",
    returnedBatteryId: "BAT_5554",
    installedBatteryId: "BAT_3721",
    chargeState: "88",
    revenue: "15299",
    timestamp: "2025-07-19T17:16:11.119Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75244",
    userId: "US_0610",
    site: "HUB_8188",
    returnedBatteryId: "BAT_5077",
    installedBatteryId: "BAT_2449",
    chargeState: "95",
    revenue: "10871",
    timestamp: "2025-07-19T20:18:48.952Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75245",
    userId: "US_6607",
    site: "HUB_9196",
    returnedBatteryId: "BAT_5563",
    installedBatteryId: "BAT_5214",
    chargeState: "87",
    revenue: "17864",
    timestamp: "2025-07-19T21:17:59.576Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75246",
    userId: "US_3823",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2882",
    installedBatteryId: "BAT_135",
    chargeState: "92",
    revenue: "18697",
    timestamp: "2025-07-19T23:18:27.446Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75325",
    userId: "US_1865",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6357",
    installedBatteryId: "BAT_2469",
    chargeState: "96",
    revenue: "13898",
    timestamp: "2025-07-20T06:53:14.366Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75326",
    userId: "US_9006",
    site: "HUB_1651",
    returnedBatteryId: "BAT_9217",
    installedBatteryId: "BAT_2877",
    chargeState: "95",
    revenue: "17133",
    timestamp: "2025-07-20T09:54:31.748Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75327",
    userId: "US_0171",
    site: "HUB_7789",
    returnedBatteryId: "BAT_10000",
    installedBatteryId: "BAT_5161",
    chargeState: "90",
    revenue: "19751",
    timestamp: "2025-07-20T12:51:23.172Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75328",
    userId: "US_9075",
    site: "HUB_8188",
    returnedBatteryId: "BAT_5720",
    installedBatteryId: "BAT_3270",
    chargeState: "90",
    revenue: "18258",
    timestamp: "2025-07-20T13:58:49.000Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75329",
    userId: "US_7624",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6092",
    installedBatteryId: "BAT_7997",
    chargeState: "87",
    revenue: "19544",
    timestamp: "2025-07-20T17:58:05.268Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75330",
    userId: "US_7217",
    site: "HUB_8188",
    returnedBatteryId: "BAT_4059",
    installedBatteryId: "BAT_7023",
    chargeState: "89",
    revenue: "19965",
    timestamp: "2025-07-20T19:14:26.436Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75331",
    userId: "US_6253",
    site: "HUB_0611",
    returnedBatteryId: "BAT_6506",
    installedBatteryId: "BAT_4530",
    chargeState: "87",
    revenue: "16736",
    timestamp: "2025-07-20T19:36:14.742Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75332",
    userId: "US_4852",
    site: "HUB_9097",
    returnedBatteryId: "BAT_938",
    installedBatteryId: "BAT_2265",
    chargeState: "97",
    revenue: "18432",
    timestamp: "2025-07-20T20:59:14.319Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75333",
    userId: "US_9514",
    site: "HUB_0611",
    returnedBatteryId: "BAT_4971",
    installedBatteryId: "BAT_8421",
    chargeState: "93",
    revenue: "15237",
    timestamp: "2025-07-21T00:30:56.434Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75334",
    userId: "US_6544",
    site: "HUB_7188",
    returnedBatteryId: "BAT_3484",
    installedBatteryId: "BAT_1643",
    chargeState: "96",
    revenue: "18447",
    timestamp: "2025-07-21T00:56:46.404Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75335",
    userId: "US_9297",
    site: "HUB_9196",
    returnedBatteryId: "BAT_268",
    installedBatteryId: "BAT_5807",
    chargeState: "89",
    revenue: "15639",
    timestamp: "2025-07-21T03:52:26.367Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75336",
    userId: "US_8835",
    site: "HUB_4273",
    returnedBatteryId: "BAT_7346",
    installedBatteryId: "BAT_5361",
    chargeState: "98",
    revenue: "14724",
    timestamp: "2025-07-21T03:56:00.744Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75337",
    userId: "US_8441",
    site: "HUB_8778",
    returnedBatteryId: "BAT_660",
    installedBatteryId: "BAT_218",
    chargeState: "99",
    revenue: "14610",
    timestamp: "2025-07-21T06:38:49.198Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75338",
    userId: "US_8743",
    site: "HUB_1651",
    returnedBatteryId: "BAT_8158",
    installedBatteryId: "BAT_1096",
    chargeState: "89",
    revenue: "16349",
    timestamp: "2025-07-21T07:24:55.942Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75339",
    userId: "US_9908",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9385",
    installedBatteryId: "BAT_1748",
    chargeState: "87",
    revenue: "19912",
    timestamp: "2025-07-21T09:45:24.710Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75340",
    userId: "US_0511",
    site: "HUB_8778",
    returnedBatteryId: "BAT_9388",
    installedBatteryId: "BAT_2976",
    chargeState: "98",
    revenue: "17393",
    timestamp: "2025-07-21T13:29:26.739Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75341",
    userId: "US_5892",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2782",
    installedBatteryId: "BAT_4025",
    chargeState: "96",
    revenue: "13306",
    timestamp: "2025-07-21T16:59:14.354Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75342",
    userId: "US_7868",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8908",
    installedBatteryId: "BAT_9366",
    chargeState: "92",
    revenue: "19321",
    timestamp: "2025-07-21T19:11:03.980Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75343",
    userId: "US_8269",
    site: "HUB_0611",
    returnedBatteryId: "BAT_1785",
    installedBatteryId: "BAT_7007",
    chargeState: "87",
    revenue: "19454",
    timestamp: "2025-07-21T19:37:28.278Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75344",
    userId: "US_7322",
    site: "HUB_9097",
    returnedBatteryId: "BAT_1723",
    installedBatteryId: "BAT_3326",
    chargeState: "92",
    revenue: "15785",
    timestamp: "2025-07-21T22:17:38.578Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75345",
    userId: "US_9512",
    site: "HUB_8188",
    returnedBatteryId: "BAT_5763",
    installedBatteryId: "BAT_5554",
    chargeState: "91",
    revenue: "16811",
    timestamp: "2025-07-21T23:01:12.477Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75346",
    userId: "US_5476",
    site: "HUB_7789",
    returnedBatteryId: "BAT_3446",
    installedBatteryId: "BAT_6959",
    chargeState: "88",
    revenue: "16684",
    timestamp: "2025-07-21T23:31:33.265Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75347",
    userId: "US_4205",
    site: "HUB_8188",
    returnedBatteryId: "BAT_3190",
    installedBatteryId: "BAT_9909",
    chargeState: "98",
    revenue: "10364",
    timestamp: "2025-07-22T00:08:12.751Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75425",
    userId: "US_6006",
    site: "HUB_0466",
    returnedBatteryId: "BAT_5142",
    installedBatteryId: "BAT_2070",
    chargeState: "96",
    revenue: "16361",
    timestamp: "2025-07-22T08:24:34.314Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75426",
    userId: "US_8276",
    site: "HUB_1651",
    returnedBatteryId: "BAT_4541",
    installedBatteryId: "BAT_3673",
    chargeState: "95",
    revenue: "18824",
    timestamp: "2025-07-22T09:59:08.327Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75427",
    userId: "US_5227",
    site: "HUB_0466",
    returnedBatteryId: "BAT_317",
    installedBatteryId: "BAT_7974",
    chargeState: "96",
    revenue: "15513",
    timestamp: "2025-07-22T12:16:41.045Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75428",
    userId: "US_2839",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5228",
    installedBatteryId: "BAT_8637",
    chargeState: "90",
    revenue: "12823",
    timestamp: "2025-07-22T15:37:35.741Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75429",
    userId: "US_8522",
    site: "HUB_0611",
    returnedBatteryId: "BAT_7337",
    installedBatteryId: "BAT_5763",
    chargeState: "87",
    revenue: "10767",
    timestamp: "2025-07-22T18:58:18.130Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75430",
    userId: "US_0640",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4878",
    installedBatteryId: "BAT_8744",
    chargeState: "94",
    revenue: "16116",
    timestamp: "2025-07-22T20:21:09.771Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75431",
    userId: "US_2288",
    site: "HUB_1651",
    returnedBatteryId: "BAT_7085",
    installedBatteryId: "BAT_1109",
    chargeState: "97",
    revenue: "13236",
    timestamp: "2025-07-22T22:19:22.882Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75432",
    userId: "US_2876",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4222",
    installedBatteryId: "BAT_5845",
    chargeState: "99",
    revenue: "12190",
    timestamp: "2025-07-22T23:10:52.680Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75433",
    userId: "US_2498",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8897",
    installedBatteryId: "BAT_9733",
    chargeState: "96",
    revenue: "14342",
    timestamp: "2025-07-22T23:49:49.536Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75434",
    userId: "US_5582",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5005",
    installedBatteryId: "BAT_4747",
    chargeState: "87",
    revenue: "15339",
    timestamp: "2025-07-23T02:11:54.703Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75435",
    userId: "US_8165",
    site: "HUB_9196",
    returnedBatteryId: "BAT_3348",
    installedBatteryId: "BAT_1817",
    chargeState: "87",
    revenue: "12562",
    timestamp: "2025-07-23T02:39:16.538Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75436",
    userId: "US_1917",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8413",
    installedBatteryId: "BAT_1306",
    chargeState: "99",
    revenue: "17107",
    timestamp: "2025-07-23T03:23:11.823Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75437",
    userId: "US_4789",
    site: "HUB_8188",
    returnedBatteryId: "BAT_322",
    installedBatteryId: "BAT_8984",
    chargeState: "89",
    revenue: "15121",
    timestamp: "2025-07-23T03:34:30.731Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75438",
    userId: "US_7835",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8539",
    installedBatteryId: "BAT_8129",
    chargeState: "88",
    revenue: "13038",
    timestamp: "2025-07-23T05:01:58.594Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75439",
    userId: "US_0439",
    site: "HUB_1651",
    returnedBatteryId: "BAT_441",
    installedBatteryId: "BAT_7956",
    chargeState: "94",
    revenue: "10913",
    timestamp: "2025-07-23T05:10:55.310Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75440",
    userId: "US_8660",
    site: "HUB_9196",
    returnedBatteryId: "BAT_2158",
    installedBatteryId: "BAT_3575",
    chargeState: "95",
    revenue: "15763",
    timestamp: "2025-07-23T06:17:47.903Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75441",
    userId: "US_6059",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6967",
    installedBatteryId: "BAT_8337",
    chargeState: "97",
    revenue: "19088",
    timestamp: "2025-07-23T08:06:36.378Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75442",
    userId: "US_6223",
    site: "HUB_9097",
    returnedBatteryId: "BAT_065",
    installedBatteryId: "BAT_3478",
    chargeState: "93",
    revenue: "14729",
    timestamp: "2025-07-23T10:46:44.029Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75443",
    userId: "US_3127",
    site: "HUB_4273",
    returnedBatteryId: "BAT_4903",
    installedBatteryId: "BAT_378",
    chargeState: "99",
    revenue: "10666",
    timestamp: "2025-07-23T11:17:07.313Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75444",
    userId: "US_2382",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7581",
    installedBatteryId: "BAT_5143",
    chargeState: "92",
    revenue: "16805",
    timestamp: "2025-07-23T14:37:01.897Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75445",
    userId: "US_2648",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8189",
    installedBatteryId: "BAT_317",
    chargeState: "87",
    revenue: "17699",
    timestamp: "2025-07-23T18:10:08.708Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75446",
    userId: "US_9423",
    site: "HUB_4273",
    returnedBatteryId: "BAT_1078",
    installedBatteryId: "BAT_9730",
    chargeState: "93",
    revenue: "13185",
    timestamp: "2025-07-23T18:27:52.328Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75447",
    userId: "US_3922",
    site: "HUB_0611",
    returnedBatteryId: "BAT_5413",
    installedBatteryId: "BAT_159",
    chargeState: "88",
    revenue: "15203",
    timestamp: "2025-07-23T22:04:13.957Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75448",
    userId: "US_5533",
    site: "HUB_7188",
    returnedBatteryId: "BAT_3721",
    installedBatteryId: "BAT_7166",
    chargeState: "93",
    revenue: "16767",
    timestamp: "2025-07-23T23:24:12.383Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75525",
    userId: "US_0610",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2449",
    installedBatteryId: "BAT_8908",
    chargeState: "87",
    revenue: "12478",
    timestamp: "2025-07-24T08:30:23.891Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75526",
    userId: "US_6607",
    site: "HUB_0611",
    returnedBatteryId: "BAT_5214",
    installedBatteryId: "BAT_2292",
    chargeState: "90",
    revenue: "16152",
    timestamp: "2025-07-24T09:22:16.364Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75527",
    userId: "US_3823",
    site: "HUB_0611",
    returnedBatteryId: "BAT_135",
    installedBatteryId: "BAT_7692",
    chargeState: "90",
    revenue: "13579",
    timestamp: "2025-07-24T09:46:09.270Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75528",
    userId: "US_1865",
    site: "HUB_9097",
    returnedBatteryId: "BAT_2469",
    installedBatteryId: "BAT_7346",
    chargeState: "91",
    revenue: "12778",
    timestamp: "2025-07-24T11:24:28.054Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75529",
    userId: "US_9006",
    site: "HUB_9196",
    returnedBatteryId: "BAT_2877",
    installedBatteryId: "BAT_2877",
    chargeState: "96",
    revenue: "16976",
    timestamp: "2025-07-24T15:19:21.510Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75530",
    userId: "US_0171",
    site: "HUB_0466",
    returnedBatteryId: "BAT_5161",
    installedBatteryId: "BAT_2474",
    chargeState: "90",
    revenue: "10388",
    timestamp: "2025-07-24T17:15:50.685Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75531",
    userId: "US_9075",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3270",
    installedBatteryId: "BAT_2289",
    chargeState: "93",
    revenue: "19839",
    timestamp: "2025-07-24T18:26:07.345Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75532",
    userId: "US_7624",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7997",
    installedBatteryId: "BAT_057",
    chargeState: "99",
    revenue: "15236",
    timestamp: "2025-07-24T19:19:16.981Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75533",
    userId: "US_7217",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7023",
    installedBatteryId: "BAT_440",
    chargeState: "94",
    revenue: "17930",
    timestamp: "2025-07-24T19:32:17.071Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75534",
    userId: "US_6253",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4530",
    installedBatteryId: "BAT_8510",
    chargeState: "89",
    revenue: "14103",
    timestamp: "2025-07-24T19:56:09.102Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75535",
    userId: "US_4852",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2265",
    installedBatteryId: "BAT_5237",
    chargeState: "93",
    revenue: "15741",
    timestamp: "2025-07-24T21:21:21.259Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75536",
    userId: "US_9514",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8421",
    installedBatteryId: "BAT_4632",
    chargeState: "91",
    revenue: "12080",
    timestamp: "2025-07-25T00:39:41.415Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75537",
    userId: "US_6544",
    site: "HUB_7789",
    returnedBatteryId: "BAT_1643",
    installedBatteryId: "BAT_8158",
    chargeState: "87",
    revenue: "19803",
    timestamp: "2025-07-25T03:56:50.592Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75538",
    userId: "US_9297",
    site: "HUB_8188",
    returnedBatteryId: "BAT_5807",
    installedBatteryId: "BAT_768",
    chargeState: "96",
    revenue: "11265",
    timestamp: "2025-07-25T04:32:36.590Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75539",
    userId: "US_8835",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5361",
    installedBatteryId: "BAT_2302",
    chargeState: "92",
    revenue: "15676",
    timestamp: "2025-07-25T04:53:21.418Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75540",
    userId: "US_8441",
    site: "HUB_8778",
    returnedBatteryId: "BAT_218",
    installedBatteryId: "BAT_2545",
    chargeState: "96",
    revenue: "14627",
    timestamp: "2025-07-25T05:11:57.309Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75541",
    userId: "US_8743",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1096",
    installedBatteryId: "BAT_8742",
    chargeState: "99",
    revenue: "10693",
    timestamp: "2025-07-25T07:56:38.430Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75542",
    userId: "US_9908",
    site: "HUB_7188",
    returnedBatteryId: "BAT_1748",
    installedBatteryId: "BAT_9661",
    chargeState: "99",
    revenue: "15399",
    timestamp: "2025-07-25T07:59:13.147Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75543",
    userId: "US_0511",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2976",
    installedBatteryId: "BAT_8413",
    chargeState: "99",
    revenue: "14113",
    timestamp: "2025-07-25T09:59:03.113Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75544",
    userId: "US_5892",
    site: "HUB_1651",
    returnedBatteryId: "BAT_4025",
    installedBatteryId: "BAT_1614",
    chargeState: "97",
    revenue: "13688",
    timestamp: "2025-07-25T11:01:06.789Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75545",
    userId: "US_7868",
    site: "HUB_1651",
    returnedBatteryId: "BAT_9366",
    installedBatteryId: "BAT_6642",
    chargeState: "99",
    revenue: "14497",
    timestamp: "2025-07-25T13:50:55.607Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75546",
    userId: "US_8269",
    site: "HUB_8188",
    returnedBatteryId: "BAT_7007",
    installedBatteryId: "BAT_062",
    chargeState: "93",
    revenue: "19179",
    timestamp: "2025-07-25T17:26:02.468Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75547",
    userId: "US_7322",
    site: "HUB_9196",
    returnedBatteryId: "BAT_3326",
    installedBatteryId: "BAT_8820",
    chargeState: "98",
    revenue: "19054",
    timestamp: "2025-07-25T20:31:57.763Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75548",
    userId: "US_9512",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5554",
    installedBatteryId: "BAT_8806",
    chargeState: "90",
    revenue: "10635",
    timestamp: "2025-07-25T23:09:32.103Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75549",
    userId: "US_5476",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6959",
    installedBatteryId: "BAT_2041",
    chargeState: "89",
    revenue: "16656",
    timestamp: "2025-07-26T03:00:38.842Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75625",
    userId: "US_4205",
    site: "HUB_8188",
    returnedBatteryId: "BAT_9909",
    installedBatteryId: "BAT_2072",
    chargeState: "87",
    revenue: "14245",
    timestamp: "2025-07-26T07:57:13.199Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75626",
    userId: "US_6006",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2070",
    installedBatteryId: "BAT_8189",
    chargeState: "87",
    revenue: "13170",
    timestamp: "2025-07-26T11:17:04.530Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75627",
    userId: "US_8276",
    site: "HUB_7188",
    returnedBatteryId: "BAT_3673",
    installedBatteryId: "BAT_9217",
    chargeState: "97",
    revenue: "19482",
    timestamp: "2025-07-26T13:26:10.976Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75628",
    userId: "US_5227",
    site: "HUB_7188",
    returnedBatteryId: "BAT_7974",
    installedBatteryId: "BAT_5333",
    chargeState: "91",
    revenue: "11046",
    timestamp: "2025-07-26T14:08:54.333Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75629",
    userId: "US_2839",
    site: "HUB_0466",
    returnedBatteryId: "BAT_8637",
    installedBatteryId: "BAT_6378",
    chargeState: "97",
    revenue: "13846",
    timestamp: "2025-07-26T15:50:27.236Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75630",
    userId: "US_8522",
    site: "HUB_0466",
    returnedBatteryId: "BAT_5763",
    installedBatteryId: "BAT_4195",
    chargeState: "91",
    revenue: "12857",
    timestamp: "2025-07-26T17:37:15.734Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75631",
    userId: "US_0640",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8744",
    installedBatteryId: "BAT_1388",
    chargeState: "92",
    revenue: "12296",
    timestamp: "2025-07-26T20:49:39.437Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75632",
    userId: "US_2288",
    site: "HUB_7789",
    returnedBatteryId: "BAT_1109",
    installedBatteryId: "BAT_110",
    chargeState: "95",
    revenue: "12128",
    timestamp: "2025-07-26T21:26:41.558Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75633",
    userId: "US_2876",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5845",
    installedBatteryId: "BAT_2974",
    chargeState: "99",
    revenue: "14295",
    timestamp: "2025-07-26T23:19:38.823Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75634",
    userId: "US_2498",
    site: "HUB_9097",
    returnedBatteryId: "BAT_9733",
    installedBatteryId: "BAT_1591",
    chargeState: "89",
    revenue: "15492",
    timestamp: "2025-07-26T23:21:11.977Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75635",
    userId: "US_5582",
    site: "HUB_8188",
    returnedBatteryId: "BAT_4747",
    installedBatteryId: "BAT_9535",
    chargeState: "87",
    revenue: "19683",
    timestamp: "2025-07-27T01:37:33.378Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75636",
    userId: "US_8165",
    site: "HUB_9196",
    returnedBatteryId: "BAT_1817",
    installedBatteryId: "BAT_8379",
    chargeState: "94",
    revenue: "19473",
    timestamp: "2025-07-27T02:21:15.468Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75637",
    userId: "US_1917",
    site: "HUB_7789",
    returnedBatteryId: "BAT_1306",
    installedBatteryId: "BAT_8788",
    chargeState: "90",
    revenue: "19069",
    timestamp: "2025-07-27T03:29:32.980Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75638",
    userId: "US_4789",
    site: "HUB_9196",
    returnedBatteryId: "BAT_8984",
    installedBatteryId: "BAT_4541",
    chargeState: "92",
    revenue: "16334",
    timestamp: "2025-07-27T03:49:49.986Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75639",
    userId: "US_7835",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8129",
    installedBatteryId: "BAT_5912",
    chargeState: "87",
    revenue: "19144",
    timestamp: "2025-07-27T07:10:56.294Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75640",
    userId: "US_0439",
    site: "HUB_7789",
    returnedBatteryId: "BAT_7956",
    installedBatteryId: "BAT_1281",
    chargeState: "99",
    revenue: "19303",
    timestamp: "2025-07-27T10:00:27.312Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75641",
    userId: "US_8660",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3575",
    installedBatteryId: "BAT_4087",
    chargeState: "90",
    revenue: "11650",
    timestamp: "2025-07-27T11:22:22.084Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75642",
    userId: "US_6059",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8337",
    installedBatteryId: "BAT_6863",
    chargeState: "89",
    revenue: "14502",
    timestamp: "2025-07-27T12:52:50.177Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75643",
    userId: "US_6223",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3478",
    installedBatteryId: "BAT_5361",
    chargeState: "93",
    revenue: "18274",
    timestamp: "2025-07-27T13:51:56.024Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75644",
    userId: "US_3127",
    site: "HUB_0466",
    returnedBatteryId: "BAT_378",
    installedBatteryId: "BAT_5413",
    chargeState: "92",
    revenue: "11923",
    timestamp: "2025-07-27T16:52:06.067Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75645",
    userId: "US_2382",
    site: "HUB_7789",
    returnedBatteryId: "BAT_5143",
    installedBatteryId: "BAT_6228",
    chargeState: "90",
    revenue: "13233",
    timestamp: "2025-07-27T19:23:12.331Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75646",
    userId: "US_2648",
    site: "HUB_0611",
    returnedBatteryId: "BAT_317",
    installedBatteryId: "BAT_3882",
    chargeState: "99",
    revenue: "16468",
    timestamp: "2025-07-27T19:55:07.620Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75647",
    userId: "US_9423",
    site: "HUB_1651",
    returnedBatteryId: "BAT_9730",
    installedBatteryId: "BAT_8539",
    chargeState: "90",
    revenue: "17081",
    timestamp: "2025-07-27T23:54:31.090Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75648",
    userId: "US_3922",
    site: "HUB_0611",
    returnedBatteryId: "BAT_159",
    installedBatteryId: "BAT_7809",
    chargeState: "92",
    revenue: "14705",
    timestamp: "2025-07-28T02:37:51.442Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75649",
    userId: "US_5533",
    site: "HUB_9097",
    returnedBatteryId: "BAT_7166",
    installedBatteryId: "BAT_268",
    chargeState: "91",
    revenue: "14928",
    timestamp: "2025-07-28T06:31:10.628Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75650",
    userId: "US_0610",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8908",
    installedBatteryId: "BAT_6396",
    chargeState: "90",
    revenue: "18205",
    timestamp: "2025-07-28T09:30:01.240Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75725",
    userId: "US_6607",
    site: "HUB_8778",
    returnedBatteryId: "BAT_2292",
    installedBatteryId: "BAT_5355",
    chargeState: "91",
    revenue: "17204",
    timestamp: "2025-07-29T05:58:51.445Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75726",
    userId: "US_3823",
    site: "HUB_1651",
    returnedBatteryId: "BAT_7692",
    installedBatteryId: "BAT_9550",
    chargeState: "97",
    revenue: "11942",
    timestamp: "2025-07-29T08:38:46.292Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75727",
    userId: "US_1865",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7346",
    installedBatteryId: "BAT_9064",
    chargeState: "97",
    revenue: "12710",
    timestamp: "2025-07-29T11:40:59.636Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75728",
    userId: "US_9006",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2877",
    installedBatteryId: "BAT_921",
    chargeState: "96",
    revenue: "12307",
    timestamp: "2025-07-29T11:52:42.561Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75729",
    userId: "US_0171",
    site: "HUB_8778",
    returnedBatteryId: "BAT_2474",
    installedBatteryId: "BAT_6668",
    chargeState: "99",
    revenue: "11000",
    timestamp: "2025-07-29T13:25:21.416Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75730",
    userId: "US_9075",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2289",
    installedBatteryId: "BAT_7810",
    chargeState: "97",
    revenue: "17800",
    timestamp: "2025-07-29T15:06:29.305Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75731",
    userId: "US_7624",
    site: "HUB_4273",
    returnedBatteryId: "BAT_057",
    installedBatteryId: "BAT_7613",
    chargeState: "91",
    revenue: "11086",
    timestamp: "2025-07-29T16:57:44.281Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75732",
    userId: "US_7217",
    site: "HUB_8188",
    returnedBatteryId: "BAT_440",
    installedBatteryId: "BAT_8673",
    chargeState: "90",
    revenue: "16529",
    timestamp: "2025-07-29T19:57:28.496Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75733",
    userId: "US_6253",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8510",
    installedBatteryId: "BAT_4026",
    chargeState: "90",
    revenue: "18829",
    timestamp: "2025-07-29T22:11:13.816Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75734",
    userId: "US_4852",
    site: "HUB_7789",
    returnedBatteryId: "BAT_5237",
    installedBatteryId: "BAT_9695",
    chargeState: "97",
    revenue: "13196",
    timestamp: "2025-07-30T01:48:17.527Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75735",
    userId: "US_9514",
    site: "HUB_8778",
    returnedBatteryId: "BAT_4632",
    installedBatteryId: "BAT_8500",
    chargeState: "88",
    revenue: "11240",
    timestamp: "2025-07-30T02:54:20.882Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75736",
    userId: "US_6544",
    site: "HUB_0466",
    returnedBatteryId: "BAT_8158",
    installedBatteryId: "BAT_7956",
    chargeState: "94",
    revenue: "17799",
    timestamp: "2025-07-30T04:58:34.078Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75737",
    userId: "US_9297",
    site: "HUB_0466",
    returnedBatteryId: "BAT_768",
    installedBatteryId: "BAT_9653",
    chargeState: "98",
    revenue: "15947",
    timestamp: "2025-07-30T05:59:14.526Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75738",
    userId: "US_8835",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2302",
    installedBatteryId: "BAT_2218",
    chargeState: "88",
    revenue: "14447",
    timestamp: "2025-07-30T09:37:58.047Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75739",
    userId: "US_8441",
    site: "HUB_0611",
    returnedBatteryId: "BAT_2545",
    installedBatteryId: "BAT_5897",
    chargeState: "87",
    revenue: "19592",
    timestamp: "2025-07-30T09:58:28.951Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75740",
    userId: "US_8743",
    site: "HUB_4273",
    returnedBatteryId: "BAT_8742",
    installedBatteryId: "BAT_5945",
    chargeState: "93",
    revenue: "15254",
    timestamp: "2025-07-30T12:13:07.529Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75741",
    userId: "US_9908",
    site: "HUB_7789",
    returnedBatteryId: "BAT_9661",
    installedBatteryId: "BAT_2474",
    chargeState: "97",
    revenue: "14040",
    timestamp: "2025-07-30T14:04:48.169Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75742",
    userId: "US_0511",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8413",
    installedBatteryId: "BAT_6967",
    chargeState: "97",
    revenue: "13088",
    timestamp: "2025-07-30T14:28:03.848Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75743",
    userId: "US_5892",
    site: "HUB_1651",
    returnedBatteryId: "BAT_1614",
    installedBatteryId: "BAT_4455",
    chargeState: "94",
    revenue: "12869",
    timestamp: "2025-07-30T14:53:22.435Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75744",
    userId: "US_7868",
    site: "HUB_0611",
    returnedBatteryId: "BAT_6642",
    installedBatteryId: "BAT_8246",
    chargeState: "99",
    revenue: "12880",
    timestamp: "2025-07-30T15:53:32.652Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75745",
    userId: "US_8269",
    site: "HUB_4273",
    returnedBatteryId: "BAT_062",
    installedBatteryId: "BAT_2918",
    chargeState: "90",
    revenue: "17010",
    timestamp: "2025-07-30T18:12:25.505Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75746",
    userId: "US_7322",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8820",
    installedBatteryId: "BAT_7581",
    chargeState: "91",
    revenue: "18581",
    timestamp: "2025-07-30T19:30:36.620Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75747",
    userId: "US_9512",
    site: "HUB_0466",
    returnedBatteryId: "BAT_8806",
    installedBatteryId: "BAT_3270",
    chargeState: "97",
    revenue: "19124",
    timestamp: "2025-07-30T19:38:35.302Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75748",
    userId: "US_5476",
    site: "HUB_0611",
    returnedBatteryId: "BAT_2041",
    installedBatteryId: "BAT_5384",
    chargeState: "91",
    revenue: "11257",
    timestamp: "2025-07-30T22:35:15.515Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75749",
    userId: "US_4205",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2072",
    installedBatteryId: "BAT_2011",
    chargeState: "99",
    revenue: "11672",
    timestamp: "2025-07-31T01:58:40.870Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75750",
    userId: "US_6006",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8189",
    installedBatteryId: "BAT_5326",
    chargeState: "88",
    revenue: "14321",
    timestamp: "2025-07-31T05:39:54.776Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75751",
    userId: "US_8276",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9217",
    installedBatteryId: "BAT_349",
    chargeState: "97",
    revenue: "10092",
    timestamp: "2025-07-31T07:05:30.194Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75826",
    userId: "US_5227",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5333",
    installedBatteryId: "BAT_3478",
    chargeState: "92",
    revenue: "12408",
    timestamp: "2025-08-01T06:33:28.064Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75827",
    userId: "US_2839",
    site: "HUB_7789",
    returnedBatteryId: "BAT_6378",
    installedBatteryId: "BAT_1227",
    chargeState: "95",
    revenue: "14906",
    timestamp: "2025-08-01T09:33:46.433Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75828",
    userId: "US_8522",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4195",
    installedBatteryId: "BAT_8521",
    chargeState: "90",
    revenue: "12483",
    timestamp: "2025-08-01T12:03:15.009Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75829",
    userId: "US_0640",
    site: "HUB_0466",
    returnedBatteryId: "BAT_1388",
    installedBatteryId: "BAT_276",
    chargeState: "89",
    revenue: "17114",
    timestamp: "2025-08-01T13:42:26.511Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75830",
    userId: "US_2288",
    site: "HUB_7188",
    returnedBatteryId: "BAT_110",
    installedBatteryId: "BAT_4641",
    chargeState: "89",
    revenue: "15736",
    timestamp: "2025-08-01T16:35:59.089Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75831",
    userId: "US_2876",
    site: "HUB_4273",
    returnedBatteryId: "BAT_2974",
    installedBatteryId: "BAT_9042",
    chargeState: "95",
    revenue: "16919",
    timestamp: "2025-08-01T17:17:53.198Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75832",
    userId: "US_2498",
    site: "HUB_7188",
    returnedBatteryId: "BAT_1591",
    installedBatteryId: "BAT_3802",
    chargeState: "94",
    revenue: "12973",
    timestamp: "2025-08-01T17:54:17.423Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75833",
    userId: "US_5582",
    site: "HUB_0466",
    returnedBatteryId: "BAT_9535",
    installedBatteryId: "BAT_8769",
    chargeState: "97",
    revenue: "12234",
    timestamp: "2025-08-01T19:53:19.897Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75834",
    userId: "US_8165",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8379",
    installedBatteryId: "BAT_5356",
    chargeState: "98",
    revenue: "18233",
    timestamp: "2025-08-01T21:59:37.322Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75835",
    userId: "US_1917",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8788",
    installedBatteryId: "BAT_5344",
    chargeState: "98",
    revenue: "18968",
    timestamp: "2025-08-02T00:22:23.151Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75836",
    userId: "US_4789",
    site: "HUB_8778",
    returnedBatteryId: "BAT_4541",
    installedBatteryId: "BAT_8883",
    chargeState: "99",
    revenue: "13173",
    timestamp: "2025-08-02T02:54:49.200Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75837",
    userId: "US_7835",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5912",
    installedBatteryId: "BAT_9762",
    chargeState: "91",
    revenue: "10085",
    timestamp: "2025-08-02T03:35:57.115Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75838",
    userId: "US_0439",
    site: "HUB_9196",
    returnedBatteryId: "BAT_1281",
    installedBatteryId: "BAT_2250",
    chargeState: "97",
    revenue: "17390",
    timestamp: "2025-08-02T06:20:09.957Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75839",
    userId: "US_8660",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4087",
    installedBatteryId: "BAT_9366",
    chargeState: "98",
    revenue: "13439",
    timestamp: "2025-08-02T09:45:51.948Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75840",
    userId: "US_6059",
    site: "HUB_9097",
    returnedBatteryId: "BAT_6863",
    installedBatteryId: "BAT_5237",
    chargeState: "95",
    revenue: "13083",
    timestamp: "2025-08-02T10:30:46.591Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75841",
    userId: "US_6223",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5361",
    installedBatteryId: "BAT_4195",
    chargeState: "97",
    revenue: "17707",
    timestamp: "2025-08-02T10:52:24.912Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75842",
    userId: "US_3127",
    site: "HUB_7789",
    returnedBatteryId: "BAT_5413",
    installedBatteryId: "BAT_1748",
    chargeState: "89",
    revenue: "15399",
    timestamp: "2025-08-02T12:57:50.626Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75843",
    userId: "US_2382",
    site: "HUB_8188",
    returnedBatteryId: "BAT_6228",
    installedBatteryId: "BAT_1203",
    chargeState: "94",
    revenue: "15523",
    timestamp: "2025-08-02T14:44:07.465Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75844",
    userId: "US_2648",
    site: "HUB_7789",
    returnedBatteryId: "BAT_3882",
    installedBatteryId: "BAT_1481",
    chargeState: "94",
    revenue: "11021",
    timestamp: "2025-08-02T17:06:24.890Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75845",
    userId: "US_9423",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8539",
    installedBatteryId: "BAT_2489",
    chargeState: "98",
    revenue: "18908",
    timestamp: "2025-08-02T19:19:14.387Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75846",
    userId: "US_3922",
    site: "HUB_9097",
    returnedBatteryId: "BAT_7809",
    installedBatteryId: "BAT_6417",
    chargeState: "91",
    revenue: "17458",
    timestamp: "2025-08-02T20:45:10.752Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75847",
    userId: "US_5533",
    site: "HUB_0466",
    returnedBatteryId: "BAT_268",
    installedBatteryId: "BAT_2782",
    chargeState: "98",
    revenue: "16415",
    timestamp: "2025-08-02T22:04:42.203Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75848",
    userId: "US_0610",
    site: "HUB_0466",
    returnedBatteryId: "BAT_6396",
    installedBatteryId: "BAT_5216",
    chargeState: "96",
    revenue: "14596",
    timestamp: "2025-08-03T00:45:20.244Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75849",
    userId: "US_6607",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5355",
    installedBatteryId: "BAT_2448",
    chargeState: "91",
    revenue: "15697",
    timestamp: "2025-08-03T02:12:03.900Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75850",
    userId: "US_3823",
    site: "HUB_8778",
    returnedBatteryId: "BAT_9550",
    installedBatteryId: "BAT_7023",
    chargeState: "89",
    revenue: "17985",
    timestamp: "2025-08-03T03:11:38.220Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75851",
    userId: "US_1865",
    site: "HUB_7188",
    returnedBatteryId: "BAT_9064",
    installedBatteryId: "BAT_1756",
    chargeState: "89",
    revenue: "17969",
    timestamp: "2025-08-03T05:30:01.411Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75852",
    userId: "US_9006",
    site: "HUB_9196",
    returnedBatteryId: "BAT_921",
    installedBatteryId: "BAT_9510",
    chargeState: "90",
    revenue: "10102",
    timestamp: "2025-08-03T08:41:11.481Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75853",
    userId: "US_0171",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6668",
    installedBatteryId: "BAT_8804",
    chargeState: "99",
    revenue: "13683",
    timestamp: "2025-08-03T11:57:34.068Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75854",
    userId: "US_9075",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7810",
    installedBatteryId: "BAT_2265",
    chargeState: "88",
    revenue: "14535",
    timestamp: "2025-08-03T13:22:10.255Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75926",
    userId: "US_7624",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7613",
    installedBatteryId: "BAT_6668",
    chargeState: "97",
    revenue: "13880",
    timestamp: "2025-08-04T06:39:43.333Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75927",
    userId: "US_7217",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8673",
    installedBatteryId: "BAT_6579",
    chargeState: "95",
    revenue: "17868",
    timestamp: "2025-08-04T09:55:21.760Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75928",
    userId: "US_6253",
    site: "HUB_8778",
    returnedBatteryId: "BAT_4026",
    installedBatteryId: "BAT_8539",
    chargeState: "99",
    revenue: "11442",
    timestamp: "2025-08-04T10:01:50.111Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75929",
    userId: "US_4852",
    site: "HUB_8188",
    returnedBatteryId: "BAT_9695",
    installedBatteryId: "BAT_3484",
    chargeState: "89",
    revenue: "17823",
    timestamp: "2025-08-04T10:55:15.447Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75930",
    userId: "US_9514",
    site: "HUB_1651",
    returnedBatteryId: "BAT_8500",
    installedBatteryId: "BAT_8294",
    chargeState: "92",
    revenue: "18816",
    timestamp: "2025-08-04T12:34:18.482Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75931",
    userId: "US_6544",
    site: "HUB_7188",
    returnedBatteryId: "BAT_7956",
    installedBatteryId: "BAT_4872",
    chargeState: "98",
    revenue: "10676",
    timestamp: "2025-08-04T12:36:00.855Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75932",
    userId: "US_9297",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9653",
    installedBatteryId: "BAT_10000",
    chargeState: "93",
    revenue: "16000",
    timestamp: "2025-08-04T14:31:30.454Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75933",
    userId: "US_8835",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2218",
    installedBatteryId: "BAT_4282",
    chargeState: "94",
    revenue: "15288",
    timestamp: "2025-08-04T14:48:11.724Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75934",
    userId: "US_8441",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5897",
    installedBatteryId: "BAT_7613",
    chargeState: "89",
    revenue: "17225",
    timestamp: "2025-08-04T15:26:13.753Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75935",
    userId: "US_8743",
    site: "HUB_8778",
    returnedBatteryId: "BAT_5945",
    installedBatteryId: "BAT_2766",
    chargeState: "98",
    revenue: "12451",
    timestamp: "2025-08-04T16:51:51.359Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75936",
    userId: "US_9908",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2474",
    installedBatteryId: "BAT_1616",
    chargeState: "96",
    revenue: "15476",
    timestamp: "2025-08-04T18:41:34.935Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75937",
    userId: "US_0511",
    site: "HUB_0611",
    returnedBatteryId: "BAT_6967",
    installedBatteryId: "BAT_4878",
    chargeState: "89",
    revenue: "18262",
    timestamp: "2025-08-04T19:06:47.503Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75938",
    userId: "US_5892",
    site: "HUB_7188",
    returnedBatteryId: "BAT_4455",
    installedBatteryId: "BAT_5150",
    chargeState: "93",
    revenue: "12274",
    timestamp: "2025-08-04T21:56:23.967Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75939",
    userId: "US_7868",
    site: "HUB_9196",
    returnedBatteryId: "BAT_8246",
    installedBatteryId: "BAT_4876",
    chargeState: "97",
    revenue: "14208",
    timestamp: "2025-08-04T23:00:11.568Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75940",
    userId: "US_8269",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2918",
    installedBatteryId: "BAT_2882",
    chargeState: "88",
    revenue: "12198",
    timestamp: "2025-08-05T00:58:14.103Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75941",
    userId: "US_7322",
    site: "HUB_1651",
    returnedBatteryId: "BAT_7581",
    installedBatteryId: "BAT_8665",
    chargeState: "91",
    revenue: "16247",
    timestamp: "2025-08-05T02:41:11.197Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75942",
    userId: "US_9512",
    site: "HUB_7188",
    returnedBatteryId: "BAT_3270",
    installedBatteryId: "BAT_7266",
    chargeState: "87",
    revenue: "16232",
    timestamp: "2025-08-05T04:56:42.342Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75943",
    userId: "US_5476",
    site: "HUB_9196",
    returnedBatteryId: "BAT_5384",
    installedBatteryId: "BAT_044",
    chargeState: "88",
    revenue: "17537",
    timestamp: "2025-08-05T05:28:51.946Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75944",
    userId: "US_4205",
    site: "HUB_9097",
    returnedBatteryId: "BAT_2011",
    installedBatteryId: "BAT_9807",
    chargeState: "95",
    revenue: "13696",
    timestamp: "2025-08-05T08:35:24.146Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75945",
    userId: "US_6006",
    site: "HUB_0611",
    returnedBatteryId: "BAT_5326",
    installedBatteryId: "BAT_1839",
    chargeState: "98",
    revenue: "19788",
    timestamp: "2025-08-05T11:28:02.212Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75946",
    userId: "US_8276",
    site: "HUB_8188",
    returnedBatteryId: "BAT_349",
    installedBatteryId: "BAT_6092",
    chargeState: "88",
    revenue: "13804",
    timestamp: "2025-08-05T11:48:51.743Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75947",
    userId: "US_5227",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3478",
    installedBatteryId: "BAT_065",
    chargeState: "89",
    revenue: "10697",
    timestamp: "2025-08-05T12:17:55.798Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75948",
    userId: "US_2839",
    site: "HUB_0611",
    returnedBatteryId: "BAT_1227",
    installedBatteryId: "BAT_4980",
    chargeState: "91",
    revenue: "10533",
    timestamp: "2025-08-05T14:35:37.071Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_75949",
    userId: "US_8522",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8521",
    installedBatteryId: "BAT_6670",
    chargeState: "96",
    revenue: "14049",
    timestamp: "2025-08-05T14:39:37.887Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75950",
    userId: "US_0640",
    site: "HUB_0611",
    returnedBatteryId: "BAT_276",
    installedBatteryId: "BAT_4633",
    chargeState: "97",
    revenue: "13752",
    timestamp: "2025-08-05T15:14:37.093Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75951",
    userId: "US_2288",
    site: "HUB_0611",
    returnedBatteryId: "BAT_4641",
    installedBatteryId: "BAT_9676",
    chargeState: "99",
    revenue: "13851",
    timestamp: "2025-08-05T15:17:47.565Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75952",
    userId: "US_2876",
    site: "HUB_8188",
    returnedBatteryId: "BAT_9042",
    installedBatteryId: "BAT_4480",
    chargeState: "95",
    revenue: "18994",
    timestamp: "2025-08-05T16:08:36.461Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_75953",
    userId: "US_2498",
    site: "HUB_8188",
    returnedBatteryId: "BAT_3802",
    installedBatteryId: "BAT_2823",
    chargeState: "90",
    revenue: "16277",
    timestamp: "2025-08-05T17:02:33.943Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_75954",
    userId: "US_5582",
    site: "HUB_1651",
    returnedBatteryId: "BAT_8769",
    installedBatteryId: "BAT_5912",
    chargeState: "90",
    revenue: "11483",
    timestamp: "2025-08-05T20:11:36.526Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_75955",
    userId: "US_8165",
    site: "HUB_0466",
    returnedBatteryId: "BAT_5356",
    installedBatteryId: "BAT_3270",
    chargeState: "90",
    revenue: "13465",
    timestamp: "2025-08-05T22:16:26.868Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76026",
    userId: "US_1917",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5344",
    installedBatteryId: "BAT_7660",
    chargeState: "98",
    revenue: "10004",
    timestamp: "2025-08-06T07:49:53.365Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76027",
    userId: "US_4789",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8883",
    installedBatteryId: "BAT_2158",
    chargeState: "92",
    revenue: "12417",
    timestamp: "2025-08-06T08:04:27.374Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76028",
    userId: "US_7835",
    site: "HUB_4273",
    returnedBatteryId: "BAT_9762",
    installedBatteryId: "BAT_057",
    chargeState: "94",
    revenue: "18374",
    timestamp: "2025-08-06T09:32:56.100Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76029",
    userId: "US_0439",
    site: "HUB_4273",
    returnedBatteryId: "BAT_2250",
    installedBatteryId: "BAT_9719",
    chargeState: "98",
    revenue: "13258",
    timestamp: "2025-08-06T09:43:55.780Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76030",
    userId: "US_8660",
    site: "HUB_0611",
    returnedBatteryId: "BAT_9366",
    installedBatteryId: "BAT_9523",
    chargeState: "94",
    revenue: "14339",
    timestamp: "2025-08-06T11:09:23.880Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76031",
    userId: "US_6059",
    site: "HUB_0466",
    returnedBatteryId: "BAT_5237",
    installedBatteryId: "BAT_8982",
    chargeState: "95",
    revenue: "12248",
    timestamp: "2025-08-06T13:32:07.716Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76032",
    userId: "US_6223",
    site: "HUB_7188",
    returnedBatteryId: "BAT_4195",
    installedBatteryId: "BAT_4059",
    chargeState: "92",
    revenue: "15392",
    timestamp: "2025-08-06T15:29:51.552Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76033",
    userId: "US_3127",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1748",
    installedBatteryId: "BAT_3882",
    chargeState: "99",
    revenue: "19504",
    timestamp: "2025-08-06T17:55:14.257Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76034",
    userId: "US_2382",
    site: "HUB_7789",
    returnedBatteryId: "BAT_1203",
    installedBatteryId: "BAT_9064",
    chargeState: "89",
    revenue: "15435",
    timestamp: "2025-08-06T21:06:34.702Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76035",
    userId: "US_2648",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1481",
    installedBatteryId: "BAT_2976",
    chargeState: "96",
    revenue: "19378",
    timestamp: "2025-08-06T23:56:43.644Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76036",
    userId: "US_9423",
    site: "HUB_8778",
    returnedBatteryId: "BAT_2489",
    installedBatteryId: "BAT_1178",
    chargeState: "97",
    revenue: "11898",
    timestamp: "2025-08-07T01:03:51.270Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76037",
    userId: "US_3922",
    site: "HUB_7789",
    returnedBatteryId: "BAT_6417",
    installedBatteryId: "BAT_8744",
    chargeState: "89",
    revenue: "19822",
    timestamp: "2025-08-07T01:09:20.249Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76038",
    userId: "US_5533",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2782",
    installedBatteryId: "BAT_3932",
    chargeState: "90",
    revenue: "11509",
    timestamp: "2025-08-07T03:08:53.030Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76039",
    userId: "US_0610",
    site: "HUB_8188",
    returnedBatteryId: "BAT_5216",
    installedBatteryId: "BAT_3186",
    chargeState: "90",
    revenue: "13236",
    timestamp: "2025-08-07T05:53:15.597Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76040",
    userId: "US_6607",
    site: "HUB_0611",
    returnedBatteryId: "BAT_2448",
    installedBatteryId: "BAT_2250",
    chargeState: "97",
    revenue: "16815",
    timestamp: "2025-08-07T06:12:41.755Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76041",
    userId: "US_3823",
    site: "HUB_9097",
    returnedBatteryId: "BAT_7023",
    installedBatteryId: "BAT_5945",
    chargeState: "91",
    revenue: "14494",
    timestamp: "2025-08-07T06:17:38.552Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76042",
    userId: "US_1865",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1756",
    installedBatteryId: "BAT_935",
    chargeState: "89",
    revenue: "11638",
    timestamp: "2025-08-07T08:30:29.095Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76043",
    userId: "US_9006",
    site: "HUB_9097",
    returnedBatteryId: "BAT_9510",
    installedBatteryId: "BAT_2218",
    chargeState: "96",
    revenue: "15394",
    timestamp: "2025-08-07T10:21:55.230Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76044",
    userId: "US_0171",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8804",
    installedBatteryId: "BAT_5974",
    chargeState: "88",
    revenue: "19786",
    timestamp: "2025-08-07T13:44:41.053Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76045",
    userId: "US_9075",
    site: "HUB_4273",
    returnedBatteryId: "BAT_2265",
    installedBatteryId: "BAT_7305",
    chargeState: "93",
    revenue: "17819",
    timestamp: "2025-08-07T15:45:53.099Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76046",
    userId: "US_7624",
    site: "HUB_4273",
    returnedBatteryId: "BAT_6668",
    installedBatteryId: "BAT_1537",
    chargeState: "92",
    revenue: "10471",
    timestamp: "2025-08-07T17:10:33.072Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76047",
    userId: "US_7217",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6579",
    installedBatteryId: "BAT_3718",
    chargeState: "99",
    revenue: "19361",
    timestamp: "2025-08-07T19:09:56.314Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76048",
    userId: "US_6253",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8539",
    installedBatteryId: "BAT_7809",
    chargeState: "97",
    revenue: "10889",
    timestamp: "2025-08-07T19:51:45.126Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76049",
    userId: "US_4852",
    site: "HUB_9196",
    returnedBatteryId: "BAT_3484",
    installedBatteryId: "BAT_5929",
    chargeState: "98",
    revenue: "19961",
    timestamp: "2025-08-07T20:31:20.788Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76050",
    userId: "US_9514",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8294",
    installedBatteryId: "BAT_9933",
    chargeState: "93",
    revenue: "17052",
    timestamp: "2025-08-07T21:25:16.314Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76051",
    userId: "US_6544",
    site: "HUB_0466",
    returnedBatteryId: "BAT_4872",
    installedBatteryId: "BAT_8082",
    chargeState: "98",
    revenue: "13472",
    timestamp: "2025-08-07T22:41:25.662Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76052",
    userId: "US_9297",
    site: "HUB_4273",
    returnedBatteryId: "BAT_10000",
    installedBatteryId: "BAT_5161",
    chargeState: "91",
    revenue: "14875",
    timestamp: "2025-08-08T01:36:35.465Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76053",
    userId: "US_8835",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4282",
    installedBatteryId: "BAT_2471",
    chargeState: "95",
    revenue: "16425",
    timestamp: "2025-08-08T03:58:44.154Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76054",
    userId: "US_8441",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7613",
    installedBatteryId: "BAT_2616",
    chargeState: "98",
    revenue: "16515",
    timestamp: "2025-08-08T04:53:37.164Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76055",
    userId: "US_8743",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2766",
    installedBatteryId: "BAT_4026",
    chargeState: "98",
    revenue: "16626",
    timestamp: "2025-08-08T05:02:00.103Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76056",
    userId: "US_9908",
    site: "HUB_7188",
    returnedBatteryId: "BAT_1616",
    installedBatteryId: "BAT_110",
    chargeState: "93",
    revenue: "16162",
    timestamp: "2025-08-08T05:59:50.456Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76126",
    userId: "US_0511",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4878",
    installedBatteryId: "BAT_6116",
    chargeState: "94",
    revenue: "10399",
    timestamp: "2025-08-09T07:24:49.003Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76127",
    userId: "US_5892",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5150",
    installedBatteryId: "BAT_349",
    chargeState: "97",
    revenue: "11728",
    timestamp: "2025-08-09T09:54:07.514Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76128",
    userId: "US_7868",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4876",
    installedBatteryId: "BAT_6335",
    chargeState: "88",
    revenue: "12472",
    timestamp: "2025-08-09T10:59:39.445Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76129",
    userId: "US_8269",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2882",
    installedBatteryId: "BAT_1591",
    chargeState: "87",
    revenue: "18525",
    timestamp: "2025-08-09T13:24:53.029Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76130",
    userId: "US_7322",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8665",
    installedBatteryId: "BAT_1281",
    chargeState: "95",
    revenue: "18006",
    timestamp: "2025-08-09T13:34:34.897Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76131",
    userId: "US_9512",
    site: "HUB_7188",
    returnedBatteryId: "BAT_7266",
    installedBatteryId: "BAT_7018",
    chargeState: "90",
    revenue: "12565",
    timestamp: "2025-08-09T15:40:19.095Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76132",
    userId: "US_5476",
    site: "HUB_7188",
    returnedBatteryId: "BAT_044",
    installedBatteryId: "BAT_8890",
    chargeState: "94",
    revenue: "12582",
    timestamp: "2025-08-09T16:04:35.886Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76133",
    userId: "US_4205",
    site: "HUB_8778",
    returnedBatteryId: "BAT_9807",
    installedBatteryId: "BAT_7337",
    chargeState: "91",
    revenue: "18848",
    timestamp: "2025-08-09T16:50:06.195Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76134",
    userId: "US_6006",
    site: "HUB_1651",
    returnedBatteryId: "BAT_1839",
    installedBatteryId: "BAT_9510",
    chargeState: "98",
    revenue: "10146",
    timestamp: "2025-08-09T18:58:16.162Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76135",
    userId: "US_8276",
    site: "HUB_9097",
    returnedBatteryId: "BAT_6092",
    installedBatteryId: "BAT_8872",
    chargeState: "99",
    revenue: "16564",
    timestamp: "2025-08-09T20:50:03.842Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76136",
    userId: "US_5227",
    site: "HUB_7789",
    returnedBatteryId: "BAT_065",
    installedBatteryId: "BAT_2041",
    chargeState: "91",
    revenue: "19284",
    timestamp: "2025-08-09T23:55:54.998Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76137",
    userId: "US_2839",
    site: "HUB_8188",
    returnedBatteryId: "BAT_4980",
    installedBatteryId: "BAT_1756",
    chargeState: "93",
    revenue: "17280",
    timestamp: "2025-08-10T01:40:32.996Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76138",
    userId: "US_8522",
    site: "HUB_7188",
    returnedBatteryId: "BAT_6670",
    installedBatteryId: "BAT_4159",
    chargeState: "89",
    revenue: "19736",
    timestamp: "2025-08-10T04:28:41.276Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76139",
    userId: "US_0640",
    site: "HUB_7188",
    returnedBatteryId: "BAT_4633",
    installedBatteryId: "BAT_3058",
    chargeState: "87",
    revenue: "19761",
    timestamp: "2025-08-10T07:09:01.091Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76140",
    userId: "US_2288",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9676",
    installedBatteryId: "BAT_9217",
    chargeState: "91",
    revenue: "15333",
    timestamp: "2025-08-10T10:04:25.430Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76141",
    userId: "US_2876",
    site: "HUB_7188",
    returnedBatteryId: "BAT_4480",
    installedBatteryId: "BAT_8984",
    chargeState: "98",
    revenue: "12115",
    timestamp: "2025-08-10T11:00:35.112Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76142",
    userId: "US_2498",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2823",
    installedBatteryId: "BAT_8529",
    chargeState: "90",
    revenue: "11389",
    timestamp: "2025-08-10T11:13:46.519Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76143",
    userId: "US_5582",
    site: "HUB_9196",
    returnedBatteryId: "BAT_5912",
    installedBatteryId: "BAT_2373",
    chargeState: "89",
    revenue: "11747",
    timestamp: "2025-08-10T13:58:00.384Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76144",
    userId: "US_8165",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3270",
    installedBatteryId: "BAT_4878",
    chargeState: "94",
    revenue: "13737",
    timestamp: "2025-08-10T15:03:57.892Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76145",
    userId: "US_1917",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7660",
    installedBatteryId: "BAT_9762",
    chargeState: "99",
    revenue: "16830",
    timestamp: "2025-08-10T16:13:58.355Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76146",
    userId: "US_4789",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2158",
    installedBatteryId: "BAT_2449",
    chargeState: "89",
    revenue: "15552",
    timestamp: "2025-08-10T19:39:26.251Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76147",
    userId: "US_7835",
    site: "HUB_9196",
    returnedBatteryId: "BAT_057",
    installedBatteryId: "BAT_1227",
    chargeState: "91",
    revenue: "19303",
    timestamp: "2025-08-10T22:00:43.827Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76148",
    userId: "US_0439",
    site: "HUB_8188",
    returnedBatteryId: "BAT_9719",
    installedBatteryId: "BAT_8510",
    chargeState: "96",
    revenue: "18993",
    timestamp: "2025-08-11T00:51:27.138Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76149",
    userId: "US_8660",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9523",
    installedBatteryId: "BAT_7023",
    chargeState: "99",
    revenue: "19251",
    timestamp: "2025-08-11T01:42:08.422Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76150",
    userId: "US_6059",
    site: "HUB_9097",
    returnedBatteryId: "BAT_8982",
    installedBatteryId: "BAT_7818",
    chargeState: "89",
    revenue: "17464",
    timestamp: "2025-08-11T04:48:21.494Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76151",
    userId: "US_6223",
    site: "HUB_9097",
    returnedBatteryId: "BAT_4059",
    installedBatteryId: "BAT_6939",
    chargeState: "99",
    revenue: "18482",
    timestamp: "2025-08-11T06:04:22.659Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76152",
    userId: "US_3127",
    site: "HUB_9196",
    returnedBatteryId: "BAT_3882",
    installedBatteryId: "BAT_7422",
    chargeState: "91",
    revenue: "19210",
    timestamp: "2025-08-11T06:28:02.067Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76153",
    userId: "US_2382",
    site: "HUB_7789",
    returnedBatteryId: "BAT_9064",
    installedBatteryId: "BAT_7974",
    chargeState: "97",
    revenue: "12043",
    timestamp: "2025-08-11T07:06:20.450Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76154",
    userId: "US_2648",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2976",
    installedBatteryId: "BAT_6036",
    chargeState: "93",
    revenue: "19114",
    timestamp: "2025-08-11T08:29:13.968Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76155",
    userId: "US_9423",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1178",
    installedBatteryId: "BAT_5356",
    chargeState: "96",
    revenue: "14737",
    timestamp: "2025-08-11T11:39:40.534Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76156",
    userId: "US_3922",
    site: "HUB_1651",
    returnedBatteryId: "BAT_8744",
    installedBatteryId: "BAT_8337",
    chargeState: "91",
    revenue: "19065",
    timestamp: "2025-08-11T14:51:14.937Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76157",
    userId: "US_5533",
    site: "HUB_8778",
    returnedBatteryId: "BAT_3932",
    installedBatteryId: "BAT_9509",
    chargeState: "98",
    revenue: "18498",
    timestamp: "2025-08-11T15:13:42.574Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76227",
    userId: "US_0610",
    site: "HUB_8188",
    returnedBatteryId: "BAT_3186",
    installedBatteryId: "BAT_9249",
    chargeState: "94",
    revenue: "16160",
    timestamp: "2025-08-12T07:59:53.732Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76228",
    userId: "US_6607",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2250",
    installedBatteryId: "BAT_2877",
    chargeState: "93",
    revenue: "17618",
    timestamp: "2025-08-12T10:54:38.180Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76229",
    userId: "US_3823",
    site: "HUB_7789",
    returnedBatteryId: "BAT_5945",
    installedBatteryId: "BAT_3932",
    chargeState: "90",
    revenue: "13487",
    timestamp: "2025-08-12T13:21:21.574Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76230",
    userId: "US_1865",
    site: "HUB_4273",
    returnedBatteryId: "BAT_935",
    installedBatteryId: "BAT_3575",
    chargeState: "99",
    revenue: "14194",
    timestamp: "2025-08-12T15:15:24.466Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76231",
    userId: "US_9006",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2218",
    installedBatteryId: "BAT_9676",
    chargeState: "87",
    revenue: "11895",
    timestamp: "2025-08-12T16:40:42.159Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76232",
    userId: "US_0171",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5974",
    installedBatteryId: "BAT_5384",
    chargeState: "97",
    revenue: "13573",
    timestamp: "2025-08-12T19:12:15.144Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76233",
    userId: "US_9075",
    site: "HUB_1651",
    returnedBatteryId: "BAT_7305",
    installedBatteryId: "BAT_4235",
    chargeState: "93",
    revenue: "14199",
    timestamp: "2025-08-12T20:03:51.507Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76234",
    userId: "US_7624",
    site: "HUB_7789",
    returnedBatteryId: "BAT_1537",
    installedBatteryId: "BAT_057",
    chargeState: "92",
    revenue: "19787",
    timestamp: "2025-08-12T21:22:10.542Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76235",
    userId: "US_7217",
    site: "HUB_0611",
    returnedBatteryId: "BAT_3718",
    installedBatteryId: "BAT_6533",
    chargeState: "94",
    revenue: "14924",
    timestamp: "2025-08-12T23:43:58.952Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76236",
    userId: "US_6253",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7809",
    installedBatteryId: "BAT_6228",
    chargeState: "92",
    revenue: "11098",
    timestamp: "2025-08-13T01:16:49.360Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76237",
    userId: "US_4852",
    site: "HUB_4273",
    returnedBatteryId: "BAT_5929",
    installedBatteryId: "BAT_7294",
    chargeState: "95",
    revenue: "14298",
    timestamp: "2025-08-13T03:11:04.377Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76238",
    userId: "US_9514",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9933",
    installedBatteryId: "BAT_2011",
    chargeState: "90",
    revenue: "16399",
    timestamp: "2025-08-13T04:22:32.473Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76239",
    userId: "US_6544",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8082",
    installedBatteryId: "BAT_6295",
    chargeState: "94",
    revenue: "12494",
    timestamp: "2025-08-13T05:20:15.386Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76240",
    userId: "US_9297",
    site: "HUB_4273",
    returnedBatteryId: "BAT_5161",
    installedBatteryId: "BAT_6959",
    chargeState: "96",
    revenue: "11655",
    timestamp: "2025-08-13T07:58:41.894Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76241",
    userId: "US_8835",
    site: "HUB_8778",
    returnedBatteryId: "BAT_2471",
    installedBatteryId: "BAT_317",
    chargeState: "96",
    revenue: "10390",
    timestamp: "2025-08-13T08:05:13.129Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76242",
    userId: "US_8441",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2616",
    installedBatteryId: "BAT_2072",
    chargeState: "89",
    revenue: "12022",
    timestamp: "2025-08-13T09:53:52.291Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76243",
    userId: "US_8743",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4026",
    installedBatteryId: "BAT_3599",
    chargeState: "92",
    revenue: "16887",
    timestamp: "2025-08-13T10:12:22.717Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76244",
    userId: "US_9908",
    site: "HUB_9196",
    returnedBatteryId: "BAT_110",
    installedBatteryId: "BAT_7810",
    chargeState: "87",
    revenue: "15968",
    timestamp: "2025-08-13T11:49:12.240Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76245",
    userId: "US_0511",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6116",
    installedBatteryId: "BAT_6670",
    chargeState: "98",
    revenue: "14723",
    timestamp: "2025-08-13T11:56:05.315Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76246",
    userId: "US_5892",
    site: "HUB_0611",
    returnedBatteryId: "BAT_349",
    installedBatteryId: "BAT_9064",
    chargeState: "92",
    revenue: "14414",
    timestamp: "2025-08-13T12:18:23.739Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76247",
    userId: "US_7868",
    site: "HUB_7188",
    returnedBatteryId: "BAT_6335",
    installedBatteryId: "BAT_6236",
    chargeState: "91",
    revenue: "16750",
    timestamp: "2025-08-13T13:15:04.193Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76248",
    userId: "US_8269",
    site: "HUB_9097",
    returnedBatteryId: "BAT_1591",
    installedBatteryId: "BAT_2158",
    chargeState: "87",
    revenue: "17248",
    timestamp: "2025-08-13T14:16:09.942Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76249",
    userId: "US_7322",
    site: "HUB_8778",
    returnedBatteryId: "BAT_1281",
    installedBatteryId: "BAT_3370",
    chargeState: "93",
    revenue: "15757",
    timestamp: "2025-08-13T16:31:27.754Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76250",
    userId: "US_9512",
    site: "HUB_9097",
    returnedBatteryId: "BAT_7018",
    installedBatteryId: "BAT_6359",
    chargeState: "93",
    revenue: "16815",
    timestamp: "2025-08-13T16:50:52.174Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76251",
    userId: "US_5476",
    site: "HUB_4273",
    returnedBatteryId: "BAT_8890",
    installedBatteryId: "BAT_5035",
    chargeState: "90",
    revenue: "16506",
    timestamp: "2025-08-13T17:19:41.996Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76252",
    userId: "US_4205",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7337",
    installedBatteryId: "BAT_1616",
    chargeState: "91",
    revenue: "15036",
    timestamp: "2025-08-13T19:59:27.675Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76253",
    userId: "US_6006",
    site: "HUB_8778",
    returnedBatteryId: "BAT_9510",
    installedBatteryId: "BAT_8539",
    chargeState: "96",
    revenue: "18667",
    timestamp: "2025-08-13T22:37:13.039Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76254",
    userId: "US_8276",
    site: "HUB_4273",
    returnedBatteryId: "BAT_8872",
    installedBatteryId: "BAT_5143",
    chargeState: "91",
    revenue: "14752",
    timestamp: "2025-08-14T00:00:23.228Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76255",
    userId: "US_5227",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2041",
    installedBatteryId: "BAT_7557",
    chargeState: "87",
    revenue: "15690",
    timestamp: "2025-08-14T01:30:44.561Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76256",
    userId: "US_2839",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1756",
    installedBatteryId: "BAT_1817",
    chargeState: "98",
    revenue: "15889",
    timestamp: "2025-08-14T01:41:20.881Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76257",
    userId: "US_8522",
    site: "HUB_7188",
    returnedBatteryId: "BAT_4159",
    installedBatteryId: "BAT_3038",
    chargeState: "95",
    revenue: "16090",
    timestamp: "2025-08-14T02:51:52.963Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76258",
    userId: "US_0640",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3058",
    installedBatteryId: "BAT_1306",
    chargeState: "91",
    revenue: "13110",
    timestamp: "2025-08-14T04:19:46.322Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76259",
    userId: "US_2288",
    site: "HUB_9097",
    returnedBatteryId: "BAT_9217",
    installedBatteryId: "BAT_5333",
    chargeState: "91",
    revenue: "19612",
    timestamp: "2025-08-14T06:49:43.070Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76260",
    userId: "US_2876",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8984",
    installedBatteryId: "BAT_5161",
    chargeState: "92",
    revenue: "11757",
    timestamp: "2025-08-14T06:53:17.269Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76327",
    userId: "US_2498",
    site: "HUB_9196",
    returnedBatteryId: "BAT_8529",
    installedBatteryId: "BAT_322",
    chargeState: "96",
    revenue: "16838",
    timestamp: "2025-08-15T05:25:53.641Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76328",
    userId: "US_5582",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2373",
    installedBatteryId: "BAT_5563",
    chargeState: "90",
    revenue: "15184",
    timestamp: "2025-08-15T07:07:03.758Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76329",
    userId: "US_8165",
    site: "HUB_9196",
    returnedBatteryId: "BAT_4878",
    installedBatteryId: "BAT_3270",
    chargeState: "91",
    revenue: "19135",
    timestamp: "2025-08-15T09:36:53.486Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76330",
    userId: "US_1917",
    site: "HUB_0611",
    returnedBatteryId: "BAT_9762",
    installedBatteryId: "BAT_9175",
    chargeState: "89",
    revenue: "14124",
    timestamp: "2025-08-15T12:20:02.353Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76331",
    userId: "US_4789",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2449",
    installedBatteryId: "BAT_9240",
    chargeState: "94",
    revenue: "16266",
    timestamp: "2025-08-15T14:13:38.316Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76332",
    userId: "US_7835",
    site: "HUB_4273",
    returnedBatteryId: "BAT_1227",
    installedBatteryId: "BAT_3882",
    chargeState: "87",
    revenue: "11229",
    timestamp: "2025-08-15T15:29:27.480Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76333",
    userId: "US_0439",
    site: "HUB_9196",
    returnedBatteryId: "BAT_8510",
    installedBatteryId: "BAT_4195",
    chargeState: "91",
    revenue: "19886",
    timestamp: "2025-08-15T16:59:28.746Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76334",
    userId: "US_8660",
    site: "HUB_8188",
    returnedBatteryId: "BAT_7023",
    installedBatteryId: "BAT_9596",
    chargeState: "92",
    revenue: "14340",
    timestamp: "2025-08-15T18:44:39.437Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76335",
    userId: "US_6059",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7818",
    installedBatteryId: "BAT_1096",
    chargeState: "90",
    revenue: "12369",
    timestamp: "2025-08-15T19:52:26.493Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76336",
    userId: "US_6223",
    site: "HUB_9097",
    returnedBatteryId: "BAT_6939",
    installedBatteryId: "BAT_4480",
    chargeState: "97",
    revenue: "18565",
    timestamp: "2025-08-15T21:44:30.613Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76337",
    userId: "US_3127",
    site: "HUB_1651",
    returnedBatteryId: "BAT_7422",
    installedBatteryId: "BAT_1914",
    chargeState: "93",
    revenue: "19607",
    timestamp: "2025-08-15T22:49:25.042Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76338",
    userId: "US_2382",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7974",
    installedBatteryId: "BAT_7581",
    chargeState: "97",
    revenue: "10797",
    timestamp: "2025-08-16T00:55:25.378Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76339",
    userId: "US_2648",
    site: "HUB_9196",
    returnedBatteryId: "BAT_6036",
    installedBatteryId: "BAT_7750",
    chargeState: "98",
    revenue: "17989",
    timestamp: "2025-08-16T02:42:45.184Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76340",
    userId: "US_9423",
    site: "HUB_8778",
    returnedBatteryId: "BAT_5356",
    installedBatteryId: "BAT_4226",
    chargeState: "97",
    revenue: "19776",
    timestamp: "2025-08-16T04:46:05.807Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76341",
    userId: "US_3922",
    site: "HUB_9196",
    returnedBatteryId: "BAT_8337",
    installedBatteryId: "BAT_5228",
    chargeState: "99",
    revenue: "11768",
    timestamp: "2025-08-16T05:49:33.445Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76342",
    userId: "US_5533",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9509",
    installedBatteryId: "BAT_268",
    chargeState: "96",
    revenue: "11094",
    timestamp: "2025-08-16T08:14:50.306Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76343",
    userId: "US_0610",
    site: "HUB_4273",
    returnedBatteryId: "BAT_9249",
    installedBatteryId: "BAT_4251",
    chargeState: "89",
    revenue: "11833",
    timestamp: "2025-08-16T08:55:58.378Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76344",
    userId: "US_6607",
    site: "HUB_9097",
    returnedBatteryId: "BAT_2877",
    installedBatteryId: "BAT_3801",
    chargeState: "97",
    revenue: "19030",
    timestamp: "2025-08-16T11:27:56.995Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76345",
    userId: "US_3823",
    site: "HUB_7789",
    returnedBatteryId: "BAT_3932",
    installedBatteryId: "BAT_2326",
    chargeState: "93",
    revenue: "17909",
    timestamp: "2025-08-16T13:25:32.415Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76346",
    userId: "US_1865",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3575",
    installedBatteryId: "BAT_5356",
    chargeState: "88",
    revenue: "11576",
    timestamp: "2025-08-16T14:45:36.693Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76347",
    userId: "US_9006",
    site: "HUB_7789",
    returnedBatteryId: "BAT_9676",
    installedBatteryId: "BAT_9509",
    chargeState: "96",
    revenue: "12724",
    timestamp: "2025-08-16T15:58:59.300Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76348",
    userId: "US_0171",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5384",
    installedBatteryId: "BAT_6583",
    chargeState: "97",
    revenue: "15397",
    timestamp: "2025-08-16T17:19:56.316Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76349",
    userId: "US_9075",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4235",
    installedBatteryId: "BAT_9633",
    chargeState: "98",
    revenue: "19451",
    timestamp: "2025-08-16T19:52:31.212Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76350",
    userId: "US_7624",
    site: "HUB_7789",
    returnedBatteryId: "BAT_057",
    installedBatteryId: "BAT_9249",
    chargeState: "93",
    revenue: "17735",
    timestamp: "2025-08-16T20:57:35.305Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76351",
    userId: "US_7217",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6533",
    installedBatteryId: "BAT_2292",
    chargeState: "97",
    revenue: "11603",
    timestamp: "2025-08-16T21:40:32.728Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76352",
    userId: "US_6253",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6228",
    installedBatteryId: "BAT_9231",
    chargeState: "98",
    revenue: "11337",
    timestamp: "2025-08-17T00:27:10.835Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76353",
    userId: "US_4852",
    site: "HUB_9097",
    returnedBatteryId: "BAT_7294",
    installedBatteryId: "BAT_2823",
    chargeState: "97",
    revenue: "19012",
    timestamp: "2025-08-17T01:28:37.392Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76354",
    userId: "US_9514",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2011",
    installedBatteryId: "BAT_1726",
    chargeState: "91",
    revenue: "16672",
    timestamp: "2025-08-17T02:57:54.139Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76355",
    userId: "US_6544",
    site: "HUB_7789",
    returnedBatteryId: "BAT_6295",
    installedBatteryId: "BAT_2474",
    chargeState: "97",
    revenue: "13019",
    timestamp: "2025-08-17T05:26:39.278Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76356",
    userId: "US_9297",
    site: "HUB_4273",
    returnedBatteryId: "BAT_6959",
    installedBatteryId: "BAT_3127",
    chargeState: "94",
    revenue: "11134",
    timestamp: "2025-08-17T07:54:55.019Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76357",
    userId: "US_8835",
    site: "HUB_4273",
    returnedBatteryId: "BAT_317",
    installedBatteryId: "BAT_8769",
    chargeState: "97",
    revenue: "16362",
    timestamp: "2025-08-17T08:47:08.870Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76358",
    userId: "US_8441",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2072",
    installedBatteryId: "BAT_2381",
    chargeState: "91",
    revenue: "13571",
    timestamp: "2025-08-17T11:37:49.474Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76359",
    userId: "US_8743",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3599",
    installedBatteryId: "BAT_2877",
    chargeState: "91",
    revenue: "17603",
    timestamp: "2025-08-17T12:27:43.138Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76360",
    userId: "US_9908",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7810",
    installedBatteryId: "BAT_6295",
    chargeState: "94",
    revenue: "16358",
    timestamp: "2025-08-17T12:45:33.844Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76361",
    userId: "US_0511",
    site: "HUB_9097",
    returnedBatteryId: "BAT_6670",
    installedBatteryId: "BAT_3599",
    chargeState: "91",
    revenue: "14289",
    timestamp: "2025-08-17T12:47:55.798Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76427",
    userId: "US_5892",
    site: "HUB_7789",
    returnedBatteryId: "BAT_9064",
    installedBatteryId: "BAT_6357",
    chargeState: "90",
    revenue: "17962",
    timestamp: "2025-08-18T06:09:07.543Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76428",
    userId: "US_7868",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6236",
    installedBatteryId: "BAT_2289",
    chargeState: "91",
    revenue: "13798",
    timestamp: "2025-08-18T06:36:31.875Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76429",
    userId: "US_8269",
    site: "HUB_7188",
    returnedBatteryId: "BAT_2158",
    installedBatteryId: "BAT_6228",
    chargeState: "95",
    revenue: "18192",
    timestamp: "2025-08-18T08:06:03.945Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76430",
    userId: "US_7322",
    site: "HUB_0611",
    returnedBatteryId: "BAT_3370",
    installedBatteryId: "BAT_7321",
    chargeState: "88",
    revenue: "15037",
    timestamp: "2025-08-18T10:44:00.622Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76431",
    userId: "US_9512",
    site: "HUB_1651",
    returnedBatteryId: "BAT_6359",
    installedBatteryId: "BAT_7997",
    chargeState: "92",
    revenue: "10353",
    timestamp: "2025-08-18T12:14:14.162Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76432",
    userId: "US_5476",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5035",
    installedBatteryId: "BAT_6863",
    chargeState: "99",
    revenue: "15614",
    timestamp: "2025-08-18T12:25:20.045Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76433",
    userId: "US_4205",
    site: "HUB_9097",
    returnedBatteryId: "BAT_1616",
    installedBatteryId: "BAT_6335",
    chargeState: "95",
    revenue: "14644",
    timestamp: "2025-08-18T13:02:40.942Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76434",
    userId: "US_6006",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8539",
    installedBatteryId: "BAT_4788",
    chargeState: "94",
    revenue: "10700",
    timestamp: "2025-08-18T16:00:43.795Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76435",
    userId: "US_8276",
    site: "HUB_0611",
    returnedBatteryId: "BAT_5143",
    installedBatteryId: "BAT_3212",
    chargeState: "98",
    revenue: "16438",
    timestamp: "2025-08-18T16:56:24.395Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76436",
    userId: "US_5227",
    site: "HUB_0466",
    returnedBatteryId: "BAT_7557",
    installedBatteryId: "BAT_9064",
    chargeState: "98",
    revenue: "11100",
    timestamp: "2025-08-18T18:37:37.638Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76437",
    userId: "US_2839",
    site: "HUB_8188",
    returnedBatteryId: "BAT_1817",
    installedBatteryId: "BAT_8744",
    chargeState: "95",
    revenue: "18410",
    timestamp: "2025-08-18T20:52:07.696Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76438",
    userId: "US_8522",
    site: "HUB_9196",
    returnedBatteryId: "BAT_3038",
    installedBatteryId: "BAT_4966",
    chargeState: "91",
    revenue: "16584",
    timestamp: "2025-08-18T22:22:34.165Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76439",
    userId: "US_0640",
    site: "HUB_9196",
    returnedBatteryId: "BAT_1306",
    installedBatteryId: "BAT_110",
    chargeState: "95",
    revenue: "13944",
    timestamp: "2025-08-18T22:47:34.146Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76440",
    userId: "US_2288",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5333",
    installedBatteryId: "BAT_9388",
    chargeState: "90",
    revenue: "14898",
    timestamp: "2025-08-19T01:21:43.607Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76441",
    userId: "US_2876",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5161",
    installedBatteryId: "BAT_8429",
    chargeState: "98",
    revenue: "15079",
    timestamp: "2025-08-19T03:53:21.361Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76442",
    userId: "US_2498",
    site: "HUB_8778",
    returnedBatteryId: "BAT_322",
    installedBatteryId: "BAT_8665",
    chargeState: "99",
    revenue: "13476",
    timestamp: "2025-08-19T06:11:57.971Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76443",
    userId: "US_5582",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5563",
    installedBatteryId: "BAT_1970",
    chargeState: "92",
    revenue: "11040",
    timestamp: "2025-08-19T06:56:09.700Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76444",
    userId: "US_8165",
    site: "HUB_1651",
    returnedBatteryId: "BAT_3270",
    installedBatteryId: "BAT_8294",
    chargeState: "87",
    revenue: "19793",
    timestamp: "2025-08-19T07:40:11.716Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76445",
    userId: "US_1917",
    site: "HUB_0611",
    returnedBatteryId: "BAT_9175",
    installedBatteryId: "BAT_6668",
    chargeState: "93",
    revenue: "13259",
    timestamp: "2025-08-19T09:09:51.075Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76446",
    userId: "US_4789",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9240",
    installedBatteryId: "BAT_7312",
    chargeState: "99",
    revenue: "17178",
    timestamp: "2025-08-19T11:39:06.706Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76447",
    userId: "US_7835",
    site: "HUB_9097",
    returnedBatteryId: "BAT_3882",
    installedBatteryId: "BAT_2549",
    chargeState: "91",
    revenue: "12124",
    timestamp: "2025-08-19T11:59:47.763Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76448",
    userId: "US_0439",
    site: "HUB_8778",
    returnedBatteryId: "BAT_4195",
    installedBatteryId: "BAT_5554",
    chargeState: "88",
    revenue: "16037",
    timestamp: "2025-08-19T14:32:13.562Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76449",
    userId: "US_8660",
    site: "HUB_8778",
    returnedBatteryId: "BAT_9596",
    installedBatteryId: "BAT_2158",
    chargeState: "91",
    revenue: "14096",
    timestamp: "2025-08-19T15:37:58.441Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76450",
    userId: "US_6059",
    site: "HUB_4273",
    returnedBatteryId: "BAT_1096",
    installedBatteryId: "BAT_8804",
    chargeState: "90",
    revenue: "17970",
    timestamp: "2025-08-19T16:50:35.006Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76451",
    userId: "US_6223",
    site: "HUB_1651",
    returnedBatteryId: "BAT_4480",
    installedBatteryId: "BAT_1717",
    chargeState: "92",
    revenue: "15570",
    timestamp: "2025-08-19T16:53:52.054Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76452",
    userId: "US_3127",
    site: "HUB_9097",
    returnedBatteryId: "BAT_1914",
    installedBatteryId: "BAT_4801",
    chargeState: "90",
    revenue: "19417",
    timestamp: "2025-08-19T17:13:23.451Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76453",
    userId: "US_2382",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7581",
    installedBatteryId: "BAT_2041",
    chargeState: "93",
    revenue: "17449",
    timestamp: "2025-08-19T17:19:41.420Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76454",
    userId: "US_2648",
    site: "HUB_7188",
    returnedBatteryId: "BAT_7750",
    installedBatteryId: "BAT_7557",
    chargeState: "94",
    revenue: "15517",
    timestamp: "2025-08-19T18:32:23.992Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76455",
    userId: "US_9423",
    site: "HUB_9097",
    returnedBatteryId: "BAT_4226",
    installedBatteryId: "BAT_2572",
    chargeState: "93",
    revenue: "13082",
    timestamp: "2025-08-19T20:03:11.364Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76456",
    userId: "US_3922",
    site: "HUB_1651",
    returnedBatteryId: "BAT_5228",
    installedBatteryId: "BAT_3882",
    chargeState: "93",
    revenue: "18099",
    timestamp: "2025-08-19T21:57:19.283Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76457",
    userId: "US_5533",
    site: "HUB_0611",
    returnedBatteryId: "BAT_268",
    installedBatteryId: "BAT_7810",
    chargeState: "91",
    revenue: "15884",
    timestamp: "2025-08-19T22:16:48.575Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76458",
    userId: "US_0610",
    site: "HUB_0611",
    returnedBatteryId: "BAT_4251",
    installedBatteryId: "BAT_4878",
    chargeState: "90",
    revenue: "16029",
    timestamp: "2025-08-20T00:37:58.500Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76459",
    userId: "US_6607",
    site: "HUB_8188",
    returnedBatteryId: "BAT_3801",
    installedBatteryId: "BAT_8042",
    chargeState: "94",
    revenue: "19907",
    timestamp: "2025-08-20T01:00:58.864Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76460",
    userId: "US_3823",
    site: "HUB_4273",
    returnedBatteryId: "BAT_2326",
    installedBatteryId: "BAT_4641",
    chargeState: "89",
    revenue: "15155",
    timestamp: "2025-08-20T04:00:01.508Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76461",
    userId: "US_1865",
    site: "HUB_7188",
    returnedBatteryId: "BAT_5356",
    installedBatteryId: "BAT_4877",
    chargeState: "92",
    revenue: "17410",
    timestamp: "2025-08-20T05:01:38.762Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76462",
    userId: "US_9006",
    site: "HUB_8188",
    returnedBatteryId: "BAT_9509",
    installedBatteryId: "BAT_044",
    chargeState: "99",
    revenue: "17084",
    timestamp: "2025-08-20T06:56:28.833Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76528",
    userId: "US_0171",
    site: "HUB_7188",
    returnedBatteryId: "BAT_6583",
    installedBatteryId: "BAT_6378",
    chargeState: "92",
    revenue: "13795",
    timestamp: "2025-08-21T07:01:06.292Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76529",
    userId: "US_9075",
    site: "HUB_9097",
    returnedBatteryId: "BAT_9633",
    installedBatteryId: "BAT_7266",
    chargeState: "96",
    revenue: "15939",
    timestamp: "2025-08-21T08:47:34.297Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76530",
    userId: "US_7624",
    site: "HUB_9196",
    returnedBatteryId: "BAT_9249",
    installedBatteryId: "BAT_8883",
    chargeState: "91",
    revenue: "16767",
    timestamp: "2025-08-21T09:34:56.931Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76531",
    userId: "US_7217",
    site: "HUB_9196",
    returnedBatteryId: "BAT_2292",
    installedBatteryId: "BAT_9509",
    chargeState: "88",
    revenue: "14933",
    timestamp: "2025-08-21T10:09:32.857Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76532",
    userId: "US_6253",
    site: "HUB_7188",
    returnedBatteryId: "BAT_9231",
    installedBatteryId: "BAT_7303",
    chargeState: "91",
    revenue: "10030",
    timestamp: "2025-08-21T11:45:35.907Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76533",
    userId: "US_4852",
    site: "HUB_9097",
    returnedBatteryId: "BAT_2823",
    installedBatteryId: "BAT_7949",
    chargeState: "87",
    revenue: "16679",
    timestamp: "2025-08-21T11:57:25.033Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76534",
    userId: "US_9514",
    site: "HUB_4273",
    returnedBatteryId: "BAT_1726",
    installedBatteryId: "BAT_8421",
    chargeState: "98",
    revenue: "10758",
    timestamp: "2025-08-21T14:27:24.967Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76535",
    userId: "US_6544",
    site: "HUB_0466",
    returnedBatteryId: "BAT_2474",
    installedBatteryId: "BAT_5912",
    chargeState: "97",
    revenue: "10228",
    timestamp: "2025-08-21T16:15:08.455Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76536",
    userId: "US_9297",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3127",
    installedBatteryId: "BAT_1281",
    chargeState: "88",
    revenue: "17933",
    timestamp: "2025-08-21T16:41:59.916Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76537",
    userId: "US_8835",
    site: "HUB_7789",
    returnedBatteryId: "BAT_8769",
    installedBatteryId: "BAT_5005",
    chargeState: "96",
    revenue: "16666",
    timestamp: "2025-08-21T19:19:15.743Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76538",
    userId: "US_8441",
    site: "HUB_1651",
    returnedBatteryId: "BAT_2381",
    installedBatteryId: "BAT_8769",
    chargeState: "89",
    revenue: "13824",
    timestamp: "2025-08-21T19:49:17.789Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76539",
    userId: "US_8743",
    site: "HUB_0466",
    returnedBatteryId: "BAT_2877",
    installedBatteryId: "BAT_7305",
    chargeState: "95",
    revenue: "18570",
    timestamp: "2025-08-21T20:49:36.278Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76540",
    userId: "US_9908",
    site: "HUB_9196",
    returnedBatteryId: "BAT_6295",
    installedBatteryId: "BAT_7422",
    chargeState: "99",
    revenue: "14425",
    timestamp: "2025-08-21T21:38:08.895Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76541",
    userId: "US_0511",
    site: "HUB_0466",
    returnedBatteryId: "BAT_3599",
    installedBatteryId: "BAT_8897",
    chargeState: "87",
    revenue: "12162",
    timestamp: "2025-08-21T21:45:44.223Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76542",
    userId: "US_5892",
    site: "HUB_0611",
    returnedBatteryId: "BAT_6357",
    installedBatteryId: "BAT_441",
    chargeState: "88",
    revenue: "10061",
    timestamp: "2025-08-21T23:50:22.687Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76543",
    userId: "US_7868",
    site: "HUB_8188",
    returnedBatteryId: "BAT_2289",
    installedBatteryId: "BAT_5333",
    chargeState: "87",
    revenue: "10134",
    timestamp: "2025-08-22T01:59:38.311Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76544",
    userId: "US_8269",
    site: "HUB_8778",
    returnedBatteryId: "BAT_6228",
    installedBatteryId: "BAT_8413",
    chargeState: "87",
    revenue: "18415",
    timestamp: "2025-08-22T02:21:41.351Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76545",
    userId: "US_7322",
    site: "HUB_8778",
    returnedBatteryId: "BAT_7321",
    installedBatteryId: "BAT_9909",
    chargeState: "95",
    revenue: "10121",
    timestamp: "2025-08-22T03:08:53.214Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76546",
    userId: "US_9512",
    site: "HUB_4273",
    returnedBatteryId: "BAT_7997",
    installedBatteryId: "BAT_378",
    chargeState: "98",
    revenue: "14259",
    timestamp: "2025-08-22T03:53:10.236Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76547",
    userId: "US_5476",
    site: "HUB_7789",
    returnedBatteryId: "BAT_6863",
    installedBatteryId: "BAT_6271",
    chargeState: "89",
    revenue: "18856",
    timestamp: "2025-08-22T05:06:17.895Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76548",
    userId: "US_4205",
    site: "HUB_7789",
    returnedBatteryId: "BAT_6335",
    installedBatteryId: "BAT_9373",
    chargeState: "90",
    revenue: "19025",
    timestamp: "2025-08-22T06:17:03.830Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76549",
    userId: "US_6006",
    site: "HUB_4273",
    returnedBatteryId: "BAT_4788",
    installedBatteryId: "BAT_440",
    chargeState: "89",
    revenue: "13669",
    timestamp: "2025-08-22T06:44:55.055Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76550",
    userId: "US_8276",
    site: "HUB_9097",
    returnedBatteryId: "BAT_3212",
    installedBatteryId: "BAT_4087",
    chargeState: "96",
    revenue: "11953",
    timestamp: "2025-08-22T06:47:42.731Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76551",
    userId: "US_5227",
    site: "HUB_8188",
    returnedBatteryId: "BAT_9064",
    installedBatteryId: "BAT_5055",
    chargeState: "99",
    revenue: "12559",
    timestamp: "2025-08-22T08:55:09.262Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76552",
    userId: "US_2839",
    site: "HUB_0466",
    returnedBatteryId: "BAT_8744",
    installedBatteryId: "BAT_5720",
    chargeState: "98",
    revenue: "17008",
    timestamp: "2025-08-22T11:29:05.802Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76553",
    userId: "US_8522",
    site: "HUB_7789",
    returnedBatteryId: "BAT_4966",
    installedBatteryId: "BAT_5945",
    chargeState: "91",
    revenue: "16912",
    timestamp: "2025-08-22T12:24:49.100Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76554",
    userId: "US_0640",
    site: "HUB_8778",
    returnedBatteryId: "BAT_110",
    installedBatteryId: "BAT_5216",
    chargeState: "98",
    revenue: "15285",
    timestamp: "2025-08-22T12:54:38.176Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76555",
    userId: "US_2288",
    site: "HUB_1651",
    returnedBatteryId: "BAT_9388",
    installedBatteryId: "BAT_3718",
    chargeState: "97",
    revenue: "11008",
    timestamp: "2025-08-22T15:23:44.519Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76556",
    userId: "US_2876",
    site: "HUB_8188",
    returnedBatteryId: "BAT_8429",
    installedBatteryId: "BAT_3058",
    chargeState: "95",
    revenue: "10039",
    timestamp: "2025-08-22T16:16:09.476Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76557",
    userId: "US_2498",
    site: "HUB_7188",
    returnedBatteryId: "BAT_8665",
    installedBatteryId: "BAT_9217",
    chargeState: "98",
    revenue: "16727",
    timestamp: "2025-08-22T17:03:47.540Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76558",
    userId: "US_5582",
    site: "HUB_9196",
    returnedBatteryId: "BAT_1970",
    installedBatteryId: "BAT_4903",
    chargeState: "87",
    revenue: "19828",
    timestamp: "2025-08-22T17:11:40.272Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76559",
    userId: "US_8165",
    site: "HUB_9196",
    returnedBatteryId: "BAT_8294",
    installedBatteryId: "BAT_5384",
    chargeState: "90",
    revenue: "15508",
    timestamp: "2025-08-22T17:41:22.574Z",
    paymentMethod: "Mobile Wallet",
  },
  {
    id: "TX_76560",
    userId: "US_1917",
    site: "HUB_9097",
    returnedBatteryId: "BAT_6668",
    installedBatteryId: "BAT_7956",
    chargeState: "97",
    revenue: "16226",
    timestamp: "2025-08-22T18:06:12.801Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76561",
    userId: "US_4789",
    site: "HUB_9196",
    returnedBatteryId: "BAT_7312",
    installedBatteryId: "BAT_7581",
    chargeState: "94",
    revenue: "18425",
    timestamp: "2025-08-22T20:10:49.583Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76562",
    userId: "US_7835",
    site: "HUB_7789",
    returnedBatteryId: "BAT_2549",
    installedBatteryId: "BAT_6785",
    chargeState: "94",
    revenue: "15514",
    timestamp: "2025-08-22T20:52:05.483Z",
    paymentMethod: "Debit Card",
  },
  {
    id: "TX_76563",
    userId: "US_0439",
    site: "HUB_0611",
    returnedBatteryId: "BAT_5554",
    installedBatteryId: "BAT_9977",
    chargeState: "92",
    revenue: "18148",
    timestamp: "2025-08-22T21:36:07.892Z",
    paymentMethod: "Credit Card",
  },
  {
    id: "TX_76564",
    userId: "US_8660",
    site: "HUB_0466",
    returnedBatteryId: "BAT_2158",
    installedBatteryId: "BAT_5554",
    chargeState: "94",
    revenue: "17865",
    timestamp: "2025-08-22T23:21:08.112Z",
    paymentMethod: "Cash",
  },
  {
    id: "TX_76565",
    userId: "US_6059",
    site: "HUB_8778",
    returnedBatteryId: "BAT_8804",
    installedBatteryId: "BAT_5249",
    chargeState: "99",
    revenue: "14800",
    timestamp: "2025-08-22T23:42:49.442Z",
    paymentMethod: "Debit Card",
  },
];

const Transaction = () => {
  const [tableSearchTerm, setTableSearchTerm] = useState("");

  const filteredTransactions = useMemo(() => {
    return transactions.filter((transaction) =>
      Object.values(transaction).some((value) =>
        value.toString().toLowerCase().includes(tableSearchTerm.toLowerCase()),
      ),
    );
  }, [transactions, tableSearchTerm]);

  const SummarySection = () => {
    const summaryData = [
      { value: filteredTransactions.length, label: "Transactions" },
      {
        value: filteredTransactions.reduce(
          (sum, t) => sum + Number.parseInt(t.revenue),
          0,
        ),
        unit: "Rp",
        label: "Revenue",
      },
      {
        value: new Set(filteredTransactions.map((t) => t.userId)).size,
        unit: "Users",
        label: "Unique Active Customers",
      },
    ];

    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-4 mb-6">
        <h2 className="text-sm font-semibold mb-4">Summary</h2>
        <div className="grid grid-cols-3 gap-4">
          {summaryData.map((item, index) => (
            <div
              key={index}
              className={`${index !== 0 ? "border-l border-gray-200 pl-4" : ""}`}
            >
              <div className="flex items-baseline">
                <span className="text-2xl font-bold text-gray-800">
                  {item.value.toLocaleString()}
                </span>
                {item.unit && (
                  <span className="ml-1 text-sm text-gray-500">
                    {item.unit}
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-500">{item.label}</p>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const TransactionChart = () => {
    const groupedByDate =
      transactions.reduce(
        (acc, t) => {
          const date = t.timestamp.split("T")[0];
          if (!acc[date]) {
            acc[date] = { revenue: 0, count: 0 };
          }
          acc[date].revenue += Number.parseInt(t.revenue);
          acc[date].count += 1;
          return acc;
        },
        {} as Record<string, { revenue: number; count: number }>,
      ) ?? {};

    const chartData = Object.entries(groupedByDate).map(([date, data]) => ({
      date,
      revenue: data.revenue,
      count: data.count,
    }));

    const maxRevenue = Math.max(...chartData.map((d) => d.revenue));
    const maxCount = Math.max(...chartData.map((d) => d.count));

    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">Transaction Revenue</h2>
        <ResponsiveContainer width="100%" height={200}>
          <ComposedChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis
              yAxisId="left"
              orientation="left"
              domain={[0, "dataMax"]}
              tickFormatter={(value) => `${Math.round(value / 1000)}k Rp`}
              ticks={[0, maxRevenue / 3, (maxRevenue / 3) * 2, maxRevenue]}
            />
            <YAxis
              yAxisId="right"
              orientation="right"
              domain={[0, "dataMax"]}
              ticks={[0, maxCount / 2, maxCount]}
            />
            <Tooltip />
            <Bar
              dataKey="revenue"
              fill="#8884d8"
              name="Revenue"
              yAxisId="left"
            />
            <Line
              dataKey="count"
              stroke="#82ca9d"
              name="Transaction Count"
              yAxisId="right"
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    );
  };

  return (
    <div className="w-full min-h-screen p-4">
      <div className="flex justify-between items-center pt-4 pb-5">
        <div className="text-heading1 text-space50">Transaction History</div>
        <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
          Export CSV
        </button>
      </div>

      {/* Summary Section */}
      <SummarySection />

      {/* Transaction Chart */}
      <TransactionChart />

      {/* Transactions Table */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-4 flex justify-between items-center">
          <h2 className="text-lg font-semibold">Transactions</h2>
          <div className="relative w-1/3">
            <input
              type="text"
              placeholder="Search"
              className="w-full p-2 pl-8 rounded border"
              onChange={(e) => setTableSearchTerm(e.target.value)}
            />
            <svg
              className="w-4 h-4 absolute left-2.5 top-3 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <th className="px-6 py-3">Transaction ID</th>
                <th className="px-6 py-3">User ID</th>
                <th className="px-6 py-3">Site</th>
                <th className="px-6 py-3">Returned Battery ID</th>
                <th className="px-6 py-3">Installed Battery ID</th>
                <th className="px-6 py-3">Installed Battery Charge State</th>
                <th className="px-6 py-3">Revenue (IDR)</th>
                <th className="px-6 py-3">Date</th>
                <th className="px-6 py-3">Time</th>
                <th className="px-6 py-3">Payment Method</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTransactions.map((transaction) => (
                <tr key={transaction.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.userId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.site}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.returnedBatteryId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.installedBatteryId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.chargeState}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {Number.parseInt(transaction.revenue).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.timestamp.split("T")[0]}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.timestamp.split("T")[1].split(".")[0]}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.paymentMethod}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Transaction;
