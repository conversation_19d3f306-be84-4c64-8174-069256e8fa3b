// This file generates a list of transactions for the next 30 days.
// This is just a simulation for demo purposes.

import fs from "node:fs";

interface Transation {
  id: string;
  userId: string; // US_{id}
  site: string; // HUB_{id}
  returnedBatteryId: string; // BAT_{id}
  installedBatteryId: string; // BAT_{id}
  chargeState: string; // 0-100%
  revenue: string; // IDR (10000-20000 depending on charge state)
  timestamp: string; // ISO 8601
  paymentMethod: "Credit Card" | "Debit Card" | "Cash" | "Mobile Wallet";
}

type Battery = {
  id: string;
  chargeState: string;
};

type BatteryRecord = Battery & {
  userId: string;
};

const generate_id = (prefix: string, length: number) => {
  return `${prefix}_${(Math.floor(Math.random() * 10000) + 1).toString().padStart(length, "0")}`;
};

// generate a list of 100 users
const users = Array.from({ length: 100 }, (_, i) => generate_id("US", 4));

// generate a list of 10 sites
const sites = Array.from({ length: 10 }, (_, i) => generate_id("HUB", 4));

// generate a list of 400 batteries with initial charge state of 98-100%
const batteries: Battery[] = Array.from({ length: 400 }, (_, i) => {
  return {
    id: generate_id("BAT", 3),
    chargeState: Math.floor(Math.random() * 3 + 98).toString(),
    userId: null,
  };
});

const batteryCheckOuts: Map<string, Battery> = new Map();
const generate_when = <T>(
  generator: () => T,
  predicate: (value: T) => boolean,
) => {
  let value = generator();
  while (!predicate(value)) {
    value = generator();
  }
  return value;
};

// check out 50 batteries
for (let i = 0; i < 50; i++) {
  const newBattery = generate_when(
    () => batteries[Math.floor(Math.random() * batteries.length)],
    (battery) =>
      !Array.from(batteryCheckOuts.values()).some((b) => b.id === battery.id),
  );
  const newUser = generate_when(
    () => users[Math.floor(Math.random() * users.length)],
    (user) => !batteryCheckOuts.has(user),
  );
  batteryCheckOuts.set(newUser, newBattery);
}

// generate an increasing list of transactions per day for the next 30 days
let start_date = new Date(Date.now() - 31 * 24 * 60 * 60 * 1000);
let start_transactions = 5;
const transactions = Array.from({ length: 30 }, (_, i) => {
  // increment start_date by 1 day
  start_date.setDate(start_date.getDate() + 1);
  start_date.setHours(0, 0, 0, 0);

  // modify start_transactions by -4 to 12%
  const percentage_change = (Math.ceil(Math.random() * 15) + 96) / 100;
  start_transactions = Math.round(start_transactions * percentage_change);

  return Array.from({ length: start_transactions + i }, (_, j) => {
    // pop random record from batteryCheckOuts
    const [userId, returnedBattery] = batteryCheckOuts.entries().next().value;
    batteryCheckOuts.delete(userId);

    // find a random battery that is not checked out
    const newBattery = generate_when(
      () => batteries[Math.floor(Math.random() * batteries.length)],
      (battery) =>
        !Array.from(batteryCheckOuts.values()).some((b) => b.id === battery.id),
    );
    batteryCheckOuts.set(userId, newBattery);

    // generate a random charge state from 87-100%
    const chargeState = Math.floor(Math.random() * 13 + 87).toString();

    // calculate revenue
    const revenue = Math.floor(Math.random() * 10000 + 10000).toString();

    // generate a random payment method
    const paymentMethod = [
      "Credit Card",
      "Debit Card",
      "Cash",
      "Mobile Wallet",
    ][Math.floor(Math.random() * 4)];

    // increment start_date by up to 24 * 60 * 60 * 1000 / start_transactions
    const max_increase = (24 * 60 * 60 * 1000) / start_transactions;
    start_date = new Date(
      start_date.getTime() + Math.floor(Math.random() * max_increase),
    );
    console.log(start_date);

    return {
      id: `TX_${start_transactions + i * 100 + j + 73619}`,
      userId: userId,
      site: sites[Math.floor(Math.random() * sites.length)],
      returnedBatteryId: returnedBattery.id,
      installedBatteryId: newBattery.id,
      chargeState: chargeState,
      revenue: revenue,
      timestamp: start_date.toISOString(),
      paymentMethod: paymentMethod,
    };
  });
});

console.log(transactions);

const transactions_sorted = transactions
  .flat()
  .sort((a, b) => a.timestamp.localeCompare(b.timestamp));

// save transactions ot json
fs.writeFileSync(
  "transactions.json",
  JSON.stringify(transactions_sorted, null, 2),
);
