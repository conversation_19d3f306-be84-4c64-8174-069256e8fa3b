import { PlaceTile } from "components/places/PlaceTile";
import { useAuth } from "context/AuthContext";

import { useEffect, useState } from "react";
import { NavLink } from "react-router-dom";

import ButtonComponent from "../../components/uikit/button";
import AddFleetModal from "./AddFleetModal";
import AddSiteModal from "./AddSiteModal";
import { MapView } from "./mapView";
import { useAppData } from "hooks/useAppData";

const LocationSelection = ({ locationType }) => {
  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSiteModalOpen, setIsSiteModalOpen] = useState(false);
  const [isFleetModalOpen, setIsFleetModalOpen] = useState(false);
  const [currentType, setCurrentType] = useState(null);

  const { permissions } = useAuth();
  const { fetchSitesIfNeeded, fetchFleetsIfNeeded, addNewSite, addNewFleet } =
    useAppData();

  const fetchLocations = () => {
    setIsLoading(true);
    const fetchFunc =
      locationType === "site" ? fetchSitesIfNeeded : fetchFleetsIfNeeded;

    fetchFunc()
      .then((data) => {
        setLocations(data);
        setIsLoading(false);
        setCurrentType(locationType);
      })
      .catch((error) => {
        console.error(`Error fetching ${locationType} data:`, error);
        setIsLoading(false);
      });
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: refetch on change location type only
  useEffect(() => {
    fetchLocations();
  }, [locationType]);

  const handleOpenSiteModal = () => setIsSiteModalOpen(true);
  const handleCloseSiteModal = () => setIsSiteModalOpen(false);
  const handleOpenFleetModal = () => setIsFleetModalOpen(true);
  const handleCloseFleetModal = () => setIsFleetModalOpen(false);

  const handleAddSite = async (siteData) => {
    return addNewSite(siteData);
  };

  const handleAddFleet = async (fleetData) => {
    return addNewFleet(fleetData);
  };

  const renderLocationTile = (location) => {
    return <PlaceTile place={location} placeType={locationType} />;
  };

  const getLocationId = (location) => {
    return locationType === "site" ? location.siteId : location.fleetId;
  };

  if (isLoading || currentType !== locationType) {
    return (
      <div className="flex w-full min-h-screen items-center justify-center">
        <div className="text-lg font-medium text-gray-600">
          Loading {locationType} data...
        </div>
      </div>
    );
  }

  const renderAddButton = () => {
    if (permissions.includes("write:ingest_things")) {
      return locationType === "site" ? (
        <ButtonComponent.Pill onClick={handleOpenSiteModal}>
          + Add Site
        </ButtonComponent.Pill>
      ) : (
        <ButtonComponent.Pill onClick={handleOpenFleetModal}>
          + Add Fleet
        </ButtonComponent.Pill>
      );
    }
    return null;
  };

  const renderContent = () => {
    if (locationType === "site") {
      return (
        <div className="flex flex-col lg:flex-row gap-4 mr-4 mb-4">
          <div className="w-full lg:w-1/2">
            {locations.map((location) => (
              <NavLink
                key={getLocationId(location)}
                to={`/${locationType}s/${getLocationId(location)}`}
              >
                {renderLocationTile(location)}
              </NavLink>
            ))}
          </div>
          <div className="w-full lg:w-1/2">
            <div className="bg-white rounded-md shadow border border-zinc-300 h-screen">
              <MapView locations={locations} />
            </div>
          </div>
        </div>
      );
    }
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mr-4">
        {locations.map((location) => (
          <NavLink
            key={getLocationId(location)}
            to={`/${locationType}s/${getLocationId(location)}`}
            className="block w-full"
          >
            {renderLocationTile(location)}
          </NavLink>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen grow">
      <div className="flex w-full min-h-screen">
        <div className="flex-1 px-4">
          <div className="flex justify-start items-center pt-4 pb-5 gap-4">
            <div className="text-heading1 text-space50">
              {locationType === "site" ? "Sites" : "Fleets"} / All
            </div>
            {renderAddButton()}
          </div>
          <div className="mr-0">{renderContent()}</div>
        </div>
      </div>
      {locationType === "site" && (
        <AddSiteModal
          isOpen={isSiteModalOpen}
          onClose={handleCloseSiteModal}
          onAddSite={handleAddSite}
        />
      )}
      {locationType === "fleet" && (
        <AddFleetModal
          isOpen={isFleetModalOpen}
          onClose={handleCloseFleetModal}
          onAddFleet={handleAddFleet}
        />
      )}
    </div>
  );
};

export default LocationSelection;
