import { Pin } from "components";
import { mapboxConfig } from "configs/mapbox";
import { useEffect, useRef } from "react";
import { Map as MapboxMap, Marker } from "react-map-gl";
import { useNavigate } from "react-router-dom";
import { US_CENTER_LOCATION } from "utils/geolocation";

export const MapView = ({
  locations,
  projection = "equirectangular",
  autoCenter = false,
  clickable = true,
  initialZoom = 12,
}) => {
  const navigate = useNavigate();

  // HACK: FIND THE CLOSEST LOCATION TO THE US CENTER
  const closestLocation = locations.reduce(
    (closest, location) => {
      const distance = Math.sqrt(
        (location.latitude - US_CENTER_LOCATION.latitude) ** 2 +
          (location.longitude - US_CENTER_LOCATION.longitude) ** 2,
      );
      return distance < closest.distance ? { location, distance } : closest;
    },
    { location: null, distance: Number.POSITIVE_INFINITY },
  );

  const closestUsLocation = closestLocation.location ?? {
    latitude: 0,
    longitude: 0,
  };
  const mapRef = useRef(null);

  // biome-ignore lint/correctness/useExhaustiveDependencies: add mapRef.current so it re-renders when the map is ready
  useEffect(() => {
    if (!mapRef.current) return;

    if (!autoCenter) return;

    const minLat = Math.min(
      ...locations.map((location) => location.latitude).filter(Boolean),
    );
    const maxLat = Math.max(
      ...locations.map((location) => location.latitude).filter(Boolean),
    );
    const minLong = Math.min(
      ...locations.map((location) => location.longitude).filter(Boolean),
    );
    const maxLong = Math.max(
      ...locations.map((location) => location.longitude).filter(Boolean),
    );

    mapRef.current.fitBounds(
      [
        [minLong, minLat],
        [maxLong, maxLat],
      ],
      {
        padding: 15,
      },
    );
  }, [locations, mapRef.current, autoCenter]);

  return (
    <MapboxMap
      ref={mapRef}
      key={`${closestUsLocation.latitude}-${closestUsLocation.longitude}`}
      initialViewState={{
        longitude: closestUsLocation.longitude,
        latitude: closestUsLocation.latitude,
        zoom: initialZoom,
      }}
      mapStyle="mapbox://styles/mapbox/light-v11"
      mapboxAccessToken={mapboxConfig.token}
      projection={projection}
      style={{
        flex: 1,
      }}
    >
      {locations.map(
        (location, index) =>
          location.latitude &&
          location.longitude && (
            <Marker
              key={`location-${location.siteId ?? location.fleetId}`}
              longitude={location.longitude}
              latitude={location.latitude}
              anchor="center"
              className="flex flex-row"
              onClick={() => {
                if (clickable) {
                  navigate(
                    `/${location.siteId ? "sites" : "fleets"}/${
                      location.siteId ?? location.fleetId
                    }`,
                  );
                }
              }}
            >
              <Pin
                color={"#6db5d1"}
                id={index + 1}
                size={28}
                locationId={location.siteId ?? location.fleetId}
                locationName={location.siteName ?? location.fleetName}
              />
            </Marker>
          ),
      )}
    </MapboxMap>
  );
};
