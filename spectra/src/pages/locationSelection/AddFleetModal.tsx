import { useAdminPlacesAPI } from "api/ingestion/admin/places";
import type React from "react";
import { useState } from "react";
import { FaPlus, FaTrash } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

interface Attribute {
  name: string;
  value: string;
}

interface AddFleetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddFleet: (data: {
    fleetName: string;
    attributes: Record<string, string>;
  }) => Promise<{
    success: boolean;
    fleetId?: string;
    error?: { message: string };
  }>;
}

const AddFleetModal = ({ isOpen, onClose, onAddFleet }: AddFleetModalProps) => {
  const [fleetName, setFleetName] = useState("");
  const [attributes, setAttributes] = useState<Attribute[]>([
    { name: "", value: "" },
  ]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [errorMessage, setErrorMessage] = useState("");
  const [isBootstrapping, setIsBootstrapping] = useState(false);
  const navigate = useNavigate();
  const { bootstrapIot, bootstrapTimeseries } = useAdminPlacesAPI();

  const handleFleetNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFleetName(e.target.value);
  };

  const handleAttributeChange = (
    index: number,
    field: keyof Attribute,
    value: string,
  ) => {
    const updatedAttributes = [...attributes];
    updatedAttributes[index][field] = value;
    setAttributes(updatedAttributes);
  };

  const addAttribute = () => {
    setAttributes([...attributes, { name: "", value: "" }]);
  };

  const removeAttribute = (index: number) => {
    const updatedAttributes = attributes.filter((_, i) => i !== index);
    setAttributes(updatedAttributes);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!fleetName.trim()) {
      newErrors.fleetName = "Fleet Name is required";
    }
    attributes.forEach((attr, index) => {
      if (attr.name.trim() && !attr.value.trim()) {
        newErrors[`value-${index}`] = "Value is required when name is provided";
      }
      if (!attr.name.trim() && attr.value.trim()) {
        newErrors[`name-${index}`] = "Name is required when value is provided";
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddFleet = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      const formData = {
        fleetName,
        attributes: Object.fromEntries(
          attributes
            .filter((attr) => attr.name.trim() && attr.value.trim())
            .map((attr) => [attr.name, attr.value]),
        ),
      };

      try {
        const result = await onAddFleet(formData);
        if (result.success && result.fleetId) {
          setFleetName("");
          setAttributes([{ name: "", value: "" }]);
          setErrors({});
          setErrorMessage("");
          onClose();

          // Bootstrap the fleet
          const fleetId = JSON.parse(result.fleetId).replace(/^"(.*)"$/, "$1");
          setIsBootstrapping(true);
          try {
            await Promise.all([
              bootstrapIot("fleet", fleetId),
              bootstrapTimeseries("fleet", fleetId),
            ]);
          } catch (error) {
            console.error("Failed to bootstrap fleet:", error);
          } finally {
            setIsBootstrapping(false);
          }

          // Redirect to the new fleet's page
          navigate(`/fleets/${fleetId}`);
        } else {
          setErrorMessage(result.error?.message || "Failed to add fleet");
        }
      } catch (error) {
        setErrorMessage(
          "An error occurred while adding the fleet. Please try again.",
        );
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed z-20 inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <dialog
        open={isOpen}
        className="bg-white rounded-lg w-full max-w-2xl relative z-50"
        aria-labelledby="modal-headline"
      >
        <div className="p-6 flex flex-col h-full">
          <h2 className="text-2xl font-bold mb-6" id="modal-headline">
            Add Fleet
          </h2>
          <form onSubmit={handleAddFleet} className="flex flex-col flex-grow">
            <div className="space-y-6 flex-grow">
              <div>
                <label
                  htmlFor="fleetName"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Fleet Name
                </label>
                <input
                  type="text"
                  id="fleetName"
                  name="fleetName"
                  value={fleetName}
                  onChange={handleFleetNameChange}
                  className={`w-full border rounded-md px-3 py-2 ${
                    errors.fleetName ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.fleetName && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.fleetName}
                  </p>
                )}
              </div>

              <div>
                <h3 className="block text-sm font-medium text-gray-700 mb-2">
                  Attributes
                </h3>
                {attributes.map((attr, index) => (
                  <div
                    key={`attr-${index}-${attr.name}`}
                    className="flex gap-2 mb-2"
                  >
                    <div className="flex-1">
                      <label htmlFor={`attr-name-${index}`} className="sr-only">
                        Attribute Name
                      </label>
                      <input
                        type="text"
                        id={`attr-name-${index}`}
                        value={attr.name}
                        onChange={(e) =>
                          handleAttributeChange(index, "name", e.target.value)
                        }
                        placeholder="Name"
                        className={`w-full border rounded-md px-3 py-2 ${
                          errors[`name-${index}`]
                            ? "border-red-500"
                            : "border-gray-300"
                        }`}
                      />
                    </div>
                    <div className="flex-1">
                      <label
                        htmlFor={`attr-value-${index}`}
                        className="sr-only"
                      >
                        Attribute Value
                      </label>
                      <input
                        type="text"
                        id={`attr-value-${index}`}
                        value={attr.value}
                        onChange={(e) =>
                          handleAttributeChange(index, "value", e.target.value)
                        }
                        placeholder="Value"
                        className={`w-full border rounded-md px-3 py-2 ${
                          errors[`value-${index}`]
                            ? "border-red-500"
                            : "border-gray-300"
                        }`}
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => removeAttribute(index)}
                      className="px-2 py-2 bg-red-100 text-red-600 rounded-md hover:bg-red-200 w-10 h-10 flex items-center justify-center"
                      aria-label={`Remove attribute ${index + 1}`}
                    >
                      <FaTrash />
                    </button>
                  </div>
                ))}
                {errors[`name-${attributes.length - 1}`] && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors[`name-${attributes.length - 1}`]}
                  </p>
                )}
                {errors[`value-${attributes.length - 1}`] && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors[`value-${attributes.length - 1}`]}
                  </p>
                )}
                <button
                  type="button"
                  onClick={addAttribute}
                  className="mt-2 px-4 py-2 bg-gray-200 text-gray-600 rounded-md hover:bg-gray-300 flex items-center"
                >
                  <FaPlus className="mr-2" /> Add Attribute
                </button>
              </div>
            </div>

            {errorMessage && (
              <p className="text-red-500 text-sm mt-4 mb-4">{errorMessage}</p>
            )}

            <div className="mt-6 flex justify-end space-x-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isBootstrapping}
                className="px-4 py-2 rounded-md border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {isBootstrapping ? "Adding Fleet..." : "Add Fleet"}
              </button>
            </div>
          </form>
        </div>
      </dialog>
    </div>
  );
};

export default AddFleetModal;
