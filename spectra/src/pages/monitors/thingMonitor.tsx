import type { MonitorDataNonOptional } from "api/enterprise";
import MonitorGraph from "components/monitors/MonitorGraph";
import { ReactComponent as DeleteIcon } from "images/icons/delete.svg";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { type Dayjs, dayjs } from "utils/dayjs";
import type { KeyType } from "../../api/data";
import type {
  AlertConfig,
  AlertState,
  MetricsConfig,
  VariablesConfig,
} from "../../api/enterprise/monitors";
import { useMonitorsApi } from "../../api/enterprise/monitors";
import MonitorAlertDetail from "../../components/monitors/MonitorAlertDetail";
import MonitorMetricDetail from "../../components/monitors/MonitorMetricDetail";
import MonitorSection from "../../components/monitors/MonitorSection";
import MonitorVariableDetail from "../../components/monitors/MonitorVariableDetail";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "../../components/pageLayout";
import ButtonComponent from "../../components/uikit/button";
import Modal from "../../components/uikit/modal";
import SelectField from "../../components/uikit/selectField";
import SyntaxTextField from "../../components/uikit/syntaxTextField";
import TextField from "../../components/uikit/textField";
import { Toggle } from "../../components/uikit/toggle";
import { useAuth } from "../../context/AuthContext";
import {
  TimeRangeSelector,
  useSelectedTimeRange,
} from "../../context/SelectedTimeRangeContext";
import { useMonitor } from "../../hooks/useMonitor";

const ThingMonitorDetailPage = () => {
  const { thingId, monitorId } = useParams();

  const { user } = useAuth();
  const { getMonitorDataForPlace } = useMonitorsApi();
  const { start, end, isUTC } = useSelectedTimeRange();

  const {
    monitor,
    thing,
    things,
    thingPropertyKeys,
    thingMetricKeys,
    isEditing,
    isSaving,
    isDeleting,
    saveError,
    deleteError,
    fetchError,
    handleSave,
    handleDelete,
    handleUpdateMonitor,
    setIsEditing,
    setSaveError,
    setDeleteError,
    addMetricToRemove,
    addVariableToRemove,
    addAlertToRemove,
    resetToOriginalMonitor,
  } = useMonitor(thingId || "", monitorId || "");

  // State for monitor data
  const [monitorData, setMonitorData] = useState<
    MonitorDataNonOptional[] | undefined
  >(undefined);
  const [lastLoaded, setLastLoaded] = useState<Dayjs | undefined>(undefined);
  const [monitorDataError, setMonitorDataError] = useState<string | undefined>(
    undefined,
  );

  const fetchMonitorData = async () => {
    if (monitor && user?.partnerId) {
      try {
        const data = await getMonitorDataForPlace(
          monitor.placeId,
          monitor.placeType,
          monitor.resourceId,
          monitor.monitorId,
          user.partnerId,
          start,
          end,
          0,
        );
        setMonitorData(
          data.filter(
            (d) => d.monitorEvent !== null && d.monitorVariablesTypes !== null,
          ),
        );
        setLastLoaded(dayjs());
        setMonitorDataError(undefined);
      } catch (error: unknown) {
        console.error("Unable to load monitor data", error);
        setMonitorDataError(
          "Unable to fetch monitor data. Please try again later.",
        );
      }
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchMonitorData is not a dependency
  useEffect(() => {
    fetchMonitorData();
  }, [monitor, user?.partnerId, start, end]);

  // Add new state for add modals
  const [isAddMetricModalOpen, setIsAddMetricModalOpen] = useState(false);
  const [isAddVariableModalOpen, setIsAddVariableModalOpen] = useState(false);
  const [isAddAlertModalOpen, setIsAddAlertModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const [newMetric, setNewMetric] = useState<MetricsConfig>({
    metricName: "",
    missingDataBehavior: "NO_INFILL",
  });
  const [newVariable, setNewVariable] = useState<VariablesConfig>({
    name: "",
    expression: "",
    expressionLanguage: "DYNAMIC_EXPRESSO",
  });
  const [newAlert, setNewAlert] = useState<AlertConfig>({
    alertId: undefined,
    alertName: "",
    message: "",
    condition: "",
    severity: "0",
    integrationIds: [] as string[],
    state: "HEALTHY",
    metadata: {},
  });
  const [addError, setAddError] = useState<string | null>(null);

  if (!monitor || !thing) {
    return <div>Loading...</div>;
  }

  if (fetchError) {
    return (
      <ContentLayout>
        <Header>
          <HeaderTitle
            category="Monitors"
            categoryLink="/monitors"
            pageTitle="Monitor Error"
            detail="Unable to load monitor"
          >
            <div />
          </HeaderTitle>
        </Header>
        <HeaderDivider />
        <PageContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="text-red50 text-lg font-medium mb-2">
                Unable to Load Monitor
              </div>
              <div className="text-space70 text-sm mb-4">
                Currently unable to fetch this monitor's data. Please try again
                later.
              </div>
              <ButtonComponent.Pill
                buttonStyle="primary"
                onClick={() => window.location.reload()}
              >
                Retry
              </ButtonComponent.Pill>
            </div>
          </div>
        </PageContent>
      </ContentLayout>
    );
  }

  const resourceTypeKey = monitor.resourceType.split(".")[1];
  const availableProperties = (thingPropertyKeys[resourceTypeKey] || []).map(
    (key) => key.name,
  );
  const availableMetrics = (thingMetricKeys[resourceTypeKey] || []).map(
    (key) => key.name,
  );
  const thingTypes = Object.keys(thingPropertyKeys);

  // HANDLERS
  const handleNameChange = (name: string) => {
    handleUpdateMonitor({ monitorName: name });
  };

  const handleLookbackChange = (lookback: string) => {
    handleUpdateMonitor({ lookbackPeriod: lookback });
  };

  const handleFrequencyChange = (frequency: string) => {
    handleUpdateMonitor({ frequency });
  };

  const handleDeleteMetric = async (metricName: string) => {
    handleUpdateMonitor({
      metrics: monitor.metrics.filter((m) => m.metricName !== metricName),
    });
    addMetricToRemove(metricName);
  };

  const handleDeleteVariable = async (variableName: string) => {
    handleUpdateMonitor({
      variables: monitor.variables.filter((v) => v.name !== variableName),
    });
    addVariableToRemove(variableName);
  };

  const handleDeleteAlert = async (alertName: string) => {
    handleUpdateMonitor({
      alertConfigs: monitor.alertConfigs.filter(
        (a) => a.alertName !== alertName,
      ),
    });
    addAlertToRemove(alertName);
  };

  const handleEditMetric = async (updatedMetric: MetricsConfig) => {
    handleUpdateMonitor({
      metrics: monitor.metrics.map((m) =>
        m.metricName === updatedMetric.metricName ? updatedMetric : m,
      ),
    });
  };

  const handleEditVariable = async (updatedVariable: VariablesConfig) => {
    handleUpdateMonitor({
      variables: monitor.variables.map((v) =>
        v.name === updatedVariable.name ? updatedVariable : v,
      ),
    });
  };

  const handleEditAlert = async (updatedAlert: AlertConfig) => {
    handleUpdateMonitor({
      alertConfigs: monitor.alertConfigs.map((a) =>
        a.alertId === updatedAlert.alertId ? updatedAlert : a,
      ),
    });
  };

  const handleAddMetric = async () => {
    try {
      handleUpdateMonitor({
        metrics: [...monitor.metrics, newMetric],
      });
      setIsAddMetricModalOpen(false);
      setNewMetric({
        metricName: "",
        missingDataBehavior: "NO_INFILL",
      });
    } catch (error: unknown) {
      const errorObj = error as { message?: string };
      setAddError(errorObj?.message || "An error occurred");
    }
  };

  const handleAddVariable = async () => {
    try {
      handleUpdateMonitor({
        variables: [...monitor.variables, newVariable],
      });
      setIsAddVariableModalOpen(false);
      setNewVariable({
        name: "",
        expression: "",
        expressionLanguage: "DYNAMIC_EXPRESSO",
      });
    } catch (error: unknown) {
      const errorObj = error as { message?: string };
      setAddError(errorObj?.message || "An error occurred");
    }
  };

  const handleAddAlert = async () => {
    try {
      handleUpdateMonitor({
        alertConfigs: [...monitor.alertConfigs, newAlert],
      });
      setIsAddAlertModalOpen(false);
      setNewAlert({
        alertId: crypto.randomUUID(),
        alertName: "",
        message: "",
        condition: "",
        severity: "0",
        integrationIds: [] as string[],
        state: "HEALTHY",
        metadata: {},
      });
    } catch (error: unknown) {
      const errorObj = error as { message?: string };
      setAddError(errorObj?.message || "An error occurred");
    }
  };

  const handleEnabledChange = (enabled: boolean) => {
    handleUpdateMonitor({ isEnabled: enabled });
  };

  return (
    <ContentLayout>
      <Header>
        <HeaderTitle
          category="Monitors"
          categoryLink="/monitors"
          pageTitle={monitor?.monitorName}
          detail={monitor?.monitorId}
        >
          <div className="flex flex-row gap-2">
            {!monitor.isEnabled && (
              <div className="font-mono px-4 py-2 rounded text-red50 bg-red90 text-sm uppercase">
                Disabled
              </div>
            )}
            {isEditing && (
              <div className="font-mono px-4 py-2 rounded text-yellow40 bg-yellow90 text-sm uppercase">
                Unsaved Changes
              </div>
            )}
          </div>
          <div />
        </HeaderTitle>
        <div className="flex items-center gap-2">
          {isEditing && (
            <div className="flex gap-2">
              <ButtonComponent.Pill
                variant="filled"
                buttonStyle="primary"
                onClick={handleSave}
                disabled={isSaving}
              >
                Save Changes
              </ButtonComponent.Pill>
              <ButtonComponent.Pill
                variant="outline"
                buttonStyle="default"
                onClick={resetToOriginalMonitor}
              >
                Discard Changes
              </ButtonComponent.Pill>
            </div>
          )}
          <TimeRangeSelector />
        </div>
      </Header>
      <HeaderDivider />
      <PageContent>
        {/* Monitor Details */}
        <MonitorSection title="Monitor Details">
          <div className="flex flex-row w-full gap-4">
            <div className="flex-1 inline-flex flex-col justify-start items-start gap-2">
              <TextField
                label="Monitor Name"
                value={monitor.monitorName}
                onChange={handleNameChange}
              />
              <SelectField
                label="Attached Resource Type"
                value={resourceTypeKey}
                options={thingTypes.map((type) => ({
                  value: type,
                  label: type,
                }))}
                disabled={true}
              />
              <div className="flex flex-col w-full gap-1">
                <SelectField
                  label="Attached Resource"
                  value={monitor.resourceId}
                  options={things
                    .filter((t) => t.thingType === resourceTypeKey)
                    .map((t) => ({
                      value: t.thingId,
                      label: t.thingName,
                    }))}
                  disabled={true}
                />
                <div className="text-space70 text-[10px] font-mono">
                  Resource ID: {monitor.resourceId}
                </div>
              </div>
              <SelectField
                label="Lookback"
                value={monitor.lookbackPeriod}
                options={[
                  { value: "1m", label: "1 minute" },
                  { value: "2m", label: "2 minutes" },
                  { value: "5m", label: "5 minutes" },
                  { value: "10m", label: "10 minutes" },
                  { value: "15m", label: "15 minutes" },
                  { value: "30m", label: "30 minutes" },
                  { value: "1h", label: "1 hour" },
                  { value: "2h", label: "2 hours" },
                  { value: "4h", label: "4 hours" },
                  { value: "8h", label: "8 hours" },
                  { value: "12h", label: "12 hours" },
                  { value: "1d", label: "1 day" },
                ]}
                onChange={handleLookbackChange}
              />
              <SelectField
                label="Frequency"
                value={monitor.frequency}
                options={[
                  { value: "1m", label: "1 minute" },
                  { value: "2m", label: "2 minutes" },
                  { value: "5m", label: "5 minutes" },
                  { value: "10m", label: "10 minutes" },
                  { value: "15m", label: "15 minutes" },
                  { value: "30m", label: "30 minutes" },
                  { value: "1h", label: "1 hour" },
                  { value: "2h", label: "2 hours" },
                  { value: "4h", label: "4 hours" },
                  { value: "8h", label: "8 hours" },
                  { value: "12h", label: "12 hours" },
                  { value: "1d", label: "1 day" },
                ]}
                onChange={handleFrequencyChange}
              />
              <Toggle
                label="Enable Monitor"
                checked={monitor.isEnabled}
                onChange={handleEnabledChange}
                description="Disabling a monitor will prevent new executions. Executions may continue to run while the change is being applied."
                disabled={isSaving}
              />
            </div>
            <MonitorGraph
              lastUpdated={lastLoaded}
              monitor={monitor}
              monitorData={monitorData}
              onRefresh={fetchMonitorData}
              disabled={isEditing}
            />
          </div>
        </MonitorSection>

        {/* Alerts */}
        <MonitorSection
          title="Alerts"
          description="To setup an alert, first choose a source metric then use the variable to setup an alert. You can optionally define transformations on metrics and properties to more easily capture important thresholds and re-use them across different alerts."
        >
          <div className="py-3 flex flex-col w-full justify-center items-leading gap-6">
            <div className="flex flex-col gap-2">
              <div className="justify-start text-space60 text-xs font-semibold uppercase">
                Available Values
              </div>
              <KeyList
                keys={[
                  {
                    name: "time",
                    type: "Int32",
                    unit: "seconds",
                    description: "Time in seconds since epoch",
                    constraints: [],
                  },
                ]}
              />
            </div>

            <div className="flex flex-col gap-2">
              <div className="justify-start text-space60 text-xs font-semibold uppercase">
                Available Properties
              </div>
              <KeyList keys={availableProperties} />
            </div>

            <div className="flex flex-col gap-2">
              <div className="justify-start text-space60 text-xs font-semibold uppercase">
                Available Metrics
              </div>
              <KeyList keys={availableMetrics} />
            </div>
          </div>
        </MonitorSection>

        <div className="flex flex-col lg:flex-row w-full justify-start items-start gap-4">
          <div className="lg:basis-1/3 w-full inline-flex flex-col justify-start items-start gap-3">
            <div className="self-stretch flex flex-col justify-start items-start gap-0.5">
              <div className="self-stretch justify-start text-space60 text-xs font-semibold uppercase">
                Step 1: Choose Metrics to Alert On
              </div>
              <div className="self-stretch justify-start text-space70 text-[10px] font-medium tracking-wide">
                Metrics are data from your connected resources
              </div>
            </div>
            {monitor.metrics.map((metric) => (
              <MonitorMetricDetail
                key={metric.metricName}
                metric={metric}
                onDelete={() => handleDeleteMetric(metric.metricName)}
                onEdit={handleEditMetric}
                resourceTypeKey={resourceTypeKey}
              />
            ))}
            <button
              type="button"
              className="px-3.5 py-2 bg-blue50 rounded-[38px] inline-flex justify-end items-center gap-1 cursor-pointer"
              onClick={() => setIsAddMetricModalOpen(true)}
            >
              <div className="justify-start text-white text-xs font-medium leading-none">
                Add Metric
              </div>
            </button>
          </div>
          <div className="lg:basis-2/3 w-full inline-flex flex-col justify-start items-start gap-3">
            <div className="self-stretch flex flex-col justify-start items-start gap-0.5">
              <div className="self-stretch justify-start text-space60 text-xs font-semibold uppercase">
                Step 2: Define Variables
              </div>
              <div className="self-stretch justify-start text-space70 text-[10px] font-medium tracking-wide">
                Define optional variables with metrics and property
                transformations to simplify alert conditions. Variable data will
                populate on the next monitor run after creation.
              </div>
            </div>
            {monitor.variables.map((variable) => (
              <MonitorVariableDetail
                key={variable.name}
                thingId={monitor.resourceId}
                monitorId={monitor.monitorId}
                variable={variable}
                onDelete={() => handleDeleteVariable(variable.name)}
                onEdit={handleEditVariable}
                monitorData={monitorData}
                disableGraph={isEditing}
                availableProperties={availableProperties}
                availableMetrics={availableMetrics}
              />
            ))}
            <button
              type="button"
              className="px-3.5 py-2 bg-blue50 rounded-[38px] inline-flex justify-end items-center gap-1 cursor-pointer"
              onClick={() => setIsAddVariableModalOpen(true)}
            >
              <div className="justify-start text-white text-xs font-medium leading-none">
                Add Variable
              </div>
            </button>
          </div>
        </div>
        <div className="inline-flex justify-start items-start gap-4">
          <div className="flex-1 inline-flex flex-col justify-start items-start gap-3">
            <div className="self-stretch flex flex-col justify-start items-start gap-0.5">
              <div className="self-stretch justify-start text-space60 text-xs font-semibold uppercase">
                Step 3: Setup Alert Conditions
              </div>
              <div className="self-stretch justify-start text-space70 text-[10px] font-medium tracking-wide">
                Add conditions to trigger alerts and specify their severities
              </div>
            </div>
            {monitor.alertConfigs.map((alert) => (
              <MonitorAlertDetail
                key={alert.alertId}
                alert={alert}
                onDelete={() => handleDeleteAlert(alert.alertName)}
                onEdit={handleEditAlert}
                disableGraph={isEditing}
                monitorData={monitorData}
                availableProperties={availableProperties}
                availableMetrics={availableMetrics}
              />
            ))}
            <button
              type="button"
              className="px-3.5 py-2 bg-blue50 rounded-[38px] inline-flex justify-end items-center gap-1 cursor-pointer"
              onClick={() => setIsAddAlertModalOpen(true)}
            >
              <div className="justify-start text-white text-xs font-medium leading-none">
                Add Alert Condition
              </div>
            </button>
          </div>
        </div>

        {/* Monitor Info */}
        <div className="opacity-50 flex flex-col justify-start items-start">
          <div className="justify-start text-black text-[10px] font-normal font-mono">
            Monitor ID: {monitor.monitorId}
          </div>
          <div className="justify-start text-black text-[10px] font-normal font-mono">
            Monitor Language: {monitor.monitorLanguage}
          </div>
          <div className="justify-start text-black text-[10px] font-normal font-mono">
            Version: {monitor.version}
          </div>
          <div className="justify-start text-black text-[10px] font-normal font-mono">
            Metadata: {JSON.stringify(monitor.metadata)}
          </div>
          <div className="justify-start text-black text-[10px] font-normal font-mono">
            Created At: {new Date(monitor.createdAt).toLocaleString()}
          </div>
          <div className="justify-start text-black text-[10px] font-normal font-mono">
            Created By: {monitor.createdBy}
          </div>
        </div>

        {/* Delete Button */}
        <div className="flex">
          <button
            type="button"
            className="px-3.5 py-2 bg-red50 rounded-[38px] flex shrink-0 justify-end items-center gap-1 cursor-pointer"
            onClick={() => setIsDeleteModalOpen(true)}
          >
            <DeleteIcon className="h-3 w-3 text-white" />
            <div className="justify-start text-white text-xs font-medium leading-none">
              Delete Monitor
            </div>
          </button>
        </div>

        {/* Delete Modal */}
        <Modal
          open={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          title="Delete Monitor"
          error={
            deleteError ? { message: JSON.stringify(deleteError) } : undefined
          }
          actions={{
            cancel: {
              label: "Cancel",
              onClick: () => setIsDeleteModalOpen(false),
            },
            confirm: {
              label: isDeleting ? "Deleting..." : "Delete Monitor",
              onClick: handleDelete,
              disabled: isDeleting,
              variant: "danger",
            },
          }}
        >
          <div className="text-center text-sm text-gray-500 mt-4">
            Are you sure you want to delete{" "}
            <span className="font-semibold">{monitor?.monitorName}</span>? This
            action cannot be undone.
          </div>
        </Modal>

        {/* Save Error Modal */}
        <Modal
          open={saveError !== null}
          onClose={() => setSaveError(null)}
          title="Error Saving Monitor"
          error={saveError ? { message: JSON.stringify(saveError) } : undefined}
          actions={{
            cancel: {
              label: "Close",
              onClick: () => setSaveError(null),
            },
          }}
        >
          <div />
        </Modal>

        {/* Add Metric Modal */}
        <Modal
          open={isAddMetricModalOpen}
          onClose={() => {
            setIsAddMetricModalOpen(false);
            setAddError(null);
          }}
          title="Add Metric"
          error={addError ? { message: addError } : undefined}
          actions={{
            cancel: {
              label: "Cancel",
              onClick: () => {
                setIsAddMetricModalOpen(false);
                setAddError(null);
              },
            },
            confirm: {
              label: "Add Metric",
              onClick: handleAddMetric,
              disabled: !newMetric.metricName,
            },
          }}
        >
          <div className="mt-4 space-y-4">
            <SelectField
              label="Metric Name"
              value={newMetric.metricName}
              options={(thingMetricKeys[resourceTypeKey] || []).map((key) => ({
                value: key.name,
                label: key.name,
              }))}
              onChange={(value) =>
                setNewMetric({ ...newMetric, metricName: value })
              }
            />
            <SelectField
              label="Missing Data Behavior"
              value={newMetric.missingDataBehavior}
              options={[
                { value: "NO_INFILL", label: "No Infill" },
                { value: "INFILL_ZERO", label: "Infill Zero" },
                { value: "INFILL_LAST_VALUE", label: "Infill Last Value" },
              ]}
              onChange={(value) =>
                setNewMetric({
                  ...newMetric,
                  missingDataBehavior: value as
                    | "NO_INFILL"
                    | "INFILL_ZERO"
                    | "INFILL_LAST_VALUE",
                })
              }
            />
          </div>
        </Modal>

        {/* Add Variable Modal */}
        <Modal
          open={isAddVariableModalOpen}
          onClose={() => {
            setIsAddVariableModalOpen(false);
            setAddError(null);
          }}
          title="Add Variable"
          error={addError ? { message: addError } : undefined}
          actions={{
            cancel: {
              label: "Cancel",
              onClick: () => {
                setIsAddVariableModalOpen(false);
                setAddError(null);
              },
            },
            confirm: {
              label: "Add Variable",
              onClick: handleAddVariable,
              disabled: !newVariable.name || !newVariable.expression,
            },
          }}
        >
          <div className="mt-4 space-y-4">
            <TextField
              label="Variable Name"
              value={newVariable.name}
              onChange={(value) =>
                setNewVariable({ ...newVariable, name: value })
              }
            />
            <SyntaxTextField
              label="Variable Expression"
              value={newVariable.expression}
              onChange={(value) =>
                setNewVariable({
                  ...newVariable,
                  expression: value,
                })
              }
              availableProperties={availableProperties}
              availableMetrics={availableMetrics}
            />
          </div>
        </Modal>

        {/* Add Alert Modal */}
        <Modal
          open={isAddAlertModalOpen}
          onClose={() => {
            setIsAddAlertModalOpen(false);
            setAddError(null);
          }}
          title="Add Alert Condition"
          error={addError ? { message: addError } : undefined}
          actions={{
            cancel: {
              label: "Cancel",
              onClick: () => {
                setIsAddAlertModalOpen(false);
                setAddError(null);
              },
            },
            confirm: {
              label: "Add Alert",
              onClick: handleAddAlert,
              disabled:
                !newAlert.alertName || !newAlert.message || !newAlert.condition,
            },
          }}
        >
          <div className="mt-4 space-y-4">
            <TextField
              label="Alert Name"
              value={newAlert.alertName}
              onChange={(value) =>
                setNewAlert({ ...newAlert, alertName: value })
              }
            />
            <TextField
              label="Alert Message"
              value={newAlert.message}
              onChange={(value) => setNewAlert({ ...newAlert, message: value })}
            />
            <SyntaxTextField
              label="Condition"
              value={newAlert.condition}
              onChange={(value) =>
                setNewAlert({ ...newAlert, condition: value })
              }
              availableProperties={availableProperties}
              availableMetrics={availableMetrics}
            />
            <SelectField
              label="Severity"
              value={newAlert.severity}
              options={[
                { value: "0", label: "0 - High" },
                { value: "1", label: "1" },
                { value: "2", label: "2" },
                { value: "3", label: "3" },
                { value: "4", label: "4" },
                { value: "5", label: "5 - Medium" },
                { value: "6", label: "6" },
                { value: "7", label: "7" },
                { value: "8", label: "8" },
                { value: "9", label: "9 - Low" },
              ]}
              onChange={(value) =>
                setNewAlert({
                  ...newAlert,
                  severity: value,
                })
              }
            />
            <SelectField
              label="State"
              value={newAlert.state}
              options={[
                { value: "HEALTHY", label: "Healthy" },
                { value: "WARNING", label: "Warning" },
                { value: "UNHEALTHY", label: "Unhealthy" },
              ]}
              onChange={(value) =>
                setNewAlert({ ...newAlert, state: value as AlertState })
              }
            />
          </div>
        </Modal>
      </PageContent>
    </ContentLayout>
  );
};

const KeyList = ({ keys }: { keys: (KeyType | string)[] }) => {
  return (
    <div className="gap-2 grid grid-cols-2">
      {keys.map((key) => {
        if (typeof key === "string") {
          return (
            <div
              key={key}
              className="flex flex-col justify-start items-leading px-2 py-1 rounded"
            >
              <div className="justify-start text-space20 text-sm font-normal font-mono">
                {key}
              </div>
            </div>
          );
        }
        return (
          <div
            key={key.name}
            className="flex flex-col justify-start items-leading px-2 py-1 rounded"
          >
            <div className="justify-start text-space20 text-sm font-normal font-mono">
              {key.name}{" "}
              <span className="text-space70">
                (type: {key.type}, unit: {key.unit})
              </span>
            </div>
            <div className="justify-start text-space50 text-xs">
              {key.description || "-"}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export { ThingMonitorDetailPage };
