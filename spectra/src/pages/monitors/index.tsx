import { useAuth } from "context/AuthContext";
import { useEffect, useState } from "react";
import { type Monitor, useMonitorsApi } from "../../api/enterprise/monitors";
import type { ErrorType } from "../../api/shared";
import CreateMonitorModal from "../../components/monitors/CreateMonitorModal";
import { MonitorCard } from "../../components/monitors/MonitorCard";
import { MonitorFilters } from "../../components/monitors/MonitorFilters";
import ButtonComponent from "../../components/uikit/button";
import { useAppData } from "../../hooks/useAppData";

const MonitorsPage = () => {
  const [monitors, setMonitors] = useState<Monitor[]>([]);
  const [filteredMonitors, setFilteredMonitors] = useState<Monitor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<ErrorType | null>(null);

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const { user } = useAuth();
  const { getMonitors } = useMonitorsApi();
  const { fetchDevicesIfNeeded } = useAppData();

  const fetchMonitors = async () => {
    try {
      // Ensure devices are loaded first so Resource Names can be displayed
      await fetchDevicesIfNeeded();
      const tempMonitors: Monitor[] = [];

      let hasMore = true;
      let continuationToken: string | undefined = undefined;

      while (hasMore) {
        const monitors = await getMonitors(user.partnerId, continuationToken);

        if (Array.isArray(monitors.items)) {
          tempMonitors.push(...monitors.items);
        } else {
          console.error("Unexpected API response: items is not an array");
        }

        hasMore = monitors.hasMore;
        continuationToken = monitors.continuationToken ?? undefined;
      }

      setMonitors(tempMonitors);
      setFilteredMonitors(tempMonitors);
      setIsLoading(false);
      setError(null);
    } catch (err) {
      console.error("Error fetching monitors:", err);
      setMonitors([]);
      setFilteredMonitors([]);
      setIsLoading(false);
      setError(null);
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: getMonitors, fetchMonitors is not a dependency
  useEffect(() => {
    if (user.partnerId) {
      fetchMonitors();
    }
  }, [user.partnerId]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="px-4">
      <div className="flex justify-between items-center pt-4 pb-5">
        <div className="text-heading1 text-space50">Monitors</div>
        <ButtonComponent.Pill
          buttonStyle="primary"
          onClick={() => {
            setIsCreateModalOpen(true);
          }}
        >
          Create Monitor
        </ButtonComponent.Pill>
      </div>

      {monitors.length > 0 ? (
        <>
          <MonitorStats monitors={monitors} />

          <MonitorFilters
            monitors={monitors}
            onFilterChange={setFilteredMonitors}
          />

          <div className="flex flex-col gap-2">
            {filteredMonitors.map((monitor) => (
              <MonitorCard key={monitor.monitorId} monitor={monitor} />
            ))}
          </div>
        </>
      ) : (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="text-space70 text-lg font-medium mb-2">
              No Monitors Found
            </div>
            <div className="text-space70 text-sm mb-4">
              No monitors are currently available. Individual monitors may show
              error states if you don't have access to them.
            </div>
          </div>
        </div>
      )}

      <CreateMonitorModal
        open={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          fetchMonitors();
        }}
      />
    </div>
  );
};

const MonitorStats = ({ monitors }: { monitors: Monitor[] }) => {
  const totalMonitors = monitors;
  const unhealthyMonitors = monitors.filter((m) => m.state === "UNHEALTHY");
  const warningMonitors = monitors.filter((m) => m.state === "WARNING");
  const disabledMonitors = monitors.filter((m) => !m.isEnabled);

  return (
    <div className="py-4 mb-4">
      <div className="grid grid-cols-4 gap-4">
        <div className="p-2 rounded-md text-space50">
          <div className="text-xs font-normal">Total Monitors</div>
          <div className="text-2xl">{totalMonitors.length}</div>
        </div>
        <div
          className={`p-2 rounded-md ${
            unhealthyMonitors.length > 0 ? "bg-red90 text-red50" : ""
          }`}
        >
          <div className="text-xs font-normal">Unhealthy</div>
          <div className="text-2xl">{unhealthyMonitors.length}</div>
        </div>
        <div
          className={`p-2 rounded-md ${
            warningMonitors.length > 0 ? "bg-yellow90 text-yellow40" : ""
          }`}
        >
          <div className="text-xs font-normal">Warning</div>
          <div className="text-2xl">{warningMonitors.length}</div>
        </div>
        <div className="p-2 rounded-md text-space50">
          <div className="text-xs font-normal">Disabled</div>
          <div className="text-2xl">{disabledMonitors.length}</div>
        </div>
      </div>
    </div>
  );
};

export { MonitorsPage };
