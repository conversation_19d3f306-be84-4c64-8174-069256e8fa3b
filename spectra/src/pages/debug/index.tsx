import ButtonComponent from "components/uikit/button";
import { useAuth } from "context/AuthContext";

const DebugPage = () => {
  const { user, permissions, token, isLoading } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="px-4">
      <div className="flex justify-between items-center pt-4 pb-5">
        <div className="text-heading1 text-space50">Debug</div>
      </div>

      <div className="flex flex-col gap-2">
        <div className="text-xs text-space50">User</div>
        <div className="text-sm text-space50 font-mono">{user?.name}</div>
        <div className="text-xs text-space50 mt-2">Permissions</div>
        <div className="text-sm text-space50 font-mono">
          {permissions.join(", ")}
        </div>
        <div className="text-xs text-space50 mt-2">Token</div>
        <div className="text-sm text-space50 font-mono p-2 bg-space90 break-all rounded-md">
          {token}
        </div>
        <div>
          <ButtonComponent.Pill
            buttonStyle="primary"
            variant="filled"
            onClick={() => {
              navigator.clipboard.writeText(token);
            }}
          >
            Copy Token to Clipboard
          </ButtonComponent.Pill>
        </div>
      </div>
    </div>
  );
};

export { DebugPage };
