import { useState } from "react";
import { AuditLogs } from "../../components/organization/AuditLogs";
import { Table, type TableColumn } from "../../components/organization/table";
import ButtonComponent from "../../components/uikit/button";

// Spacing constants for tree indentation
const SPACING_CONSTANTS = {
  BASE_INDENT: 16,
  LEVEL_INDENT: 24,
  CONNECTION_OFFSET: 12,
} as const;

interface Organization {
  id: string;
  name: string;
  userCount: number;
  isParent?: boolean;
  children?: Organization[];
}

// Mock data - when we have a real API, this would come from an API
const mockOrganizations: Organization[] = [
  {
    id: "acme",
    name: "AC<PERSON>",
    userCount: 75,
    isParent: true,
    children: [
      { id: "acme-maintenance", name: "ACME Maintenance", userCount: 21 },
      { id: "acme-inc", name: "ACME Inc.", userCount: 17 },
      {
        id: "acme-rnd",
        name: "ACME R&D",
        userCount: 20,
        children: [
          { id: "acme-rnd-test", name: "ACME R&D Test", userCount: 5 },
          { id: "acme-rnd-hardware", name: "ACME R&D Hardware", userCount: 6 },
          { id: "acme-rnd-software", name: "ACME R&D Software", userCount: 6 },
        ],
      },
      { id: "acme-holdings", name: "ACME Holdings", userCount: 8 },
      { id: "acme-solutions", name: "ACME Solutions", userCount: 6 },
    ],
  },
];

// Sample data for the delegation table
interface Delegation {
  id: string;
  delegatedTo: string;
  resources: string;
  read: boolean;
  write: boolean;
  control: boolean;
}

const mockDelegations: Delegation[] = [
  {
    id: "1",
    delegatedTo: "ACME R&D Hardware",
    resources: "Things: *, Places: *",
    read: true,
    write: true,
    control: true,
  },
  {
    id: "2",
    delegatedTo: "ACME R&D Software",
    resources: "Things: *, Places: *",
    read: true,
    write: false,
    control: false,
  },
  {
    id: "3",
    delegatedTo: "ACME R&D Software",
    resources: "Things: batteries, Places: *",
    read: true,
    write: true,
    control: false,
  },
  {
    id: "4",
    delegatedTo: "ACME R&D Test",
    resources: "Things: test/*, Place: testcenter/*",
    read: true,
    write: true,
    control: true,
  },
];

interface OrganizationTreeItemProps {
  organization: Organization;
  selectedOrg: Organization | null;
  onSelectOrg: (org: Organization) => void;
  level?: number;
  isLast?: boolean;
}

const OrganizationTreeItem = ({
  organization,
  selectedOrg,
  onSelectOrg,
  level = 0,
  isLast = false,
}: OrganizationTreeItemProps) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const isSelected = selectedOrg?.id === organization.id;

  // Calculate indentation based on hierarchy
  const levelIndent = level * SPACING_CONSTANTS.LEVEL_INDENT;
  const totalIndent = SPACING_CONSTANTS.BASE_INDENT + levelIndent;

  // Button width calculation - parent full width, children narrower
  const buttonWidth = level === 0 ? "100%" : `calc(100% - ${totalIndent}px)`;

  return (
    <div className="relative">
      {level > 0 && (
        <>
          <div
            className="absolute"
            style={{
              left: `${
                SPACING_CONSTANTS.BASE_INDENT +
                (level - 1) * SPACING_CONSTANTS.LEVEL_INDENT +
                SPACING_CONSTANTS.CONNECTION_OFFSET
              }px`,
              top: "-10px",
              height: isLast ? "42px" : "100%",
              width: "1px",
              borderLeft: "2px solid #E8EAEC",
            }}
          />

          <div
            className="absolute"
            style={{
              left: `${
                SPACING_CONSTANTS.BASE_INDENT +
                (level - 1) * SPACING_CONSTANTS.LEVEL_INDENT +
                SPACING_CONSTANTS.CONNECTION_OFFSET
              }px`,
              top: "32px",
              height: "1px",
              width: `${SPACING_CONSTANTS.CONNECTION_OFFSET}px`,
              borderTop: "2px solid #E8EAEC",
            }}
          />
        </>
      )}

      <div className="pb-3 flex justify-end relative">
        <ButtonComponent
          onClick={() => onSelectOrg(organization)}
          className={`
            bg-white border border-space90 rounded-lg p-3
            hover:bg-blue97 transition-colors shadow-sm
            ${isSelected ? "ring-2 ring-blue40 border-blue50" : ""}
          `}
          style={{ width: buttonWidth }}
          type="button"
        >
          <div className="flex items-center justify-between">
            <div className="text-left flex items-center gap-2">
              <div className="font-medium text-space50">
                {organization.name}
                <div className="text-sm text-space60">
                  {organization.userCount} Users
                </div>
              </div>
              {organization.isParent && (
                <span className="ml-2 text-white text-[8px] font-bold uppercase px-2 py-1 rounded bg-gray-400 self-center">
                  YOUR ORGANIZATION
                </span>
              )}
            </div>
            <div className="text-blue50 text-lg">→</div>
          </div>
        </ButtonComponent>
      </div>

      {isExpanded && organization.children && (
        <div className="relative">
          {organization.children.map((child, index) => (
            <OrganizationTreeItem
              key={child.id}
              organization={child}
              selectedOrg={selectedOrg}
              onSelectOrg={onSelectOrg}
              level={level + 1}
              isLast={index === organization.children.length - 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const OrganizationTabs = ({
  selectedOrg,
  onSelectOrg,
}: {
  selectedOrg: Organization | null;
  onSelectOrg: (org: Organization) => void;
}) => {
  const [activeTab, setActiveTab] = useState("audit-logs");

  const tabs = [
    { id: "link-1", label: "LINK 1" },
    { id: "link-2", label: "LINK 2" },
    { id: "link-selected", label: "LINK SELECTED" },
    { id: "audit-logs", label: "AUDIT LOGS" },
  ];

  const getFilteredDelegations = () => {
    if (!selectedOrg) return mockDelegations;

    // Filter by delegated organization matching the selected organization
    return mockDelegations.filter(
      (delegation) =>
        delegation.delegatedTo
          .toLowerCase()
          .includes(selectedOrg.name.toLowerCase()) ||
        selectedOrg.name === "ACME", // Show all for parent organization
    );
  };

  const delegationColumns: TableColumn<Delegation>[] = [
    {
      id: "delegatedTo",
      header: "Delegated To",
      accessorKey: "delegatedTo",
      enableSorting: true,
    },
    {
      id: "resources",
      header: "Resources",
      accessorKey: "resources",
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="text-sm text-space50 font-mono">
          {getValue() as string}
        </span>
      ),
    },
    {
      id: "read",
      header: "Read",
      accessorKey: "read",
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="text-sm text-space50">
          {getValue() ? "Yes" : "No"}
        </span>
      ),
    },
    {
      id: "write",
      header: "Write",
      accessorKey: "write",
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="text-sm text-space50">
          {getValue() ? "Yes" : "No"}
        </span>
      ),
    },
    {
      id: "control",
      header: "Control",
      accessorKey: "control",
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="text-sm text-space50">
          {getValue() ? "Yes" : "No"}
        </span>
      ),
    },
    {
      id: "actions",
      header: "",
      enableSorting: false,
      cell: ({ row }) => (
        <div className="flex gap-2">
          <button
            type="button"
            className="text-blue50 hover:text-blue40 text-sm"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
              role="img"
            >
              <title>Edit</title>
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
          </button>
          <button
            type="button"
            className="text-red50 hover:text-red40 text-sm"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
              role="img"
            >
              <title>Delete</title>
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <div className="border-b border-space90">
        <div className="py-4">
          <div className="text-heading1 text-space50">
            {selectedOrg?.name || "Select an organization"}
          </div>
        </div>
        <div className="flex">
          {tabs.map((tab) => (
            <ButtonComponent
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? "border-blue50 text-blue50"
                  : "border-transparent text-space60 hover:text-space40"
              }`}
              type="button"
            >
              {tab.label}
            </ButtonComponent>
          ))}
        </div>
      </div>

      <div className="flex-1 p-6">
        {activeTab === "link-selected" ? (
          <div>
            <Table
              data={getFilteredDelegations()}
              columns={delegationColumns}
              pagination={{
                pageSize: 5,
                showPagination: true,
                pageSizeOptions: [5, 10, 20, 50],
              }}
              sorting={{
                enableSorting: true,
                enableMultiSort: false,
                initialSort: [{ id: "delegatedTo", desc: false }],
              }}
              onRowClick={(delegation) => {}}
              onPaginationChange={(pageIndex, pageSize) => {}}
              onSortingChange={(sorting) => {}}
              emptyMessage={
                selectedOrg
                  ? `No delegations found for ${selectedOrg.name}`
                  : "Select an organization to view delegations"
              }
            />
          </div>
        ) : activeTab === "audit-logs" ? (
          <div>
            <AuditLogs />
          </div>
        ) : (
          <div className="text-center text-space60 py-8">
            <p>Content for {activeTab} tab</p>
            <p className="mt-2">This area is work in progress</p>
          </div>
        )}
      </div>
    </div>
  );
};

export const MyOrganizationPage = () => {
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(
    mockOrganizations[0],
  );

  return (
    <div className="h-screen flex">
      <div className="w-1/3 flex flex-col">
        <div className="p-4 pb-0">
          <div className="text-heading1 text-space50">My Organization</div>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {mockOrganizations.map((org, index) => (
            <OrganizationTreeItem
              key={org.id}
              organization={org}
              selectedOrg={selectedOrg}
              onSelectOrg={setSelectedOrg}
              isLast={index === mockOrganizations.length - 1}
            />
          ))}
        </div>

        <div className="p-4 border-t border-space90">
          <div className="text-xs text-space60 mb-3 text-center">
            Click on an organization above to filter results on the right
          </div>
          <ButtonComponent.Default
            variant="outline"
            buttonStyle="default"
            className="w-full"
            onClick={() => console.log("Create new organization")}
          >
            + Create New Organization
          </ButtonComponent.Default>
        </div>
      </div>

      <div className="w-2/3">
        <OrganizationTabs
          selectedOrg={selectedOrg}
          onSelectOrg={setSelectedOrg}
        />
      </div>
    </div>
  );
};
