import {
  type Organization,
  type OrganizationTreeNode,
  useIdentityPlatformApi,
} from "api/ingestion/identityPlatform";
import { useEffect, useState } from "react";

import {
  AuthorizedComponent,
  useAuthorization,
} from "components/AuthorizedComponent";
import ButtonComponent from "components/uikit/button";
import { InvitesSection } from "./invites";
import { OrganizationTreeItem } from "./orgTree";
import { UsersSection } from "./users";

type Tab = "users" | "invites";
const tabs: { id: Tab; label: string }[] = [
  { id: "users", label: "Users" },
  { id: "invites", label: "Invites" },
];

const TabBar = ({
  selectedTab,
  setSelectedTab,
}: {
  selectedTab: Tab;
  setSelectedTab: (tab: Tab) => void;
}) => {
  return (
    <div className="border-b border-space90">
      <div className="flex">
        {tabs.map((tab) => (
          <ButtonComponent
            key={tab.id}
            onClick={() => setSelectedTab(tab.id)}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              selectedTab === tab.id
                ? "border-blue50 text-blue50"
                : "border-transparent text-space60 hover:text-space40"
            }`}
            type="button"
          >
            {tab.label}
          </ButtonComponent>
        ))}
      </div>
    </div>
  );
};

const PageLayout = () => {
  const [selectedTab, setSelectedTab] = useState<Tab>("users");

  const [organization, setOrganization] = useState<Organization | null>(null);
  const [myOrg, setMyOrg] = useState<OrganizationTreeNode | null>(null);
  const [organizationTree, setOrganizationTree] =
    useState<OrganizationTreeNode | null>(null);
  const [selectedOrg, setSelectedOrg] = useState<OrganizationTreeNode | null>(
    null,
  );

  const {
    getUserPlatformOrganization,
    getUserPlatformOrganizationDescendants,
  } = useIdentityPlatformApi();

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch organizations on mount; getUserPlatformOrganizationDescendants is not a dependency
  useEffect(() => {
    // TODO: this should be one api call...
    // ... and doesnt work for child orgs
    getUserPlatformOrganization().then((org) => {
      setOrganization(org);
      getUserPlatformOrganizationDescendants().then((resp) => {
        const myOrg = {
          organizationId: org.organizationId,
          displayName: org.displayName,
          logoUrl: org.brandingConfig.logoUrl,
          children: resp.organizationTree,
        };

        setMyOrg(myOrg);
        setOrganizationTree(myOrg);
        setSelectedOrg(myOrg);
      });
    });
  }, []);

  const hasWritePermission = useAuthorization("write:ingest_admin");

  const showData = selectedOrg === myOrg;

  return (
    <div className="flex flex-col lg:flex-row w-full min-h-screen gap-6 pr-4">
      <div className="w-full lg:w-1/3 flex flex-col">
        <div className="p-4 pb-0">
          <div className="text-heading1 text-space50">My Organization</div>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {myOrg && (
            <OrganizationTreeItem
              key={organizationTree.organizationId}
              organization={myOrg}
              myOrg={organizationTree}
              selectedOrg={selectedOrg}
              onSelectOrg={setSelectedOrg}
            />
          )}
        </div>
      </div>
      <div className="flex w-full lg:w-2/3 flex-col gap-4">
        {!organization && (
          <div className="pt-4 text-space60 text-center flex items-center justify-center h-full">
            Loading...
          </div>
        )}
        {!!organization && !selectedOrg && (
          <div className="pt-4 text-space60 text-center flex items-center justify-center h-full">
            Select an organization to view details
          </div>
        )}
        {selectedOrg && (
          <>
            <div className="pt-4 text-heading1 text-space50">
              {selectedOrg.displayName}
            </div>
            <TabBar selectedTab={selectedTab} setSelectedTab={setSelectedTab} />
            {selectedTab === "users" && (
              <UsersSection
                editable={hasWritePermission}
                filterByOrg={selectedOrg}
              />
            )}
            {selectedTab === "invites" && (
              <InvitesSection showEmptyState={!showData} />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export const OrganizationPage = () => {
  return (
    <AuthorizedComponent requiredPermission="read:ingest_admin">
      <PageLayout />
    </AuthorizedComponent>
  );
};
