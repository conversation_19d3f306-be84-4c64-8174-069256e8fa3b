import { type Invite, useIdentity<PERSON>pi } from "api/ingestion/identity";

import { useEffect, useState } from "react";
import { FaClipboard, FaTrash } from "react-icons/fa";

import { useAuthorization } from "components/AuthorizedComponent";
import { AddUserModal } from "./addModal";
import { DeleteInviteModal } from "./deleteInviteModal";
import { PrimaryButton } from "./shared";

const InviteRow = ({
  invite,
  handleOpenDeleteModal,
  editable,
}: {
  invite: Invite;
  handleOpenDeleteModal: (invite: Invite) => void;
  editable: boolean;
}) => {
  return (
    <tr key={invite.id}>
      <td className="whitespace-nowrap py-5 pl-4 pr-3 text-sm sm:pl-0 font-medium text-space10">
        {invite.invitee.email}
      </td>
      <td className="whitespace-nowrap py-5 pl-4 pr-3 text-sm sm:pl-0">
        <button
          className="text-blue50 hover:text-blue20 underline"
          onClick={() => navigator.clipboard.writeText(invite.invitationUrl)}
          type="button"
        >
          <FaClipboard />
          <span className="sr-only">
            Copy invite for {invite.invitee.email}
          </span>
        </button>
      </td>
      <td className="whitespace-nowrap py-5 pl-4 pr-3 text-sm sm:pl-0">
        {new Date(invite.createdAt).toLocaleString()}
      </td>
      <td className="whitespace-nowrap py-5 pl-4 pr-3 text-sm sm:pl-0">
        {new Date(invite.expiresAt).toLocaleString()}
      </td>
      {editable && (
        <td className="relative whitespace-nowrap py-5 pl-3 pr-4 text-left text-sm font-medium sm:pr-0">
          <button
            onClick={() => handleOpenDeleteModal(invite)}
            className="text-red50 hover:text-red20"
            type="button"
          >
            <FaTrash />
            <span className="sr-only">Delete, {invite.invitee.email}</span>
          </button>
        </td>
      )}
    </tr>
  );
};
const InvitesTable = ({
  invites,
  refresh,
  editable,
}: {
  invites: Invite[] | null;
  refresh: () => Promise<void>;
  editable: boolean;
}) => {
  const [inviteToDelete, setInviteToDelete] = useState<Invite | null>(null);

  if (!invites)
    return <div className="text-center text-space50 pt-4">Loading...</div>;

  const handleCloseDeleteModal = () => {
    setInviteToDelete(null);
    refresh();
  };

  return (
    <div className="overflow-x-auto">
      <DeleteInviteModal
        invite={inviteToDelete}
        open={inviteToDelete !== null}
        setOpen={handleCloseDeleteModal}
      />
      <table className="min-w-full divide-y divide-gray-300">
        <thead>
          <tr>
            <th
              scope="col"
              className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-space10 sm:pl-0"
            >
              Email
            </th>
            <th
              scope="col"
              className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-space10 sm:pl-0"
            >
              Invitation Link
            </th>
            <th
              scope="col"
              className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-space10 sm:pl-0"
            >
              Created
            </th>
            <th
              scope="col"
              className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-space10 sm:pl-0"
            >
              Expires
            </th>
            <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-0">
              <span className="sr-only">Edit</span>
            </th>
            <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-0">
              <span className="sr-only">Delete</span>
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {invites.length === 0 && (
            <tr>
              <td colSpan={6} className="text-center text-space50 pt-4">
                No invites found
              </td>
            </tr>
          )}
          {invites.map((invite) => (
            <InviteRow
              key={invite.id}
              invite={invite}
              handleOpenDeleteModal={setInviteToDelete}
              editable={editable}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

export const InvitesSection = ({
  showEmptyState = false, // HACK: we want to show an empty state for the invites section for a demo
}: { showEmptyState?: boolean }) => {
  const { getInvites } = useIdentityApi();
  const writeAuthorized = useAuthorization("write:ingest_admin");
  const [invites, setInvites] = useState<Invite[] | null>(null);
  const [addUserModalOpen, setAddUserModalOpen] = useState(false);

  const fetchAllInvites = async () => {
    let allInvites: Invite[] = [];
    let page = 0;
    const pageSize = 50;
    let fetchedInvites: Invite[] = [];

    do {
      fetchedInvites = await getInvites(page, pageSize);
      allInvites = [...allInvites, ...fetchedInvites];
      page++;
    } while (fetchedInvites.length === pageSize);
    setInvites(allInvites);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch invites on mount
  useEffect(() => {
    if (showEmptyState) {
      setInvites([]);
      return;
    }
    fetchAllInvites();
  }, [addUserModalOpen.toString(), showEmptyState.toString()]);

  return (
    <div>
      <InvitesTable
        invites={invites}
        refresh={fetchAllInvites}
        editable={writeAuthorized}
      />
      <div className="mt-4">
        <PrimaryButton
          onClick={() => setAddUserModalOpen(true)}
          disabled={addUserModalOpen}
        >
          Invite Member
        </PrimaryButton>
      </div>
      <AddUserModal open={addUserModalOpen} setOpen={setAddUserModalOpen} />
    </div>
  );
};
