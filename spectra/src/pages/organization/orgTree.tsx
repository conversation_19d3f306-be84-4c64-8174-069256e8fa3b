import type { OrganizationTreeNode } from "api/ingestion/identityPlatform";
import ButtonComponent from "components/uikit/button";

const SPACING_CONSTANTS = {
  BASE_INDENT: 16,
  LEVEL_INDENT: 24,
  CONNECTION_OFFSET: 12,
} as const;

interface OrganizationTreeItemProps {
  organization: OrganizationTreeNode;
  myOrg: OrganizationTreeNode;
  selectedOrg: OrganizationTreeNode | null;
  onSelectOrg: (org: OrganizationTreeNode) => void;
  level?: number;
  isLast?: boolean;
}

export const OrganizationTreeItem = ({
  organization,
  myOrg,
  selectedOrg,
  onSelectOrg,
  level = 0,
  isLast = false,
}: OrganizationTreeItemProps) => {
  const isSelected =
    selectedOrg?.organizationId === organization.organizationId;

  // Calculate indentation based on hierarchy
  const levelIndent = level * SPACING_CONSTANTS.LEVEL_INDENT;
  const totalIndent = SPACING_CONSTANTS.BASE_INDENT + levelIndent;

  // Button width calculation - parent full width, children narrower
  const buttonWidth = level === 0 ? "100%" : `calc(100% - ${totalIndent}px)`;

  return (
    <div className="relative">
      {level > 0 && (
        <>
          <div
            className="absolute"
            style={{
              left: `${
                SPACING_CONSTANTS.BASE_INDENT +
                (level - 1) * SPACING_CONSTANTS.LEVEL_INDENT +
                SPACING_CONSTANTS.CONNECTION_OFFSET
              }px`,
              top: "-10px",
              height: isLast ? "42px" : "100%",
              width: "1px",
              borderLeft: "2px solid #E8EAEC",
            }}
          />

          <div
            className="absolute"
            style={{
              left: `${
                SPACING_CONSTANTS.BASE_INDENT +
                (level - 1) * SPACING_CONSTANTS.LEVEL_INDENT +
                SPACING_CONSTANTS.CONNECTION_OFFSET
              }px`,
              top: "32px",
              height: "1px",
              width: `${SPACING_CONSTANTS.CONNECTION_OFFSET}px`,
              borderTop: "2px solid #E8EAEC",
            }}
          />
        </>
      )}

      <div className="pb-3 flex justify-end relative">
        <ButtonComponent
          onClick={() => onSelectOrg(organization)}
          className={`
            bg-white border border-space90 rounded-lg p-3
            hover:bg-blue97 transition-colors shadow-sm
            ${isSelected ? "ring-2 ring-blue40 border-blue50" : ""}
          `}
          style={{ width: buttonWidth }}
          type="button"
        >
          <div className="flex items-center justify-between">
            <div className="text-left flex items-center gap-2">
              <img
                src={organization.logoUrl}
                alt={organization.displayName}
                className="w-12"
              />
              <div className="font-medium text-space50">
                {organization.displayName}
              </div>
              {myOrg.organizationId === organization.organizationId && (
                <span className="ml-2 text-white text-[8px] font-bold uppercase px-2 py-1 rounded bg-gray-400 self-center">
                  YOUR ORGANIZATION
                </span>
              )}
            </div>
            <div className="text-blue50 text-lg">&rarr;</div>
          </div>
        </ButtonComponent>
      </div>

      {organization.children && (
        <div className="relative">
          {organization.children.map((child, index) => (
            <OrganizationTreeItem
              key={child.organizationId}
              organization={child}
              myOrg={myOrg}
              selectedOrg={selectedOrg}
              onSelectOrg={onSelectOrg}
              level={level + 1}
              isLast={index === organization.children.length - 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};
