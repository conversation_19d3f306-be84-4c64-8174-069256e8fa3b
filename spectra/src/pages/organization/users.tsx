import { type UserDetails, useIdentityApi } from "api/ingestion/identity";
import { useAuth } from "context/AuthContext";

import { useEffect, useState } from "react";
import { FaPencilAlt, FaTrash } from "react-icons/fa";

import type { OrganizationTreeNode } from "api/ingestion/identityPlatform";
import { DeleteUserModal } from "./deleteModal";
import { EditUserModal } from "./editModal";

const UserRow = ({
  user,
  handleOpenEditModal,
  handleOpenDeleteModal,
  editable,
  disabled = false,
}: {
  user: UserDetails;
  handleOpenEditModal: (userId: string) => void;
  handleOpenDeleteModal: (userId: string) => void;
  editable: boolean;
  disabled?: boolean;
}) => {
  return (
    <tr key={user.userId}>
      <td className="whitespace-nowrap py-5 pl-4 pr-3 text-sm sm:pl-0">
        <div className="flex items-center">
          <div className="h-11 w-11 flex-shrink-0">
            <img alt="" src={user.picture} className="h-11 w-11 rounded-full" />
          </div>
          <div className="ml-4">
            <div className="font-medium text-space10">{user.name}</div>
            <div className="mt-1 text-space70">{user.email}</div>
          </div>
        </div>
      </td>
      {editable && (
        <td className="relative whitespace-nowrap py-5 pl-3 pr-4 text-left text-sm font-medium sm:pr-0">
          <button
            type="button"
            onClick={() => handleOpenEditModal(user.userId)}
            className="text-blue50 hover:text-blue20 disabled:text-gray80 disabled:hover:text-gray80"
            disabled={disabled}
          >
            <FaPencilAlt />
            <span className="sr-only">Edit, {user.name}</span>
          </button>
        </td>
      )}
      {editable && (
        <td className="relative whitespace-nowrap py-5 pl-3 pr-4 text-left text-sm font-medium sm:pr-0">
          <button
            type="button"
            onClick={() => handleOpenDeleteModal(user.userId)}
            className="text-red50 hover:text-red20 disabled:text-gray80 disabled:hover:text-gray80"
            disabled={disabled}
          >
            <FaTrash />
            <span className="sr-only">Delete, {user.name}</span>
          </button>
        </td>
      )}
    </tr>
  );
};

const UsersTable = ({
  users,
  refresh,
  editable,
  disabled = false,
}: {
  users: UserDetails[] | null;
  refresh: () => Promise<void>;
  editable: boolean;
  disabled?: boolean;
}) => {
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [userIdToDelete, setUserIdToDelete] = useState<string | null>(null);

  if (!users)
    return <div className="text-center text-space50 pt-4">Loading...</div>;

  const handleCloseEditModal = () => {
    setSelectedUserId(null);
    refresh();
  };
  const handleCloseDeleteModal = () => {
    setUserIdToDelete(null);
    refresh();
  };

  return (
    <div className="overflow-x-auto">
      <EditUserModal
        userId={selectedUserId}
        open={selectedUserId !== null}
        setOpen={handleCloseEditModal}
      />
      <DeleteUserModal
        userId={userIdToDelete}
        open={userIdToDelete !== null}
        setOpen={handleCloseDeleteModal}
      />
      <table className="min-w-full divide-y divide-gray-300">
        <tbody className="divide-y divide-gray-200 bg-white">
          {users.map((user) => (
            <UserRow
              key={user.userId}
              user={user}
              handleOpenEditModal={setSelectedUserId}
              handleOpenDeleteModal={setUserIdToDelete}
              editable={editable}
              disabled={disabled}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

type DemoUser = {
  showForOrgId: string;
} & UserDetails;
const DEMO_USERS: DemoUser[] = [
  {
    userId: "whatever_clubcar",
    name: "<EMAIL>",
    email: "<EMAIL>",
    picture: "https://cdn.auth0.com/avatars/ma.png",
    showForOrgId: "28c02735-f1d4-49ea-9975-60cedd1d735c",
  },
  {
    userId: "whatever_tennant",
    name: "<EMAIL>",
    email: "<EMAIL>",
    picture: "https://cdn.auth0.com/avatars/ro.png",
    showForOrgId: "d6b41fff-dadd-4b47-8b31-1402fc37c796",
  },
  {
    userId: "whatever_kawasaki",
    name: "<EMAIL>",
    email: "<EMAIL>",
    picture: "https://cdn.auth0.com/avatars/yo.png",
    showForOrgId: "3cd3bd16-a526-4d69-bb41-7780c09644ca",
  },
];

export const UsersSection = ({
  editable,
  filterByOrg, // HACK: we want to show an preloaded state for the users section for a demo
}: {
  editable: boolean;
  showEmptyState?: boolean;
  filterByOrg: OrganizationTreeNode;
}) => {
  const { getUsersForOrganization } = useIdentityApi();
  const { user } = useAuth();

  const [users, setUsers] = useState<UserDetails[] | null>(null);

  const fetchAllUsers = async () => {
    let allUsers: UserDetails[] = [];
    let page = 0;
    const pageSize = 50;
    let fetchedUsers: UserDetails[] = [];

    do {
      fetchedUsers = await getUsersForOrganization(user.org_id, page, pageSize);
      allUsers = [...allUsers, ...fetchedUsers];
      page++;
    } while (fetchedUsers.length === pageSize);
    setUsers(allUsers);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch users on mount
  useEffect(() => {
    fetchAllUsers();
  }, [user.org_id]);

  // HACK: we want to show a preloaded state for the users section for a demo
  const filteredDemoUsers = DEMO_USERS.filter(
    (user) => user.showForOrgId === filterByOrg.organizationId,
  );
  const showDemoUsers = filteredDemoUsers.length > 0;
  const usersToDisplay = showDemoUsers ? filteredDemoUsers : users;

  return (
    <UsersTable
      users={usersToDisplay}
      refresh={fetchAllUsers}
      editable={editable}
      disabled={showDemoUsers}
    />
  );
};
