import { useAuthFetch } from "context/AuthContext";

import { BASE_URL } from "./common";

export type IntegrationResource = {
  path: string;
  metadata: Record<string, unknown>;
};

export type IntegrationResponse = {
  integrationId: string;
  integrationName: string;
  partnerId: string;
  direction: string;
  integrationType: string;
  resources: IntegrationResource[];
  properties: Record<string, string[]>;
  metaData: Record<string, unknown>;
  createdAt: string;
  createdBy: string;
};

export type IntegrationUpdateRequest = {
  integrationName?: string;
  metaData?: Record<string, unknown>;
  metaDataToDelete?: string[];
  properties?: Record<string, string[]>;
  propertiesToDelete?: string[];
};

type IntegrationTypeConfig = {
  name: string;
  description: string;
};

type IntegrationTypesResponse = {
  [direction: string]: {
    [type: string]: IntegrationTypeConfig[];
  };
};

export type IntegrationMappingResponse = {
  [direction: string]: Array<{
    type: string;
    supportedResourceTypes: string[];
  }>;
};

export const useIntegrationsApi = () => {
  const { authFetch } = useAuthFetch();

  const getIntegrations = async (): Promise<IntegrationResponse[]> => {
    const url = `${BASE_URL}/integrations`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP error, status: ${res.status}`);
      }
      return res.json();
    });
  };

  const getIntegrationDetail = async (
    integrationId: string,
  ): Promise<IntegrationResponse> => {
    const url = `${BASE_URL}/integrations/${integrationId}`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP error, status: ${res.status}`);
      }
      return res.json();
    });
  };

  const updateIntegration = async (
    integrationId: string,
    data: IntegrationUpdateRequest,
  ): Promise<{
    success: boolean;
    message?: string;
  }> => {
    const url = `${BASE_URL}/integrations/${integrationId}`;
    try {
      const response = await authFetch(url, {
        method: "PATCH",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.status === 201) {
        return { success: true, message: "Integration updated successfully" };
      }

      const errorData = await response.json();
      return {
        success: false,
        message: errorData?.message ?? errorData.errors[0].message, // there might be two error types, one is the error message and the other is the error object
      };
    } catch (error) {
      console.error("Error updating integration:", error);
      return { success: false, message: error.message };
    }
  };

  const deleteIntegration = async (integrationId: string) => {
    const url = `${BASE_URL}/integrations/${integrationId}`;
    try {
      const response = await authFetch(url, {
        method: "DELETE",
      });

      if (response.status === 200) {
        return { success: true, message: "Integration deleted successfully" };
      }
      const errorData = await response.json();
      console.error("Error deleting integration:", errorData);
      return { success: false, error: errorData };
    } catch (error) {
      console.error("Error deleting integration:", error);
      return { success: false, error: error.message };
    }
  };

  const createIntegration = async (data: {
    integrationName: string;
    integrationDirection: string;
    integrationType: string;
    resources?: Array<{
      path: string;
      metadata?: Record<string, string>;
    }>;
    properties?: Record<string, string[]>;
    metaData?: Record<string, string>;
    secretProperties?: Record<string, string>;
  }) => {
    const url = `${BASE_URL}/integrations`;
    try {
      const response = await authFetch(url, {
        method: "POST",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.status === 201) {
        return { success: true, message: "Integration created successfully" };
      }
      const errorData = await response.json();
      console.error("Error creating integration:", errorData);
      return { success: false, error: errorData };
    } catch (error) {
      console.error("Error creating integration:", error);
      return { success: false, error: error.message };
    }
  };

  const getIntegrationTypes = async (): Promise<IntegrationTypesResponse> => {
    const url = `${BASE_URL}/integrations/properties`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP error, status: ${res.status}`);
      }
      return res.json();
    });
  };

  const getIntegrationMapping =
    async (): Promise<IntegrationMappingResponse> => {
      const url = `${BASE_URL}/integrations/mapping`;
      return await authFetch(url, {
        method: "GET",
      }).then((res) => {
        if (!res.ok) {
          throw new Error(`HTTP error, status: ${res.status}`);
        }
        return res.json();
      });
    };

  const getResourceMappings = async (integrationId: string) => {
    const url = `${BASE_URL}/integrations/${integrationId}/resource-mappings`;
    try {
      const response = await authFetch(url, {
        method: "GET",
      });

      if (!response.ok) {
        throw new Error(`HTTP error, status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting resource mappings:", error);
      throw error;
    }
  };

  const deleteResourceMapping = async (
    integrationId: string,
    resourceId: string,
  ) => {
    const url = `${BASE_URL}/integrations/${integrationId}/resource-mappings/${resourceId}`;
    try {
      const response = await authFetch(url, {
        method: "DELETE",
      });

      if (response.status === 200) {
        return {
          success: true,
          message: "Resource mapping deleted successfully",
        };
      }
      const errorData = await response.json();
      console.error("Error deleting resource mapping:", errorData);
      return { success: false, error: errorData };
    } catch (error) {
      console.error("Error deleting resource mapping:", error);
      return { success: false, error: error.message };
    }
  };

  const createResourceMapping = async (data: {
    integrationId: string;
    direction: string;
    integrationType: string;
    thirdPartyId?: string;
    resourceThirdPartyId?: string;
    resourceBaseType: string;
    resourceType: string;
    resourceId: string;
    metaData?: Record<string, string>;
  }) => {
    const url = `${BASE_URL}/integrations/resource-mappings`;
    try {
      const response = await authFetch(url, {
        method: "POST",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      if (response.status === 201) {
        return {
          success: true,
          message: "Resource mapping created successfully",
        };
      }
      const errorData = await response.json();
      console.error("Error creating resource mapping:", errorData);
      return { success: false, error: errorData };
    } catch (error) {
      console.error("Error creating resource mapping:", error);
      return { success: false, error: error.message };
    }
  };

  const validatePath = async (resourcePath: string): Promise<boolean> => {
    const url = `${BASE_URL}/integrations/validate-path?resourcePath=${encodeURIComponent(resourcePath)}`;
    try {
      const response = await authFetch(url, {
        method: "GET",
        headers: {
          Accept: "text/plain",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error, status: ${response.status}`);
      }

      const result = await response.text();
      return result === "true";
    } catch (error) {
      console.error("Error validating path:", error);
      return false;
    }
  };

  const doIntegrationAction = async (
    integrationId: string,
    actionName: string,
    params: Record<string, string>,
  ): Promise<void> => {
    const url = `${BASE_URL}/integrations/${integrationId}/action/${actionName}`;
    try {
      const response = await authFetch(url, {
        method: "POST",
        body: JSON.stringify(params),
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (response.status === 202) {
        return;
      }
      throw new Error(`HTTP error, status: ${response.status}`);
    } catch (error) {
      console.error("Error doing integration action:", error);
      throw error;
    }
  };

  return {
    getIntegrations,
    getIntegrationDetail,
    updateIntegration,
    deleteIntegration,
    createIntegration,
    getIntegrationTypes,
    getIntegrationMapping,
    deleteResourceMapping,
    createResourceMapping,
    getResourceMappings,
    validatePath,
    doIntegrationAction,
  };
};
