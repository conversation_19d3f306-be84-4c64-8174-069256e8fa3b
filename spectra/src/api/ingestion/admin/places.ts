import { useAuthFetch } from "context/AuthContext";
import { BASE_URL, type PlaceType } from "../common";

export type BootstrapStateResponse = {
  iotBootstrapped: boolean;
  timeseriesBootstrapped: boolean;
};

export const useAdminPlacesAPI = () => {
  const { authFetch } = useAuthFetch();

  const getBootstrapState = async (
    placeType: PlaceType,
    placeId: string,
  ): Promise<BootstrapStateResponse> => {
    const url = `${BASE_URL}/admin/${placeType}/${placeId}/bootstrap-state`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => res.json());
  };

  const bootstrapIot = async (
    placeType: PlaceType,
    placeId: string,
  ): Promise<void> => {
    const url = `${BASE_URL}/admin/${placeType}/${placeId}/iot/bootstrap`;
    const response = await authFetch(url, {
      method: "POST",
    });
    if (response.status === 201) {
      return;
    }
    const error = await response.json();
    throw new Error(error.message || "Failed to bootstrap IoT");
  };

  const bootstrapTimeseries = async (
    placeType: PlaceType,
    placeId: string,
  ): Promise<void> => {
    const url = `${BASE_URL}/admin/${placeType}/${placeId}/timeseries/bootstrap`;
    const response = await authFetch(url, {
      method: "POST",
    });
    if (response.status === 201) {
      return;
    }
    const error = await response.json();
    throw new Error(error.message || "Failed to bootstrap timeseries");
  };

  return {
    getBootstrapState,
    bootstrapIot,
    bootstrapTimeseries,
  };
};
