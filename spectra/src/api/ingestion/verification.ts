import { useAuthFetch } from "context/AuthContext";

import type { GenericApiError } from "api/types";
import { BASE_URL } from "./common";

export type VerificationResponse = {
  success: boolean;
  message: string;
};

export type CheckVerificationResponse = {
  isValid: boolean;
};

export const useVerificationApi = () => {
  const { authFetch } = useAuthFetch();

  const startPhoneNumberVerification = async (
    phoneNumber: string,
  ): Promise<VerificationResponse> => {
    const url = `${BASE_URL}/verification/phone-number/start`;
    return await authFetch(url, {
      method: "POST",
      body: `"${phoneNumber}"`, // technically this is a string, but we need to send it as a json object
      headers: {
        "Content-Type": "application/json",
      },
    }).then(async (res) => {
      if (!res.ok) {
        // there are two possible error formats, one is a GenericApiError, the other is a string
        const text = await res.text();
        try {
          const json = JSON.parse(text) as GenericApiError;
          return { success: false, message: json.errors.message[0] };
        } catch (e) {
          return { success: false, message: text };
        }
      }
      return res.json();
    });
  };

  const checkPhoneNumberVerification = async (
    phoneNumber: string,
    code: string,
  ): Promise<CheckVerificationResponse> => {
    const url = `${BASE_URL}/verification/phone-number/check`;
    return await authFetch(url, {
      method: "POST",
      body: JSON.stringify({ phoneNumber, code }),
      headers: {
        "Content-Type": "application/json",
      },
    }).then(async (res) => {
      if (!res.ok) {
        throw new Error(`HTTP error, status: ${res.status}`);
      }
      return res.json();
    });
  };

  return {
    startPhoneNumberVerification,
    checkPhoneNumberVerification,
  };
};
