import { useAuthFetch } from "context/AuthContext";

import { BASE_URL } from "./common";

export type OrganizationTreeNode = {
  organizationId: string;
  displayName: string;
  logoUrl: string | null;
  children: OrganizationTreeNode[];
};

export type Organization = {
  organizationId: string;
  parentOrganizationId: string | null;
  childOrganizationIds: string[];
  ancestryPath: string;
  name: string;
  displayName: string;
  isRoot: boolean;
  isSoftDeleted: boolean;
  identityConfig: {
    identityProvider: string;
    providerOrgId: string;
  };
  brandingConfig: {
    hasUploadedLogo: boolean;
    logoUrl: string | null;
    primaryColor: string;
    secondaryColor: string;
  };
  createdAt: string;
  createdBy: string;
};

export type OrganizationTree = {
  organizationTree: OrganizationTreeNode[];
  organizations: Organization[];
};

export const useIdentityPlatformApi = () => {
  const { authFetch } = useAuthFetch();

  const getUserPlatformOrganization = async (): Promise<Organization> => {
    const url = `${BASE_URL}/identity/platform/organization`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => res.json());
  };

  const getUserPlatformOrganizationDescendants =
    async (): Promise<OrganizationTree> => {
      const url = `${BASE_URL}/identity/platform/organization/descendants`;
      return await authFetch(url, {
        method: "GET",
      }).then((res) => res.json());
    };

  return {
    getUserPlatformOrganization,
    getUserPlatformOrganizationDescendants,
  };
};
