import { useAuthFetch } from "context/AuthContext";
import type { Dayjs } from "dayjs";

import { BASE_URL, type MonitorData, formatDate } from "../enterprise";
import type { ErrorType, SuccessType } from "../shared";

export type MetricsConfig = {
  metricName: string;
  missingDataBehavior: "NO_INFILL" | "INFILL_ZERO" | "INFILL_LAST_VALUE";
};

export type VariablesConfig = {
  name: string;
  expression: string;
  expressionLanguage: "DYNAMIC_EXPRESSO";
};

export type MonitorState = "UNHEALTHY" | "WARNING" | "HEALTHY";
export type MonitorDataState = "MISSING_DATA" | "NO_DATA" | "COMPLETE_DATA";

export type AlertState = "UNHEALTHY" | "HEALTHY" | "WARNING";
export type AlertConfig = {
  alertId: string | undefined;

  alertName: string;
  state: AlertState;
  message: string;
  severity: string;
  condition: string;
  integrationIds: string[];
  metadata: Record<string, string>;
};

export type MonitorRequiredFields = {
  partnerId: string;
  placeId: string;
  placeType: string;
  resourceType: string;
  resourceId: string;

  monitorName: string;
  monitorLanguage: "DYNAMIC_EXPRESSO"; // TODO: add?
  frequency: string; // m, h, or d as the unit and a valid number before the unit
  lookbackPeriod: string; // m, h, or d as the unit and a valid number before the unit

  metrics: MetricsConfig[];
  variables: VariablesConfig[];
  alertConfigs: AlertConfig[];

  metadata: Record<string, string>;
};

export type Monitor = MonitorRequiredFields & {
  isEnabled: boolean; // should this be in the required fields?

  monitorId: string; // can be null for new monitors
  version: number;
  createdAt: string; // "2025-02-10T14:38:21.978-05:00",
  createdBy: string;

  state: MonitorState;
  dataState: MonitorDataState;
};

export type MonitorDiff = {
  metricsToRemove: string[];
  variablesToRemove: string[];
  alertConfigsToRemove: string[];
};

export type MonitorListResponse = {
  items: Monitor[];
  continuationToken?: string;
  hasMore: boolean;
};

export type MonitorSummary = {
  stateSummary: Record<MonitorState, number>;
  dataStateSummary: Record<MonitorDataState, number>;
  calculationTime: string; // ISO 8601
};

export const useMonitorsApi = () => {
  const { authFetch } = useAuthFetch();

  const getMonitors = async (
    partnerId: string,
    continuationToken?: string,
    limit?: number,
  ): Promise<MonitorListResponse> => {
    const params = new URLSearchParams();
    params.set("partnerId", partnerId);
    if (continuationToken) {
      params.set("continuationToken", continuationToken);
    }
    if (limit) {
      params.set("limit", limit.toString());
    }

    const url = `${BASE_URL}/v1/monitors?${params.toString()}`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => res.json());
  };

  const getMonitorForThing = async (
    thingId: string,
    monitorId: string,
    partnerId: string,
  ): Promise<Monitor> => {
    const params = new URLSearchParams();
    params.set("partnerId", partnerId);

    const url = `${BASE_URL}/v1/monitors/things/${thingId}/${monitorId}?${params.toString()}`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => res.json());
  };

  const updateMonitorForThing = async (
    thingId: string,
    monitorId: string,
    partnerId: string,
    monitor: Monitor,
  ): Promise<Monitor & MonitorDiff> => {
    const params = new URLSearchParams();
    params.set("partnerId", partnerId);

    const url = `${BASE_URL}/v1/monitors/things/${thingId}/${monitorId}?${params.toString()}`;
    return await authFetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(monitor),
    }).then((res) => {
      if (!res.ok) {
        return res.json().then((json) => {
          throw json;
        });
      }
      return { success: true, message: "Monitor updated successfully" };
    });
  };

  const deleteMonitorForThing = async (
    thingId: string,
    monitorId: string,
    partnerId: string,
  ): Promise<void> => {
    const params = new URLSearchParams();
    params.set("partnerId", partnerId);

    const url = `${BASE_URL}/v1/monitors/things/${thingId}/${monitorId}?${params.toString()}`;
    await authFetch(url, {
      method: "DELETE",
    }).then((res) => {
      if (!res.ok) {
        return res.json().then((json) => {
          throw json;
        });
      }
    });
  };

  const createMonitorForThing = async (
    thingId: string,
    partnerId: string,
    monitorDefinition: MonitorRequiredFields,
  ): Promise<ErrorType | SuccessType> => {
    const params = new URLSearchParams();
    params.set("partnerId", partnerId);

    const url = `${BASE_URL}/v1/monitors/things/${thingId}?${params.toString()}`;
    return await authFetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(monitorDefinition),
    }).then((res) => {
      if (!res.ok) {
        return res.json().then((json) => {
          throw json;
        });
      }
      return { success: true, message: "Monitor created successfully" };
    });
  };
  const getMonitorDataForPlace = async (
    placeId: string,
    placeType: string,
    resourceId: string,
    monitorId: string,
    partnerId: string,
    start: Dayjs,
    end: Dayjs,
    limit?: number,
  ): Promise<MonitorData[]> => {
    const startFmtd = formatDate(start);
    const endFmtd = formatDate(end);

    const params = new URLSearchParams();
    params.set("partnerId", partnerId);
    params.set("startTime", startFmtd);
    params.set("endTime", endFmtd);
    params.set("resourceId", resourceId);
    params.set("monitorId", monitorId);
    if (limit !== undefined) {
      params.set("limit", limit.toString());
    }

    const url = `${BASE_URL}/v1/monitors/data/${placeType}/${placeId}?${params.toString()}`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => {
      if (res.status !== 200) {
        throw new Error("Monitor data not found");
      }
      return res.json();
    });
  };

  const evaluateMonitorVariable = async (
    thingId: string,
    monitorId: string,
    partnerId: string,
    includeVariables: boolean,
    expression: string,
  ): Promise<string> => {
    const params = new URLSearchParams();
    params.set("partnerId", partnerId);
    params.set("includeVariables", includeVariables.toString());
    params.set("expression", expression);

    const url = `${BASE_URL}/v1/monitors/things/${thingId}/${monitorId}/evaluate?${params.toString()}`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => {
      if (!res.ok) {
        return res.json().then((json) => {
          throw json;
        });
      }
      return res.json(); // technically returned as a quoted string
    });
  };

  const getMonitorSummary = async (
    partnerId: string,
  ): Promise<MonitorSummary> => {
    const params = new URLSearchParams();
    params.set("partnerId", partnerId);

    const url = `${BASE_URL}/v1/monitors/summary?${params.toString()}`;
    return await authFetch(url, {
      method: "GET",
    }).then((res) => res.json());
  };

  return {
    getMonitors,
    getMonitorForThing,
    updateMonitorForThing,
    deleteMonitorForThing,
    createMonitorForThing,
    getMonitorDataForPlace,
    evaluateMonitorVariable,
    getMonitorSummary,
  };
};
