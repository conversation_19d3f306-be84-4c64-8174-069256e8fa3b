import { useAuthFetch } from "context/AuthContext";
import { BASE_URL } from "./common";

export enum IntegrationDirection {
  INGRESS = "INGRESS",
  EGRESS = "EGRESS",
}

export enum IngressIntegrationType {
  UtilityAPI = "UtilityAPI",
  Weather = "Weather",
}

export enum EgressIntegrationType {
  WEBHOOK = "WEBHOOK",
  EMAIL = "EMAIL",
  SMS = "SMS",
  PHONE = "PHONE",
}

export enum MultiValueFieldType {
  URL = "url",
  EMAIL = "recipientAddress",
  PHONE = "recipientPhone",
}

export const MULTI_VALUE_FIELD_LABELS: Record<MultiValueFieldType, string> = {
  [MultiValueFieldType.URL]: "URL",
  [MultiValueFieldType.EMAIL]: "Email Address",
  [MultiValueFieldType.PHONE]: "Phone Number",
};

export const MULTI_VALUE_FIELD_TYPES = Object.values(MultiValueFieldType);

export type IntegrationAction = {
  name: string;
  description: string;
  actionParams: string[];
};

export type IntegrationActionsResponse = {
  [IntegrationDirection.INGRESS]: {
    [K in IngressIntegrationType]: IntegrationAction[];
  };
  [IntegrationDirection.EGRESS]: {
    [K in EgressIntegrationType]: IntegrationAction[];
  };
};

export type IntegrationProperty = {
  name: string;
  description: string;
  isEditable: boolean;
};

export type IntegrationPropertiesResponse = {
  [IntegrationDirection.INGRESS]: {
    [K in IngressIntegrationType]: IntegrationProperty[];
  };
  [IntegrationDirection.EGRESS]: {
    [K in EgressIntegrationType]: IntegrationProperty[];
  };
};

export type IntegrationMapping = {
  type: string;
  supportedResourceTypes: string[];
};

export type IntegrationMappingResponse = {
  [IntegrationDirection.INGRESS]: IntegrationMapping[];
  [IntegrationDirection.EGRESS]: IntegrationMapping[];
};

export const useIntegrationsDataAPI = () => {
  const { authFetch } = useAuthFetch();

  const getIntegrationActions =
    async (): Promise<IntegrationActionsResponse> => {
      const url = `${BASE_URL}/integration-actions`;
      const response = await authFetch(url, {
        method: "GET",
      });
      return response.json();
    };

  const getIntegrationProperties =
    async (): Promise<IntegrationPropertiesResponse> => {
      const url = `${BASE_URL}/integration-properties`;
      const response = await authFetch(url, {
        method: "GET",
      });
      return response.json();
    };

  const getIntegrationMapping =
    async (): Promise<IntegrationMappingResponse> => {
      const url = `${BASE_URL}/integration-mapping`;
      const response = await authFetch(url, {
        method: "GET",
      });
      return response.json();
    };

  return {
    getIntegrationActions,
    getIntegrationProperties,
    getIntegrationMapping,
  };
};
