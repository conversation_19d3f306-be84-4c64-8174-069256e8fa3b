import { Meta } from '@storybook/blocks';

<Meta title="Introduction/Welcome" />

# Welcome to Spectra UI

Spectra UI is a component library for the Spectra Dashboard application.

## Design System

Our design system is built on the following principles:

- **Consistency**: Components should look and behave consistently across the application
- **Accessibility**: Components should be accessible to all users
- **Flexibility**: Components should be flexible enough to handle a variety of use cases
- **Performance**: Components should be optimized for performance

## Color Palette

Our color palette is based on a set of blues and grays, with accent colors for specific states.

### Blues

- `blue10`: #0A121F
- `blue20`: #15253E
- `blue30`: #1F375D
- `blue40`: #2A447C
- `blue50`: #345C9B
- `blue60`: #5D7DAF
- `blue70`: #859DC3
- `blue80`: #AEBED7
- `blue90`: #D6DEEB
- `blue95`: #ECF0F5
- `blue97`: #F5F7FA

### Space (Grays)

- `space10`: #06090D
- `space20`: #0B131A
- `space30`: #111C26
- `space40`: #162633
- `space50`: #1C2F40
- `space60`: #495966
- `space70`: #77828C
- `space80`: #A4ACB3
- `space90`: #E8EAEC

### Accent Colors

- Green: For success states
- Red: For error states
- Yellow: For warning states
- Purple: For information states

## Typography

We use the Inter font family for all text in the application.

### Text Styles

- `text-title`: 38px, 300 weight
- `text-subtitle`: 28px, 400 weight
- `text-heading1`: 20px, 500 weight
- `text-heading2`: 16px, 500 weight
- `text-heading3`: 14px, 500 weight
- `text-body`: 14px, 400 weight

## Components

Our component library is organized into the following categories:

- **UIKit**: Basic UI components like buttons, inputs, and modals
- **Layout**: Components for page layout
- **Data Display**: Components for displaying data
- **Navigation**: Components for navigation
- **Feedback**: Components for user feedback

## Getting Started

To use a component, import it from the components directory:

```jsx
import Button from 'components/uikit/button';

function MyComponent() {
  return <Button>Click Me</Button>;
}
```

## Contributing

To add a new component to the library:

1. Create a new file in the appropriate directory
2. Create a story file for the component
3. Document the component's props and usage
4. Add examples of different states and variants
