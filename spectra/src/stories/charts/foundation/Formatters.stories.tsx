import type { Meta, StoryObj } from '@storybook/react';
import { 
  formatNumericValue, 
  formatPercentage, 
  formatTimeAxis, 
  formatDateAxis,
  formatSmartDateTime,
  formatTooltipTime,
  formatBoolean,
  formatTooltipValue,
  formatUnit,
  formatLabelWithUnit
} from '../../../components/charts/foundation/formatters';

// Demo component to showcase formatters
const FormatterDemo = ({ 
  formatter, 
  values, 
  title, 
  description 
}: { 
  formatter: (value: any, ...args: any[]) => string;
  values: any[];
  title: string;
  description: string;
}) => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">{title}</h3>
    <p className="text-sm text-gray-600 mb-4">{description}</p>
    <div className="space-y-2">
      {values.map((value, index) => (
        <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
          <code className="text-sm">{JSON.stringify(value)}</code>
          <span className="font-mono text-blue-600">→ "{formatter(value)}"</span>
        </div>
      ))}
    </div>
  </div>
);

const meta: Meta<typeof FormatterDemo> = {
  title: 'Charts/Foundation/Formatters',
  component: FormatterDemo,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# Chart Formatters - Foundation Utilities

The chart formatters provide consistent data formatting across all charts in the application. These utilities ensure that numbers, dates, and other values are displayed uniformly.

## Key Features:
- **2-decimal rule** for numeric values (critical requirement)
- **Smart time formatting** based on time ranges
- **Boolean value formatting** for status indicators
- **Unit formatting** with consistent display
- **Type-safe formatting** with proper fallbacks

## Critical Rule:
**All numeric values must be formatted to a maximum of 2 decimal places** to maintain consistency across the application.

## Available Formatters:
- \`formatNumericValue\`: Numbers with 2-decimal max rule
- \`formatPercentage\`: Percentage values with configurable decimals
- \`formatTimeAxis\`: Time values for chart axes
- \`formatDateAxis\`: Date values for chart axes
- \`formatSmartDateTime\`: Smart time/date based on range
- \`formatTooltipTime\`: Full timestamp for tooltips
- \`formatBoolean\`: Boolean values as "True"/"False"
- \`formatTooltipValue\`: Generic formatter for any value type
- \`formatUnit\`: Unit display with parentheses
- \`formatLabelWithUnit\`: Label + unit combination
        `
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof FormatterDemo>;

// Numeric value formatting (most critical)
export const NumericFormatting: Story = {
  args: {
    formatter: formatNumericValue,
    values: [
      123,           // Integer
      123.4,         // One decimal
      123.45,        // Two decimals
      123.456,       // Three decimals (should truncate)
      123.456789,    // Many decimals (should truncate)
      0,             // Zero
      0.1,           // Small decimal
      0.12,          // Small two decimals
      0.123,         // Small three decimals (should truncate)
      1000.999,      // Large with decimals
      "invalid",     // Invalid string
      null,          // Null value
      undefined,     // Undefined value
      NaN            // NaN value
    ],
    title: "formatNumericValue - Critical 2-Decimal Rule",
    description: "Tests the most important formatting rule: all numeric values must display with maximum 2 decimal places. Integers show no decimals, decimals show up to 2 places."
  },
  parameters: {
    docs: {
      description: {
        story: 'This is the **most critical formatter** - it enforces the 2-decimal rule across all charts. Notice how values with more than 2 decimals are truncated, while integers remain whole numbers.'
      }
    }
  }
};

// Percentage formatting
export const PercentageFormatting: Story = {
  args: {
    formatter: (value: number) => formatPercentage(value),
    values: [
      85,
      85.5,
      85.55,
      85.555,
      0,
      100,
      99.9,
      0.1,
      NaN,
      null
    ],
    title: "formatPercentage - Default 1 Decimal",
    description: "Formats percentage values with 1 decimal place by default. Can be configured for different decimal places."
  },
  parameters: {
    docs: {
      description: {
        story: 'Percentage formatting with default 1 decimal place. Used for efficiency ratings, battery levels, etc.'
      }
    }
  }
};

// Boolean formatting
export const BooleanFormatting: Story = {
  args: {
    formatter: formatBoolean,
    values: [
      true,
      false,
      1,      // Truthy
      0,      // Falsy
      "true", // String
      "",     // Empty string
      null,   // Null
      undefined // Undefined
    ],
    title: "formatBoolean - True/False Display",
    description: "Converts boolean values to 'True' or 'False' strings for consistent display in charts."
  },
  parameters: {
    docs: {
      description: {
        story: 'Boolean formatting for status indicators and binary data in charts.'
      }
    }
  }
};

// Time axis formatting
export const TimeAxisFormatting: Story = {
  args: {
    formatter: formatTimeAxis,
    values: [
      new Date('2024-01-01T09:30:00Z').getTime(),
      new Date('2024-01-01T14:45:00Z').getTime(),
      new Date('2024-01-01T23:59:00Z').getTime(),
      new Date('2024-01-01T00:00:00Z').getTime(),
      "2024-01-01T12:00:00Z",
      Date.now()
    ],
    title: "formatTimeAxis - Hour:Minute Display",
    description: "Formats timestamps for chart X-axis display showing hour and minute."
  },
  parameters: {
    docs: {
      description: {
        story: 'Time axis formatting for hourly and sub-daily chart displays.'
      }
    }
  }
};

// Date axis formatting
export const DateAxisFormatting: Story = {
  args: {
    formatter: formatDateAxis,
    values: [
      new Date('2024-01-01').getTime(),
      new Date('2024-06-15').getTime(),
      new Date('2024-12-31').getTime(),
      "2024-03-15T10:30:00Z",
      Date.now()
    ],
    title: "formatDateAxis - Date Display",
    description: "Formats timestamps for chart X-axis display showing full date."
  },
  parameters: {
    docs: {
      description: {
        story: 'Date axis formatting for daily, weekly, and longer time range charts.'
      }
    }
  }
};

// Smart date/time formatting
export const SmartDateTimeFormatting: Story = {
  args: {
    formatter: (value: any) => {
      const oneDay = 24 * 60 * 60 * 1000;
      return `Short range: ${formatSmartDateTime(value, oneDay / 2)} | Long range: ${formatSmartDateTime(value, oneDay * 2)}`;
    },
    values: [
      new Date('2024-01-01T09:30:00Z').getTime(),
      new Date('2024-01-01T14:45:00Z').getTime(),
      "2024-01-01T12:00:00Z"
    ],
    title: "formatSmartDateTime - Range-Based Formatting",
    description: "Automatically chooses time or date format based on the time range. Short ranges show time, long ranges show date."
  },
  parameters: {
    docs: {
      description: {
        story: 'Smart formatting that adapts based on time range - shows time for short ranges, dates for long ranges.'
      }
    }
  }
};

// Tooltip value formatting (generic)
export const TooltipValueFormatting: Story = {
  args: {
    formatter: formatTooltipValue,
    values: [
      123.456,       // Number
      true,          // Boolean
      false,         // Boolean
      "Custom Text", // String
      null,          // Null
      undefined,     // Undefined
      0,             // Zero
      ""             // Empty string
    ],
    title: "formatTooltipValue - Generic Formatter",
    description: "Generic formatter that handles any value type appropriately. Numbers get 2-decimal formatting, booleans become True/False, strings pass through."
  },
  parameters: {
    docs: {
      description: {
        story: 'Generic tooltip formatter that automatically handles different data types with appropriate formatting.'
      }
    }
  }
};

// Unit formatting
export const UnitFormatting: Story = {
  args: {
    formatter: formatUnit,
    values: [
      "kW",
      "kWh", 
      "V",
      "A",
      "°C",
      "%",
      "",
      null,
      undefined
    ],
    title: "formatUnit - Unit Display",
    description: "Formats units with parentheses for display. Empty or null units return empty string."
  },
  parameters: {
    docs: {
      description: {
        story: 'Unit formatting adds parentheses around units for consistent display in labels and legends.'
      }
    }
  }
};

// Label with unit formatting
export const LabelWithUnitFormatting: Story = {
  args: {
    formatter: (label: string) => {
      const units = ["kW", "kWh", "V", "", null];
      return units.map(unit => `${label}: ${formatLabelWithUnit(label, unit)}`).join(" | ");
    },
    values: [
      "Power Output",
      "Energy Consumed",
      "Voltage",
      "Status"
    ],
    title: "formatLabelWithUnit - Label + Unit Combination",
    description: "Combines labels with units for complete display strings. Handles missing units gracefully."
  },
  parameters: {
    docs: {
      description: {
        story: 'Combines labels with units for chart legends and axis labels. Gracefully handles missing units.'
      }
    }
  }
};
