import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { 
  CHART_COLORS, 
  CHART_PALETTES, 
  getChartColors,
  getEnergyColors,
  getStatusColors
} from '../../../components/charts/foundation/colors';

// Color swatch component
const ColorSwatch = ({ color, name, description }: { color: string; name: string; description?: string }) => (
  <div className="flex items-center space-x-3 p-3 border rounded-lg">
    <div 
      className="w-12 h-12 rounded border-2 border-gray-300" 
      style={{ backgroundColor: color }}
    />
    <div>
      <div className="font-semibold">{name}</div>
      <div className="text-sm text-gray-600 font-mono">{color}</div>
      {description && <div className="text-xs text-gray-500">{description}</div>}
    </div>
  </div>
);

// Palette display component
const PaletteDisplay = ({ colors, title, description }: { colors: string[]; title: string; description: string }) => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">{title}</h3>
    <p className="text-sm text-gray-600 mb-4">{description}</p>
    <div className="flex space-x-2 mb-4">
      {colors.map((color, index) => (
        <div key={index} className="text-center">
          <div 
            className="w-16 h-16 rounded border-2 border-gray-300 mb-2" 
            style={{ backgroundColor: color }}
          />
          <div className="text-xs font-mono">{color}</div>
        </div>
      ))}
    </div>
  </div>
);

const meta: Meta<typeof ColorSwatch> = {
  title: 'Charts/Foundation/Colors',
  component: ColorSwatch,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# Chart Colors - Foundation System

The chart color system provides consistent, accessible colors across all chart components. It includes base colors, specialized palettes, and utility functions for color assignment.

## Key Features:
- **Consistent color palette** across all charts
- **Specialized palettes** for energy, status, and other domains
- **Automatic color assignment** functions
- **Accessibility considerations** with sufficient contrast
- **Theme integration** using design system colors where possible

## Color Categories:
- **Base Colors**: Primary chart colors for general use
- **Energy Colors**: Specialized colors for power/energy data
- **Status Colors**: Colors for health/status indicators
- **Neutral Colors**: Grays for axes, grids, and text
- **Background Colors**: Chart background and alternate colors

## Utility Functions:
- \`getChartColors(count)\`: Get colors for multi-series charts
- \`getEnergyColors(count)\`: Get energy-specific colors
- \`getStatusColors(count)\`: Get status-specific colors
        `
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof ColorSwatch>;

// Base chart colors
export const BaseColors: Story = {
  render: () => (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-bold mb-4">Base Chart Colors</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ColorSwatch 
          color={CHART_COLORS.primary} 
          name="Primary" 
          description="Main data color for single-series charts"
        />
        <ColorSwatch 
          color={CHART_COLORS.secondary} 
          name="Secondary" 
          description="Secondary data color for multi-series charts"
        />
        <ColorSwatch 
          color={CHART_COLORS.tertiary} 
          name="Tertiary" 
          description="Third data color for multi-series charts"
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Base colors used for general chart data. These are the primary colors assigned to data series.'
      }
    }
  }
};

// Energy-specific colors
export const EnergyColors: Story = {
  render: () => (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-bold mb-4">Energy-Specific Colors</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ColorSwatch 
          color={CHART_COLORS.stored} 
          name="Stored Energy" 
          description="Color for stored/battery energy data"
        />
        <ColorSwatch 
          color={CHART_COLORS.forward} 
          name="Forward Energy" 
          description="Color for forward/input energy data"
        />
        <ColorSwatch 
          color={CHART_COLORS.net} 
          name="Net Energy" 
          description="Color for net energy calculations"
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Specialized colors for energy-related data to maintain consistency across energy charts.'
      }
    }
  }
};

// Status colors
export const StatusColors: Story = {
  render: () => (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-bold mb-4">Status Colors</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ColorSwatch 
          color={CHART_COLORS.online} 
          name="Online/Healthy" 
          description="Green color for healthy/online status"
        />
        <ColorSwatch 
          color={CHART_COLORS.offline} 
          name="Offline/Error" 
          description="Red color for offline/error status"
        />
        <ColorSwatch 
          color={CHART_COLORS.warning} 
          name="Warning/Caution" 
          description="Orange color for warning states"
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Status colors for health indicators, device states, and alert levels.'
      }
    }
  }
};

// Neutral colors
export const NeutralColors: Story = {
  render: () => (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-bold mb-4">Neutral Colors</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ColorSwatch 
          color={CHART_COLORS.axis} 
          name="Axis" 
          description="Color for chart axes and ticks"
        />
        <ColorSwatch 
          color={CHART_COLORS.grid} 
          name="Grid" 
          description="Color for chart grid lines"
        />
        <ColorSwatch 
          color={CHART_COLORS.text} 
          name="Text" 
          description="Color for chart text and labels"
        />
        <ColorSwatch 
          color={CHART_COLORS.background} 
          name="Background" 
          description="Primary chart background color"
        />
        <ColorSwatch 
          color={CHART_COLORS.backgroundAlt} 
          name="Background Alt" 
          description="Alternative background color"
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Neutral colors for chart infrastructure elements like axes, grids, and backgrounds.'
      }
    }
  }
};

// Default palette
export const DefaultPalette: Story = {
  render: () => (
    <div className="p-6">
      <PaletteDisplay 
        colors={CHART_PALETTES.default}
        title="Default Palette"
        description="General-purpose color palette for multi-series charts. Provides good contrast and visual separation."
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Default color palette used for general multi-series charts when no specific palette is needed.'
      }
    }
  }
};

// Energy palette
export const EnergyPalette: Story = {
  render: () => (
    <div className="p-6">
      <PaletteDisplay 
        colors={CHART_PALETTES.energy}
        title="Energy Palette"
        description="Specialized palette for energy and power-related charts. Uses blues and teals for energy themes."
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Energy-specific palette for power, energy consumption, and generation charts.'
      }
    }
  }
};

// Status palette
export const StatusPalette: Story = {
  render: () => (
    <div className="p-6">
      <PaletteDisplay 
        colors={CHART_PALETTES.status}
        title="Status Palette"
        description="Status-specific palette for health indicators and system status charts."
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Status palette for health indicators, device status, and alert level charts.'
      }
    }
  }
};

// Color function demonstrations
export const ColorFunctions: Story = {
  render: () => (
    <div className="p-6 space-y-6">
      <h2 className="text-xl font-bold mb-4">Color Assignment Functions</h2>
      
      <PaletteDisplay 
        colors={getChartColors(5)}
        title="getChartColors(5)"
        description="Automatically assigns colors for 5 data series from the default palette."
      />
      
      <PaletteDisplay 
        colors={getEnergyColors(3)}
        title="getEnergyColors(3)"
        description="Automatically assigns energy-specific colors for 3 data series."
      />
      
      <PaletteDisplay 
        colors={getStatusColors(4)}
        title="getStatusColors(4)"
        description="Automatically assigns status-specific colors for 4 data series."
      />
      
      <div className="p-4 border rounded-lg bg-yellow-50">
        <h3 className="font-semibold text-yellow-800 mb-2">Usage Example</h3>
        <pre className="text-sm text-yellow-700">
{`// Automatic color assignment
const colors = getChartColors(timeseries.types.length);

// Energy-specific colors
const energyColors = getEnergyColors(3);

// Status-specific colors  
const statusColors = getStatusColors(deviceStates.length);`}
        </pre>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates the utility functions for automatic color assignment based on data series count and type.'
      }
    }
  }
};
