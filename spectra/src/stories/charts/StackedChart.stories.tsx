import type { <PERSON>a, StoryObj } from '@storybook/react';
import { StackedChart } from '../../components/charts/StackedChart';
import type { Timeseries } from '../../api/data';

// Mock data generator for stacked charts
const createStackedTimeseries = (
  types: string[],
  units: string[],
  valueCount: number = 24,
  valueRanges: Array<[number, number]> = [[0, 100]]
): Timeseries => {
  const now = new Date();
  const values = Array.from({ length: valueCount }, (_, i) => {
    const time = new Date(now.getTime() - (valueCount - i - 1) * 60 * 60 * 1000);
    const value: any = { time: time.toISOString() };

    types.forEach((type, index) => {
      const range = valueRanges[index] || valueRanges[0];
      const baseValue = range[0] + (range[1] - range[0]) * Math.random();
      const variation = baseValue * 0.15 * (Math.random() - 0.5);
      value[type] = Math.max(range[0], baseValue + variation);
    });

    return value;
  });

  return {
    start: values.length > 0 ? values[0].time : new Date().toISOString(),
    end: values.length > 0 ? values[values.length - 1].time : new Date().toISOString(),
    types,
    units,
    values,
    summary: {}
  };
};

const meta: Meta<typeof StackedChart> = {
  title: 'Charts/StackedChart',
  component: StackedChart,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# StackedChart - Multi-Chart Layout

The StackedChart component displays multiple synchronized line charts in a vertical stack layout. Each chart shows a different data series but they share the same time axis and are synchronized for coordinated interaction.

## Key Features:
- **Synchronized charts** with shared time axis
- **Individual chart titles** for each data series
- **Consistent formatting** using foundation system
- **Micro chart layout** optimized for space
- **Synchronized tooltips** across all charts
- **Foundation system integration** for consistent styling

## Use Cases:
- Monitoring multiple related metrics over time
- Comparing different measurement types
- Dashboard layouts with multiple data streams
- Time-series analysis with different scales
        `
      }
    }
  },
  argTypes: {
    timeseries: {
      description: 'Timeseries data with multiple types to display as stacked charts',
      control: false
    },
    colors: {
      description: 'Array of colors for each chart series',
      control: { type: 'object' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof StackedChart>;

// Default energy monitoring scenario
export const EnergyMonitoring: Story = {
  args: {
    timeseries: createStackedTimeseries(
      ['Power Output', 'Voltage', 'Current', 'Temperature'],
      ['kW', 'V', 'A', '°C'],
      48,
      [[20, 80], [220, 250], [10, 40], [20, 45]]
    ),
    colors: ['#4A90E2', '#6E9F66', '#E87D48', '#C55B57']
  },
  parameters: {
    docs: {
      description: {
        story: 'Energy monitoring dashboard showing power output, voltage, current, and temperature as synchronized stacked charts.'
      }
    }
  }
};

// Battery system monitoring
export const BatterySystem: Story = {
  args: {
    timeseries: createStackedTimeseries(
      ['State of Charge', 'Charge Rate', 'Discharge Rate'],
      ['%', 'kW', 'kW'],
      36,
      [[20, 95], [0, 50], [0, 45]]
    ),
    colors: ['#89A7BF', '#6E9F66', '#C55B57']
  },
  parameters: {
    docs: {
      description: {
        story: 'Battery system monitoring with state of charge, charge rate, and discharge rate displayed as synchronized charts.'
      }
    }
  }
};

// High precision decimal formatting test
export const DecimalFormatting: Story = {
  args: {
    timeseries: {
      start: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString(),
      types: ['Precision Voltage', 'Precision Current'],
      units: ['V', 'A'],
      values: [
        {
          time: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          'Precision Voltage': 240.123456789,
          'Precision Current': 15.987654321
        },
        {
          time: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          'Precision Voltage': 239.876543210,
          'Precision Current': 16.123456789
        },
        {
          time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          'Precision Voltage': 241.555555555,
          'Precision Current': 14.777777777
        },
        {
          time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          'Precision Voltage': 240.999999999,
          'Precision Current': 15.111111111
        },
        {
          time: new Date().toISOString(),
          'Precision Voltage': 240.444444444,
          'Precision Current': 15.666666666
        }
      ],
      summary: {}
    },
    colors: ['#4A90E2', '#6E9F66']
  },
  parameters: {
    docs: {
      description: {
        story: 'Tests the **2-decimal formatting rule** across multiple synchronized charts. All values should display with exactly 2 decimal places in tooltips.'
      }
    }
  }
};

// Single chart (edge case)
export const SingleChart: Story = {
  args: {
    timeseries: createStackedTimeseries(
      ['Power Consumption'],
      ['kW'],
      24,
      [[30, 70]]
    ),
    colors: ['#4A90E2']
  },
  parameters: {
    docs: {
      description: {
        story: 'Edge case showing a single chart in the stacked layout - still maintains the same styling and behavior.'
      }
    }
  }
};

// Loading state
export const LoadingState: Story = {
  args: {
    timeseries: null,
    colors: ['#4A90E2', '#6E9F66']
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the loading state when timeseries data is null.'
      }
    }
  }
};

// Empty data
export const EmptyData: Story = {
  args: {
    timeseries: createStackedTimeseries(['Power'], ['kW'], 0),
    colors: ['#4A90E2']
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the loading state when timeseries has no data points.'
      }
    }
  }
};

// Many series (stress test)
export const ManySeriesStressTest: Story = {
  args: {
    timeseries: createStackedTimeseries(
      [
        'Power Output', 'Voltage L1', 'Voltage L2', 'Voltage L3',
        'Current L1', 'Current L2', 'Current L3', 'Temperature',
        'Humidity', 'Pressure'
      ],
      ['kW', 'V', 'V', 'V', 'A', 'A', 'A', '°C', '%', 'Pa'],
      24,
      [
        [20, 80], [220, 250], [220, 250], [220, 250],
        [10, 40], [10, 40], [10, 40], [20, 45],
        [30, 70], [980, 1020]
      ]
    ),
    colors: [
      '#4A90E2', '#6E9F66', '#E87D48', '#C55B57',
      '#6db5d1', '#f2aa3c', '#9B59B6', '#34495E',
      '#89A7BF', '#5ea15f'
    ]
  },
  parameters: {
    docs: {
      description: {
        story: 'Stress test with many data series to verify performance and layout with multiple synchronized charts.'
      }
    }
  }
};

// Different time ranges
export const ShortTimeRange: Story = {
  args: {
    timeseries: createStackedTimeseries(
      ['Real-time Power', 'Real-time Current'],
      ['kW', 'A'],
      12, // Only 12 data points
      [[40, 60], [15, 25]]
    ),
    colors: ['#4A90E2', '#6E9F66']
  },
  parameters: {
    docs: {
      description: {
        story: 'Short time range with fewer data points to test chart behavior with limited data.'
      }
    }
  }
};
