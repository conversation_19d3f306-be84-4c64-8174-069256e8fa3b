import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { RawChart } from '../../components/charts/ChargerPowerChart';
import type { Timeseries } from '../../api/data';

// Mock data for different scenarios
const createMockTimeseries = (
  types: string[],
  units: string[],
  valueCount: number = 24,
  valueRange: [number, number] = [0, 100]
): Timeseries => {
  const now = new Date();
  const values = Array.from({ length: valueCount }, (_, i) => {
    const time = new Date(now.getTime() - (valueCount - i - 1) * 60 * 60 * 1000);
    const value: any = { time: time.toISOString() };

    types.forEach((type, index) => {
      // Create realistic power data with some variation
      const baseValue = valueRange[0] + (valueRange[1] - valueRange[0]) * Math.random();
      const variation = baseValue * 0.2 * (Math.random() - 0.5);
      value[type] = Math.max(0, baseValue + variation);
    });

    return value;
  });

  return {
    start: values.length > 0 ? values[0].time : new Date().toISOString(),
    end: values.length > 0 ? values[values.length - 1].time : new Date().toISOString(),
    types,
    units,
    values,
    summary: {}
  };
};

const meta: Meta<typeof RawChart> = {
  title: 'Charts/ChargerPowerChart (RawChart)',
  component: RawChart,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# ChargerPowerChart (RawChart) - Migrated Component

This component demonstrates the **successful migration** from a complex 104-line custom chart to a **30-line standardized implementation** using the chart foundation system.

## Migration Benefits:
- **70% code reduction** (104 → 30 lines)
- **Automatic 2-decimal formatting** for all numeric values
- **Consistent styling** and colors across the application
- **Built-in loading and empty states**
- **Standardized tooltips and axes**
- **Better accessibility** and responsive behavior

## Key Features:
- Custom legend with units display
- Multiple data series support
- Synchronized colors between chart and legend
- Foundation system integration
        `
      }
    }
  },
  argTypes: {
    timeseries: {
      description: 'Timeseries data to display in the chart',
      control: false
    },
    colors: {
      description: 'Array of colors for each data series',
      control: { type: 'object' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof RawChart>;

// Default story with power data
export const Default: Story = {
  args: {
    timeseries: createMockTimeseries(
      ['Power Output', 'Power Input'],
      ['kW', 'kW'],
      24,
      [20, 80]
    ),
    colors: ['#4A90E2', '#6E9F66']
  },
  parameters: {
    docs: {
      description: {
        story: 'Default charger power chart showing power input and output over 24 hours with the standardized 2-decimal formatting.'
      }
    }
  }
};

// Multiple series with different units
export const MultipleDataSeries: Story = {
  args: {
    timeseries: createMockTimeseries(
      ['AC Power', 'DC Power', 'Battery Level', 'Temperature'],
      ['kW', 'kW', '%', '°C'],
      48,
      [10, 90]
    ),
    colors: ['#4A90E2', '#6E9F66', '#E87D48', '#C55B57']
  },
  parameters: {
    docs: {
      description: {
        story: 'Chart with multiple data series showing different types of measurements with their respective units in the custom legend.'
      }
    }
  }
};

// High precision decimal values to test 2-decimal formatting
export const DecimalFormatting: Story = {
  args: {
    timeseries: {
      start: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString(),
      types: ['Voltage', 'Current'],
      units: ['V', 'A'],
      values: [
        { time: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), Voltage: 240.123456, Current: 15.987654 },
        { time: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), Voltage: 239.876543, Current: 16.123456 },
        { time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), Voltage: 241.555555, Current: 14.777777 },
        { time: new Date().toISOString(), Voltage: 240.999999, Current: 15.111111 }
      ],
      summary: {}
    },
    colors: ['#4A90E2', '#6E9F66']
  },
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates the critical **2-decimal formatting rule** - all values are automatically truncated to 2 decimal places in tooltips and displays.'
      }
    }
  }
};

// Loading state
export const LoadingState: Story = {
  args: {
    timeseries: undefined,
    colors: ['#4A90E2', '#6E9F66']
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the standardized loading state when timeseries data is undefined.'
      }
    }
  }
};

// Empty data state
export const EmptyData: Story = {
  args: {
    timeseries: createMockTimeseries(['Power'], ['kW'], 0),
    colors: ['#4A90E2']
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the standardized empty state when no data points are available.'
      }
    }
  }
};

// Single data series
export const SingleSeries: Story = {
  args: {
    timeseries: createMockTimeseries(['Charging Power'], ['kW'], 24, [30, 50]),
    colors: ['#4A90E2']
  },
  parameters: {
    docs: {
      description: {
        story: 'Simple single-series chart showing just charging power over time.'
      }
    }
  }
};

// Energy-specific colors
export const EnergyColors: Story = {
  args: {
    timeseries: createMockTimeseries(
      ['Forward Energy', 'Net Energy', 'Stored Energy'],
      ['kWh', 'kWh', 'kWh'],
      36,
      [0, 100]
    ),
    colors: ['#4E6D86', '#6B89A2', '#89A7BF'] // Energy palette from foundation
  },
  parameters: {
    docs: {
      description: {
        story: 'Uses the standardized energy color palette from the foundation system for energy-related measurements.'
      }
    }
  }
};

// Boolean data types (converted to numeric for display)
export const BooleanData: Story = {
  args: {
    timeseries: {
      start: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString(),
      types: ['Charging Active', 'Grid Connected'],
      units: ['', ''],
      values: Array.from({ length: 12 }, (_, i) => ({
        time: new Date(Date.now() - (12 - i) * 60 * 60 * 1000).toISOString(),
        'Charging Active': Math.random() > 0.3 ? 1 : 0,
        'Grid Connected': Math.random() > 0.1 ? 1 : 0
      })),
      summary: {}
    },
    colors: ['#6E9F66', '#C55B57']
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows how boolean values (0/1) are handled by the formatting system, useful for status indicators.'
      }
    }
  }
};
