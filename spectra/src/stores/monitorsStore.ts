import type { Monitor } from "api/enterprise/monitors";
import { create } from "zustand";

interface MonitorsState {
  // Data
  monitors: Monitor[];

  // Loading states
  isLoading: boolean;
  isAdding: boolean;
  isUpdating: boolean;

  // Actions
  setMonitors: (monitors: Monitor[]) => void;
  setLoading: (value: boolean) => void;
  setAdding: (value: boolean) => void;
  setUpdating: (value: boolean) => void;
}

export const useMonitorsStore = create<MonitorsState>((set) => ({
  // Initial state
  monitors: [],
  isLoading: false,
  isAdding: false,
  isUpdating: false,

  // Setters
  setMonitors: (monitors) => set({ monitors }),
  setLoading: (value) => set({ isLoading: value }),
  setAdding: (value) => set({ isAdding: value }),
  setUpdating: (value) => set({ isUpdating: value }),
}));
