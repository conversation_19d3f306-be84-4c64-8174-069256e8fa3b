import type { Dayjs } from "utils/dayjs";
import { create } from "zustand";

interface DevicePollingState {
  // Data indexed by thingId
  lastEventTime: Record<string, Dayjs | null>;

  // Actions
  setLastEventTime: (thingId: string, lastEventTime: Dayjs | null) => void;
}

export const useLastPolledTimeStore = create<DevicePollingState>((set) => ({
  // Initial state
  lastEventTime: {},

  // Actions
  setLastEventTime: (thingId, lastEventTime) =>
    set((state) => ({
      lastEventTime: {
        ...state.lastEventTime,
        [thingId]: lastEventTime,
      },
    })),
}));
