/**
 * Centralized error management store
 */

import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";
import type {
  AnyError,
  AppError,
  AuthenticationError,
  ErrorCategory,
} from "../types/errors";
import { createError } from "../utils/errorUtils";

interface ErrorState {
  // State
  errors: AppError[];
  maxErrors: number;
  hasNoAccessError: boolean;

  // Actions
  addError: (error: AppError | AnyError) => void;
  removeError: (errorId: string) => void;
  clearErrors: () => void;
  clearErrorsByCategory: (category: ErrorCategory) => void;
  showNoAccessError: () => void;
  clearNoAccessError: () => void;

  // Convenience methods
  showError: (
    title: string,
    message: string,
    category?: ErrorCategory,
    options?: Partial<AppError>,
  ) => void;
  showNetworkError: (response: Response, url?: string, method?: string) => void;
  showValidationError: (
    message: string,
    field?: string,
    fieldErrors?: Record<string, string[]>,
  ) => void;
  showAuthError: (message?: string, redirectToLogin?: boolean) => void;
  showServerError: (
    statusCode: number,
    message?: string,
    errorCode?: string,
  ) => void;

  // Utility methods
  hasErrors: () => boolean;
  hasErrorsOfCategory: (category: ErrorCategory) => boolean;
  getErrorsByCategory: (category: ErrorCategory) => AppError[];
  getErrorsBySeverity: (severity: AppError["severity"]) => AppError[];
}

export const useErrorStore = create<ErrorState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    errors: [],
    maxErrors: 10, // Prevent memory leaks from too many errors
    hasNoAccessError: false,

    // Add error to the store
    addError: (error: AppError | AnyError) => {
      const normalizedError = error as AppError;

      // Set up auto-hide timer before adding to store to prevent race conditions
      let autoHideTimer: ReturnType<typeof setTimeout> | null = null;
      if (normalizedError.autoHide && normalizedError.autoHideDelay > 0) {
        autoHideTimer = setTimeout(() => {
          get().removeError(normalizedError.id);
        }, normalizedError.autoHideDelay);
      }

      set((state) => {
        // Prevent duplicate errors (same title and message within 1 second)
        const isDuplicate = state.errors.some(
          (existingError) =>
            existingError.title === normalizedError.title &&
            existingError.message === normalizedError.message &&
            Date.now() - existingError.timestamp.getTime() < 1000,
        );

        if (isDuplicate) {
          if (autoHideTimer) {
            clearTimeout(autoHideTimer);
          }
          return state;
        }

        const newErrors = [normalizedError, ...state.errors];

        // Limit the number of errors
        if (newErrors.length > state.maxErrors) {
          newErrors.splice(state.maxErrors);
        }

        return { errors: newErrors };
      });
    },

    removeError: (errorId: string) => {
      set((state) => ({
        errors: state.errors.filter((error) => error.id !== errorId),
      }));
    },

    clearErrors: () => {
      set({ errors: [] });
    },

    clearErrorsByCategory: (category: ErrorCategory) => {
      set((state) => ({
        errors: state.errors.filter((error) => error.category !== category),
      }));
    },

    showError: (
      title: string,
      message: string,
      category: ErrorCategory = "unknown",
      options: Partial<AppError> = {},
    ) => {
      const error = createError(title, message, category, options);
      get().addError(error);
    },

    showNetworkError: (response: Response, url?: string, method?: string) => {
      const { createNetworkError } = require("../utils/errorUtils");
      const error = createNetworkError(response, url, method);
      get().addError(error);
    },

    showValidationError: (
      message: string,
      field?: string,
      fieldErrors?: Record<string, string[]>,
    ) => {
      const { createValidationError } = require("../utils/errorUtils");
      const error = createValidationError(message, field, fieldErrors);
      get().addError(error);
    },

    showAuthError: (message?: string, redirectToLogin = true) => {
      const { createAuthenticationError } = require("../utils/errorUtils");
      const error = createAuthenticationError(message, redirectToLogin);
      get().addError(error);
    },

    showServerError: (
      statusCode: number,
      message?: string,
      errorCode?: string,
    ) => {
      const { createServerError } = require("../utils/errorUtils");
      const error = createServerError(statusCode, message, errorCode);
      get().addError(error);
    },

    showNoAccessError: () => {
      set({ hasNoAccessError: true });
    },

    clearNoAccessError: () => {
      set({ hasNoAccessError: false });
    },

    hasErrors: () => get().errors.length > 0,

    hasErrorsOfCategory: (category: ErrorCategory) =>
      get().errors.some((error) => error.category === category),

    getErrorsByCategory: (category: ErrorCategory) =>
      get().errors.filter((error) => error.category === category),

    getErrorsBySeverity: (severity: AppError["severity"]) =>
      get().errors.filter((error) => error.severity === severity),
  })),
);

// Subscribe to authentication errors and handle redirects
useErrorStore.subscribe(
  (state) => state.errors,
  (errors) => {
    const authErrors = errors.filter(
      (error): error is AuthenticationError =>
        error.category === "authentication" &&
        "redirectToLogin" in error &&
        (error as AuthenticationError).redirectToLogin === true,
    );

    if (authErrors.length > 0) {
      // Handle authentication redirect
      console.warn(
        "Authentication error detected, redirect to login may be needed",
      );
    }
  },
);

// Subscribe to offline/online events (only in production environment)
if (typeof window !== "undefined" && process.env.NODE_ENV === "production") {
  window.addEventListener("online", () => {
    // Clear network errors when coming back online
    useErrorStore.getState().clearErrorsByCategory("network");
  });

  window.addEventListener("offline", () => {
    const { createOfflineError } = require("../utils/errorUtils");
    useErrorStore.getState().addError(createOfflineError());
  });
}

// Export convenience hooks
export const useErrors = () => useErrorStore((state) => state.errors);

// Create stable selectors to prevent infinite re-renders
const actionsSelector = (state: ErrorState) => state.addError;
const removeErrorSelector = (state: ErrorState) => state.removeError;
const clearErrorsSelector = (state: ErrorState) => state.clearErrors;
const clearErrorsByCategorySelector = (state: ErrorState) =>
  state.clearErrorsByCategory;
const showErrorSelector = (state: ErrorState) => state.showError;
const showNetworkErrorSelector = (state: ErrorState) => state.showNetworkError;
const showValidationErrorSelector = (state: ErrorState) =>
  state.showValidationError;
const showAuthErrorSelector = (state: ErrorState) => state.showAuthError;
const showServerErrorSelector = (state: ErrorState) => state.showServerError;
const showNoAccessErrorSelector = (state: ErrorState) =>
  state.showNoAccessError;
const clearNoAccessErrorSelector = (state: ErrorState) =>
  state.clearNoAccessError;

export const useErrorActions = () => ({
  addError: useErrorStore(actionsSelector),
  removeError: useErrorStore(removeErrorSelector),
  clearErrors: useErrorStore(clearErrorsSelector),
  clearErrorsByCategory: useErrorStore(clearErrorsByCategorySelector),
  showError: useErrorStore(showErrorSelector),
  showNetworkError: useErrorStore(showNetworkErrorSelector),
  showValidationError: useErrorStore(showValidationErrorSelector),
  showAuthError: useErrorStore(showAuthErrorSelector),
  showServerError: useErrorStore(showServerErrorSelector),
  showNoAccessError: useErrorStore(showNoAccessErrorSelector),
  clearNoAccessError: useErrorStore(clearNoAccessErrorSelector),
});

export const useErrorUtils = () =>
  useErrorStore((state) => ({
    hasErrors: state.hasErrors,
    hasErrorsOfCategory: state.hasErrorsOfCategory,
    getErrorsByCategory: state.getErrorsByCategory,
    getErrorsBySeverity: state.getErrorsBySeverity,
    hasNoAccessError: state.hasNoAccessError,
  }));
