import type { IntegrationResponse } from "api/ingestion/integrations";
import { create } from "zustand";

interface IntegrationsState {
  // Data
  integrations: IntegrationResponse[];

  // Loading states
  isLoading: boolean;
  isAdding: boolean;
  isUpdating: boolean;

  // Actions
  setIntegrations: (integrations: IntegrationResponse[]) => void;
  setLoading: (value: boolean) => void;
  setAdding: (value: boolean) => void;
  setUpdating: (value: boolean) => void;
}

export const useIntegrationsStore = create<IntegrationsState>((set) => ({
  // Initial state
  integrations: [],
  isLoading: false,
  isAdding: false,
  isUpdating: false,

  // Setters
  setIntegrations: (integrations) => set({ integrations }),
  setLoading: (value) => set({ isLoading: value }),
  setAdding: (value) => set({ isAdding: value }),
  setUpdating: (value) => set({ isUpdating: value }),
}));
