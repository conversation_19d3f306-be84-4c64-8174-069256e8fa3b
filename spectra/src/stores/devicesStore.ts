import type { Thing } from "api/ingestion/things";
import { create } from "zustand";

interface DevicesState {
  // Data
  devices: Thing[];
  deviceTypes: string[];

  // Loading states
  isLoading: boolean;
  isAdding: boolean;
  isUpdating: boolean;

  // Actions
  setDevices: (devices: Thing[]) => void;
  setDeviceTypes: (types: string[]) => void;
  setLoading: (value: boolean) => void;
  setAdding: (value: boolean) => void;
  setUpdating: (value: boolean) => void;
}

export const useDevicesStore = create<DevicesState>((set) => ({
  // Initial state
  devices: [],
  deviceTypes: [],
  isLoading: false,
  isAdding: false,
  isUpdating: false,

  // Setters
  setDevices: (devices) => set({ devices }),
  setDeviceTypes: (types) => set({ deviceTypes: types }),
  setLoading: (value) => set({ isLoading: value }),
  setAdding: (value) => set({ isAdding: value }),
  setUpdating: (value) => set({ isUpdating: value }),
}));
