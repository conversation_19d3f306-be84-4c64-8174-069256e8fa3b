import type { FleetResponse } from "api/ingestion/places";
import { create } from "zustand";

interface FleetsState {
  // Data
  fleets: FleetResponse[];

  // Loading states
  isLoading: boolean;
  isAdding: boolean;
  isUpdating: boolean;

  // Actions
  setFleets: (fleets: FleetResponse[]) => void;
  setLoading: (value: boolean) => void;
  setAdding: (value: boolean) => void;
  setUpdating: (value: boolean) => void;
}

export const useFleetsStore = create<FleetsState>((set) => ({
  // Initial state
  fleets: [],
  isLoading: false,
  isAdding: false,
  isUpdating: false,

  // Setters
  setFleets: (fleets) => set({ fleets }),
  setLoading: (value) => set({ isLoading: value }),
  setAdding: (value) => set({ isAdding: value }),
  setUpdating: (value) => set({ isUpdating: value }),
}));
