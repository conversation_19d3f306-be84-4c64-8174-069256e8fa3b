import type { SiteResponse } from "api/ingestion/places";
import { create } from "zustand";

interface SitesState {
  // Data
  sites: SiteResponse[];

  // Loading states
  isLoading: boolean;
  isAdding: boolean;
  isUpdating: boolean;

  // Actions
  setSites: (sites: SiteResponse[]) => void;
  setLoading: (value: boolean) => void;
  setAdding: (value: boolean) => void;
  setUpdating: (value: boolean) => void;
}

export const useSitesStore = create<SitesState>((set) => ({
  // Initial state
  sites: [],
  isLoading: false,
  isAdding: false,
  isUpdating: false,

  // Setters
  setSites: (sites) => set({ sites }),
  setLoading: (value) => set({ isLoading: value }),
  setAdding: (value) => set({ isAdding: value }),
  setUpdating: (value) => set({ isUpdating: value }),
}));
