import { addons } from '@storybook/manager-api';
import { create } from '@storybook/theming/create';

const theme = create({
  base: 'light',
  brandTitle: 'Spectra UI',
  brandUrl: '/',
  brandImage: './aerovy_logo.png',
  brandTarget: '_self',
  
  // UI colors
  colorPrimary: '#345C9B', // blue50
  colorSecondary: '#2A447C', // blue40
  
  // UI
  appBg: '#F5F7FA', // blue97
  appContentBg: '#FFFFFF',
  appBorderColor: '#D6DEEB', // blue90
  appBorderRadius: 4,
  
  // Typography
  fontBase: '"Inter", sans-serif',
  fontCode: 'monospace',
  
  // Text colors
  textColor: '#1C2F40', // space50
  textInverseColor: '#FFFFFF',
  
  // Toolbar default and active colors
  barTextColor: '#495966', // space60
  barSelectedColor: '#345C9B', // blue50
  barBg: '#FFFFFF',
  
  // Form colors
  inputBg: '#FFFFFF',
  inputBorder: '#E8EAEC', // space90
  inputTextColor: '#1C2F40', // space50
  inputBorderRadius: 4,
});

addons.setConfig({
  theme,
});
