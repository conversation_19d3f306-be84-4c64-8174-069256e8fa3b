import {
  type DatapointMap,
  convertToTimeseries,
  datapointsToMap,
  generateIdealBinSize,
  useDataApi,
} from "api/data";
import { usePlacesApi } from "api/ingestion/places";
import {
  type SimulationResponse,
  useSimulationsApi,
} from "api/ingestion/simulations";
import { useThingsApi } from "api/ingestion/things";
import {
  DeviceDetail,
  EnergyCard,
  NetworkMap,
  SkyportsChargerStatus,
} from "components";
import { HorizontalTimeseriesChart } from "components/charts/horizontalTimeSeries";
import { useSelectedDevice } from "context/SelectedDeviceContext";
import {
  SelectedSimulationProvider,
  SelectedSimulationSelector,
  useSelectedSimulation,
} from "context/SelectedSimulationContext";
import {
  TimeRangeSelector,
  useSelectedTimeRange,
} from "context/SelectedTimeRangeContext";

import { useEffect, useState } from "react";
import { FaEdit } from "react-icons/fa";
import { NavLink, useNavigate, useParams } from "react-router-dom";

import type { Thing } from "api/ingestion/things";
import { BootstrapControls } from "components/BootstrapControls";
import { useAppData } from "hooks/useAppData";
import { PlaceAlerts } from "../../components/alerts/PlaceAlerts";
import { useAuth } from "../../context/AuthContext";
import AddDeviceModal from "../devices/AddDeviceModal";
import { EditFleetModal } from "./EditFleetModal";

interface DeviceGroup {
  [key: string]: Thing[];
}

const Tile = ({ className = "", children }) => {
  return (
    <div
      className={`bg-white rounded-md shadow border border-gray90 ${className}`}
    >
      {children}
    </div>
  );
};

const DeviceDetailWrapper = ({
  onDeviceUpdated,
  onDeviceDeleted,
}: {
  onDeviceUpdated: (device: Thing) => void;
  onDeviceDeleted: (deviceId: string) => void;
}) => {
  const { selectedDevice, setSelectedDevice } = useSelectedDevice();
  console.log("selectedDevice:fleetdetail", selectedDevice);
  return (
    <DeviceDetail
      selectedDevice={selectedDevice}
      setSelectedDevice={setSelectedDevice}
      placeType="fleet"
      onDeviceUpdated={onDeviceUpdated}
      onDeviceDeleted={onDeviceDeleted}
    />
  );
};

const PageContent = ({ fleetId }) => {
  const [fleet, setFleet] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deviceTypes, setDeviceTypes] = useState<string[]>([]);
  const [devices, setDevices] = useState<DeviceGroup>({});
  const [summaryStats, setSummaryStats] = useState<DatapointMap>({});
  const [allBatteryTimeseries, setAllBatteryTimeseries] = useState(null);
  const [isDownloadingAggregateData, setIsDownloadingAggregateData] =
    useState(false);

  const { user, permissions } = useAuth();
  const { getFleetDetail } = usePlacesApi();
  const { getFleetAggregateReport, getTimeseriesForPlace, getSummaryForFleet } =
    useDataApi();
  const { getThingsFromPlace, getThingTypes, addDevice } = useThingsApi();
  const { start, end, isUTC } = useSelectedTimeRange();
  const { simulationId, simulation } = useSelectedSimulation();
  const { editFleet, deleteFleet, isUpdating } = useAppData();
  const navigate = useNavigate();

  const updateSpecificDevice = async (updatedDevice: Thing) => {
    try {
      setDevices((prevDevices) => {
        const newDevices = { ...prevDevices };
        let deviceFound = false;

        for (const deviceType of Object.keys(newDevices)) {
          const deviceIndex = newDevices[deviceType]?.findIndex(
            (device) => device.thingId === updatedDevice.thingId,
          );

          if (deviceIndex !== -1) {
            newDevices[deviceType] = [...newDevices[deviceType]];
            newDevices[deviceType][deviceIndex] = updatedDevice;
            deviceFound = true;
          }
        }

        if (!deviceFound) {
          setTimeout(() => refreshAllDevicesData(), 0);
        }

        return newDevices;
      });
    } catch (error) {
      console.error("Error updating specific device:", error);
      await refreshAllDevicesData();
    }
  };

  const removeDeviceFromState = (deletedDeviceId: string) => {
    setDevices((prevDevices) => {
      const newDevices = { ...prevDevices };

      for (const deviceType of Object.keys(newDevices)) {
        const deviceIndex = newDevices[deviceType]?.findIndex(
          (device) => device.thingId === deletedDeviceId,
        );

        if (deviceIndex !== -1) {
          newDevices[deviceType] = newDevices[deviceType].filter(
            (device) => device.thingId !== deletedDeviceId,
          );
        }
      }

      return newDevices;
    });
  };

  const refreshAllDevicesData = async () => {
    try {
      const devicesData = await getThingsFromPlace("fleet", fleetId);
      const groupedDevices = (devicesData as Thing[]).reduce<
        Record<string, Thing[]>
      >((acc, device) => {
        acc[device.thingType] = (acc[device.thingType] ?? []).concat(device);
        return acc;
      }, {});
      setDevices(groupedDevices);
    } catch (error) {
      console.error("Error refreshing devices data:", error);
    }
  };

  const refreshDevicesData = async () => {
    await refreshAllDevicesData();
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: adding getThingTypes, getSummaryForFleet, getThingsFromPlace to the dependency array causes a re-render loop
  useEffect(() => {
    if (!user) return;

    // Reset all timeseries
    if (simulationId === null) {
      setAllBatteryTimeseries(undefined);
    }

    Promise.all([getFleetDetail(fleetId), getThingTypes()])
      .then(([fleetData, types]) => {
        setFleet(fleetData);
        setDeviceTypes(types);
      })
      .catch(console.error);

    const [binValue, binUnit] = generateIdealBinSize(start, end);
    getTimeseriesForPlace(
      "fleet",
      fleetId,
      start,
      end,
      isUTC,
      binUnit,
      binValue,
      simulationId,
    )
      .then(convertToTimeseries)
      .then(setAllBatteryTimeseries)
      .catch(console.error);

    getThingsFromPlace("fleet", fleetId)
      .then((devices) => {
        const groupedDevices = (devices as Thing[]).reduce<
          Record<string, Thing[]>
        >((acc, device) => {
          acc[device.thingType] = (acc[device.thingType] ?? []).concat(device);
          return acc;
        }, {});
        setDevices(groupedDevices);
      })
      .catch(console.error);

    getSummaryForFleet(fleetId, start, end, simulationId)
      .then(datapointsToMap)
      .then(setSummaryStats)
      .catch(console.error);
  }, [start, end, fleetId, simulationId]);

  const energyCardStats = [
    {
      value: summaryStats.fwd?.value?.toFixed(0) ?? null,
      units: "kWh",
      label: "Drawn from Grid",
      trend: 0,
    },
    {
      value: null,
      units: "kWh",
      label: "Total Battery Capacity",
      trend: 0,
    },
    {
      value: summaryStats.stored?.value?.toFixed(0) ?? null,
      units: "kWh",
      label: "Currently Stored",
      trend: 0,
    },
    {
      value: devices.Battery?.length || 0,
      units: "",
      label: "Number of Batteries",
      trend: 0,
    },
  ];

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);

  const handleDeviceAdded = (result: { success: boolean; error?: string }) => {
    if (result.success) {
      getThingsFromPlace("fleet", fleetId).then((devices) => {
        const groupedDevices = (devices as Thing[]).reduce<
          Record<string, Thing[]>
        >((acc, device) => {
          acc[device.thingType] = (acc[device.thingType] ?? []).concat(device);
          return acc;
        }, {});
        setDevices(groupedDevices);
      });
      return { success: true };
    }
    return { success: false, error: result.error };
  };

  const handleDownloadAggregateReport = async () => {
    setIsDownloadingAggregateData(true);
    try {
      await getFleetAggregateReport(fleetId, start, end, "h", 1, simulationId);
    } catch (error) {
      alert(`Download failed: ${error}`);
      console.error("Download failed:", error);
    } finally {
      setIsDownloadingAggregateData(false);
    }
  };

  const handleSaveFleet = async (formData) => {
    const result = await editFleet(fleetId, formData);
    if (result.success) {
      setFleet({ ...fleet, ...formData });
      return true;
    }
    console.error("Failed to update fleet:", result.error);
    return false;
  };

  const handleDeleteFleet = async () => {
    try {
      const result = await deleteFleet(fleetId);
      if (result.success) {
        navigate("/fleets");
      } else {
        console.error("Failed to delete fleet:", result.error);
        alert("Failed to delete fleet. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting fleet:", error);
      alert("An error occurred while deleting the fleet. Please try again.");
    }
  };

  const renderActionButtons = () => {
    if (permissions.includes("write:ingest_things")) {
      return (
        <div className="flex items-center gap-2">
          <button
            type="button"
            onClick={() => setIsEditModalOpen(true)}
            className="px-3.5 py-2 rounded-full justify-end items-center gap-1 cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue90 focus:ring-blue90 flex border border-space80 hover:bg-gray95 text-space70"
          >
            <div className="text-xs">
              <FaEdit />
            </div>
            <div className="text-xs font-medium leading-[14px]">Edit Fleet</div>
          </button>
          <button
            type="button"
            onClick={handleOpenModal}
            className="px-3.5 py-2 rounded-full justify-end items-center gap-1 cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue90 focus:ring-blue90 flex border border-space80 hover:bg-gray95 text-space70"
          >
            <div className="text-xs font-medium leading-[14px]">
              + Add Device
            </div>
          </button>
        </div>
      );
    }
    return null;
  };

  if (!fleet) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex h-full">
      <div className="grow h-screen overflow-y-scroll">
        <div className="flex pt-4 pb-3 justify-between">
          <div className="text-heading1 text-space50 flex items-center">
            <div className="flex items-center">
              <NavLink to="/fleets">Fleets</NavLink>
              <span className="mx-1">/</span>
              <span>{fleet.fleetName}</span>
            </div>
            <div className="ml-4">{renderActionButtons()}</div>
          </div>
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={handleDownloadAggregateReport}
              disabled={isDownloadingAggregateData}
              className="px-3.5 py-2 rounded-full justify-end items-center gap-1 cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue90 focus:ring-blue90 flex border border-space80 hover:bg-gray95 text-space70"
            >
              <div className="text-xs font-medium leading-[14px]">
                {isDownloadingAggregateData
                  ? "Downloading..."
                  : "Download Aggregate Report"}
              </div>
            </button>
            <SelectedSimulationSelector />
            <TimeRangeSelector />
          </div>
        </div>
        <hr />
        <div className="my-4">
          <h2 className="text-xl font-semibold mb-2">{fleet.fleetName}</h2>
          {fleet.attributes && Object.keys(fleet.attributes).length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {Object.entries(fleet.attributes).map(([key, value]) => (
                <span
                  key={key}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {key}: {String(value)}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">No attributes defined</p>
          )}
        </div>

        {simulationId !== null && (
          <div className="my-4 bg-blue90 p-4 rounded-md">
            <p className="font-bold">Current Simulation: {simulationId}</p>
            <p>
              {simulation.startDate.toLocaleString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
                hour: "numeric",
                minute: "numeric",
              })}
              {" - "}
              {simulation.endDate?.toLocaleString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
                hour: "numeric",
                minute: "numeric",
              })}
            </p>
          </div>
        )}

        <div className="flex space-x-4 mb-4 h-[300px]">
          <Tile className="w-1/3 p-4">
            <PlaceAlerts
              placeType={"fleet"}
              placeId={fleetId}
              partnerId={user?.partnerId}
              start={start}
              end={end}
            />
          </Tile>
          <Tile className="w-2/3">
            <NetworkMap place={fleet} devices={devices} />
          </Tile>
        </div>

        <div className="flex space-x-4 mb-4">
          <Tile className="w-full">
            <HorizontalTimeseriesChart timeseries={allBatteryTimeseries} />
          </Tile>
        </div>

        <div className="flex space-x-4 mb-4 h-[200px]">
          <Tile className="w-full">
            <EnergyCard energyCardStats={energyCardStats} />
          </Tile>
        </div>

        <div className="flex space-x-4 mb-4">
          <Tile className="w-full">
            <SkyportsChargerStatus devices={devices} />
          </Tile>
        </div>

        <div className="flex space-x-4 mb-4">
          <BootstrapControls placeType="fleet" placeId={fleet.fleetId} />
        </div>
      </div>

      <EditFleetModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        fleet={fleet}
        onSave={handleSaveFleet}
        onDelete={handleDeleteFleet}
        isUpdating={isUpdating.fleet}
      />

      <AddDeviceModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onDeviceAdded={handleDeviceAdded}
      />

      <DeviceDetailWrapper
        onDeviceUpdated={updateSpecificDevice}
        onDeviceDeleted={removeDeviceFromState}
      />
    </div>
  );
};

const FleetDetail = () => {
  const { fleetId } = useParams();
  const [simulations, setSimulations] = useState<SimulationResponse[]>([]);
  const { getSimulationsForPlace } = useSimulationsApi();
  const { setSelectedDevice } = useSelectedDevice();

  useEffect(() => {
    setSelectedDevice(null);
  }, [setSelectedDevice]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: adding getSimulationsForPlace to the dependency array causes a re-render loop
  useEffect(() => {
    getSimulationsForPlace(fleetId, "fleet").then(setSimulations);
  }, [fleetId]);

  return (
    <SelectedSimulationProvider simulations={simulations}>
      <PageContent fleetId={fleetId} />
    </SelectedSimulationProvider>
  );
};

export default FleetDetail;
