/** @type { import('@storybook/react-webpack5').StorybookConfig } */
const path = require('path');

const config = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-a11y',
    '@storybook/addon-viewport',
    '@storybook/addon-themes',
    '@storybook/preset-create-react-app',
  ],
  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
  staticDirs: ['../public'],
  typescript: {
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      compilerOptions: {
        allowSyntheticDefaultImports: false,
        esModuleInterop: false,
      },
      propFilter: (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
    },
  },
  webpackFinal: async (config) => {
    // Add support for path aliases
    config.resolve.alias = {
      ...config.resolve.alias,
      components: path.resolve(__dirname, '../src/components'),
      context: path.resolve(__dirname, '../src/context'),
      images: path.resolve(__dirname, '../src/images'),
      utils: path.resolve(__dirname, '../src/utils'),
      api: path.resolve(__dirname, '../src/api'),
      pages: path.resolve(__dirname, '../src/pages'),
      stores: path.resolve(__dirname, '../src/stores'),
    };

    // Find the rule that handles CSS files
    const cssRule = config.module.rules.find(
      (rule) => rule.test && rule.test.toString().includes('css')
    );

    // If found, modify it to use PostCSS with Tailwind
    if (cssRule) {
      // Make sure we're not modifying the original rule
      const newRule = { ...cssRule };

      // Find the PostCSS loader if it exists
      const postCssLoader = newRule.use && newRule.use.find(
        (loader) => loader.loader && loader.loader.includes('postcss-loader')
      );

      // If PostCSS loader exists, configure it to use our tailwind config
      if (postCssLoader) {
        postCssLoader.options = {
          ...postCssLoader.options,
          postcssOptions: {
            plugins: [
              require('tailwindcss'),
              require('autoprefixer'),
            ],
          },
        };
      }
    }

    return config;
  },
};
export default config;
