# Storybook for Spectra UI

This project uses Storybook to document and develop UI components in isolation.

## Getting Started

To run Storybook locally:

```bash
bun storybook
```

This will start Storybook on port 6006. Open your browser and navigate to http://localhost:6006 to view the component library.

To build a static version of Storybook:

```bash
bun build-storybook
```

## Creating Stories

Stories are located alongside their components with the `.stories.tsx` extension. For example:

- `src/components/uikit/button.tsx` - Component file
- `src/components/uikit/button.stories.tsx` - Stories for the Button component

### Basic Story Structure

```tsx
import type { Meta, StoryObj } from '@storybook/react';
import YourComponent from './YourComponent';

const meta = {
  title: 'Category/YourComponent',
  component: YourComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof YourComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // Component props go here
  },
};
```

## Project Structure

- `.storybook/` - Storybook configuration
  - `main.js` - Main configuration file
  - `preview.js` - Preview configuration and global decorators
  - `manager.js` - UI customization

## Tailwind CSS Integration

Storybook is configured to work with Tailwind CSS. You can use Tailwind classes in your stories just like in your regular components.

## Available Addons

The following addons are available in this Storybook setup:

- **Essentials**: Includes Docs, Controls, Actions, Viewport, Backgrounds, and Toolbars
- **Links**: For linking between stories
- **Interactions**: For testing component interactions
- **A11y**: For accessibility testing
- **Viewport**: For testing responsive designs
- **Styling**: For working with CSS, including Tailwind CSS

## Best Practices

1. **Component Documentation**: Use the `autodocs` tag to automatically generate documentation for your components.

2. **Variants**: Create multiple stories for different states and variants of your components.

3. **Responsive Design**: Use the Viewport addon to test your components at different screen sizes.

4. **Accessibility**: Use the A11y addon to check for accessibility issues.

5. **Interactive Examples**: Use the Interactions addon to create interactive examples of your components.

## Troubleshooting

If you encounter issues with Storybook:

1. Make sure all dependencies are installed: `bun install`
2. Check for errors in the console
3. Verify that your component imports are correct
4. Ensure that your component doesn't rely on global context that isn't available in Storybook

## Resources

- [Storybook Documentation](https://storybook.js.org/docs/react/get-started/introduction)
- [Component-Driven Development](https://www.componentdriven.org/)
- [Storybook Addons](https://storybook.js.org/addons/)
